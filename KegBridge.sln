
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34728.123
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "KegBridgeCore", "KegBridgeCore\KegBridgeCore.csproj", "{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "KegBridgeCoreTests", "KegBridgeCoreTests\KegBridgeCoreTests.csproj", "{D4754947-7BD3-45F4-A333-C579FCD8F669}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{99628B55-C338-4502-A432-85AE71FFDD43}"
	ProjectSection(SolutionItems) = preProject
		KegBridgeCore\appsettings-belhaven.json = KegBridgeCore\appsettings-belhaven.json
		docker-compose.yml = docker-compose.yml
		grafana_etc\grafana.ini = grafana_etc\grafana.ini
		KegBridgeCore\keg_bridge-belhaven.json = KegBridgeCore\keg_bridge-belhaven.json
		KegBridgeCore\keg_bridge-development.json = KegBridgeCore\keg_bridge-development.json
		KegBridgeCore\keg_bridge-haacht.json = KegBridgeCore\keg_bridge-haacht.json
		KegBridgeCore\keg_bridge-halvemaan.json = KegBridgeCore\keg_bridge-halvemaan.json
		KegBridgeCore\keg_bridge-lindemans.json = KegBridgeCore\keg_bridge-lindemans.json
		KegBridgeCore\keg_bridge-moortgat-vl2.json = KegBridgeCore\keg_bridge-moortgat-vl2.json
		KegBridgeCore\keg_bridge-pietra.json = KegBridgeCore\keg_bridge-pietra.json
		compose.yaml = compose.yaml
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpcUAPlayground", "OpcUAPlayground\OpcUAPlayground.csproj", "{50F2BB85-6416-49D2-BA5C-9D4E26286028}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KegInsight", "KegInsight\KegInsight.csproj", "{23250B90-96D5-4859-AA06-FE43862CC15C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KegBridgeCoreExamples", "KegBridgeCoreExamples\KegBridgeCoreExamples.csproj", "{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Debug|x64.Build.0 = Debug|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Debug|x86.Build.0 = Debug|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Release|Any CPU.Build.0 = Release|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Release|x64.ActiveCfg = Release|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Release|x64.Build.0 = Release|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Release|x86.ActiveCfg = Release|Any CPU
		{92B6229F-3BAF-4DB7-A8EC-9E2EF52FC51D}.Release|x86.Build.0 = Release|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Debug|x64.Build.0 = Debug|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Debug|x86.Build.0 = Debug|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Release|x64.ActiveCfg = Release|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Release|x64.Build.0 = Release|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Release|x86.ActiveCfg = Release|Any CPU
		{D4754947-7BD3-45F4-A333-C579FCD8F669}.Release|x86.Build.0 = Release|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Debug|x64.ActiveCfg = Debug|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Debug|x64.Build.0 = Debug|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Debug|x86.ActiveCfg = Debug|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Debug|x86.Build.0 = Debug|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Release|Any CPU.Build.0 = Release|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Release|x64.ActiveCfg = Release|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Release|x64.Build.0 = Release|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Release|x86.ActiveCfg = Release|Any CPU
		{50F2BB85-6416-49D2-BA5C-9D4E26286028}.Release|x86.Build.0 = Release|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Debug|x64.Build.0 = Debug|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Debug|x86.Build.0 = Debug|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Release|Any CPU.Build.0 = Release|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Release|x64.ActiveCfg = Release|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Release|x64.Build.0 = Release|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Release|x86.ActiveCfg = Release|Any CPU
		{23250B90-96D5-4859-AA06-FE43862CC15C}.Release|x86.Build.0 = Release|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Debug|x64.Build.0 = Debug|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Debug|x86.Build.0 = Debug|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Release|x64.ActiveCfg = Release|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Release|x64.Build.0 = Release|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Release|x86.ActiveCfg = Release|Any CPU
		{BA0EB168-6C10-432E-ABCC-F91D81BFF68A}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {30CAD327-0A4A-487B-A1DC-8974401B1F8D}
	EndGlobalSection
EndGlobal
