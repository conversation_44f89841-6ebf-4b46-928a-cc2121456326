# KegBridge Core Examples

This project contains comprehensive examples demonstrating the features of KegBridgeCore, particularly the new dot notation functionality for `NodeEvent` and `NodeEventData` classes.

## Overview

The examples showcase how to use the immutable `NodeEvent` class as messages in data processing pipelines, particularly with Akka.NET actors. The dot notation functionality allows for intuitive access to nested data structures while maintaining immutability guarantees.

## Examples Included

### 1. DotNotationExample.cs
Demonstrates the core dot notation functionality:
- Reading nested values with `GetPath()`
- Type-safe access with `TryGetPath<T>()`
- Setting single values with `SetPath()`
- Setting multiple values efficiently with `SetPaths()`
- Working with complex nested data structures
- Immutability guarantees

### 2. DataProcessingPipelineExample.cs
Shows real-world usage in a data processing pipeline:
- Creating NodeEvents from raw data
- Enriching events with metadata
- Transforming events for different downstream systems
- Error handling with safe path access
- Maintaining immutability throughout the pipeline

### 3. AkkaActorExample.cs
Conceptual example of NodeEvent usage in Akka.NET:
- Passing immutable messages between actors
- Data validation and enrichment actors
- Business logic processing
- Routing to specialized actors
- Message transformation patterns

### 4. NCalcExpressionExample.cs
Comprehensive demonstration of NCalc expression evaluation:
- Basic property access with dot notation
- Mathematical calculations and formulas
- String manipulation functions
- Conditional logic and business rules
- Null handling and type coercion
- Date/time operations
- Custom functions for NodeEvent/NodeEventData

### 5. TransformNodeExample.cs
Real-world TransformNode configuration examples:
- Environmental data processing with calculations
- Alert generation based on conditions
- Data enrichment and categorization
- Complex business rule implementation
- Practical transformation scenarios

## Key Features Demonstrated

### Dot Notation Access
```csharp
// Reading nested values
var userName = nodeEvent.GetPath("user.profile.name");
var theme = nodeEvent.GetPath("user.profile.preferences.theme");

// Type-safe access
if (nodeEvent.TryGetPath<bool>("user.profile.preferences.notifications", out var notifications))
{
    Console.WriteLine($"Notifications: {notifications}");
}
```

### Efficient Bulk Updates
```csharp
// Setting multiple paths at once
var updated = nodeEvent.SetPaths(new[]
{
    new KeyValuePair<string, object?>("api.config.timeout", 30000),
    new KeyValuePair<string, object?>("api.config.retries", 3),
    new KeyValuePair<string, object?>("api.endpoints.users", "/api/v1/users")
});

// Using anonymous objects
var configured = nodeEvent.SetPaths(new
{
    DatabaseHost = "db.example.com",
    DatabasePort = 5432,
    CacheEnabled = true
});
```

### NCalc Expression Evaluation
```csharp
// NodeEvent properties (direct access)
"node"           // Access NodeEvent.Node
"topic"          // Access NodeEvent.Topic
"timestamp"      // Access NodeEvent.Timestamp
"id"             // Access NodeEvent.Id

// Data properties (bracket notation required)
"[data.order.amount] * [data.order.tax_rate]"
"round([data.user.salary] / 12, 2)"

// String manipulation
"upper([data.user.name])"
"concat([data.user.first_name], ' ', [data.user.last_name])"
"substring([data.email], 0, indexOf([data.email], '@'))"

// Conditional logic
"[data.user.age] >= 18 ? 'Adult' : 'Minor'"
"[data.order.amount] > 1000 ? 'Premium' : 'Standard'"

// Combining NodeEvent properties with data
"concat('Node: ', node, ', User: ', [data.user.name])"
"topic == 'user.login' && [data.user.active]"

// Null handling
"nvl([data.user.bonus], 0)"
"coalesce([data.user.nickname], [data.user.first_name])"

// Date/time operations
"formatDate(now(), 'yyyy-MM-dd')"
"formatDate(timestamp, 'yyyy-MM-dd HH:mm:ss')"
"addDays([data.created_date], 30)"

// Custom NodeEvent functions
"getPath(data, 'user.profile.department')"
"hasPath(data, 'user.preferences.theme')"
```

### Immutable Message Passing
```csharp
// Each transformation creates a new immutable instance
var enrichedMessage = originalMessage
    .WithNode("EnrichmentActor")
    .WithTopic("data.enriched")
    .SetPaths(enrichmentData);
```

## Running the Examples

To run all examples:

```bash
cd KegBridgeCoreExamples
dotnet run
```

This will execute all three examples in sequence, demonstrating the progression from basic functionality to real-world usage patterns.

## Benefits for Data Processing Pipelines

1. **Immutability**: All NodeEvent instances are immutable, preventing accidental mutations
2. **Deep Freezing**: Nested collections are automatically converted to immutable types
3. **Type Safety**: Generic methods provide compile-time type checking
4. **Performance**: Efficient bulk operations with `SetPaths()`
5. **Intuitive API**: Dot notation makes nested data access natural
6. **Actor-Friendly**: Perfect for Akka.NET message passing patterns

## Use Cases

- **Event Sourcing**: Immutable events in event stores
- **Data Pipelines**: Transforming data through multiple processing stages
- **Microservices**: Passing structured data between services
- **Analytics**: Enriching events with metadata for analysis
- **Audit Trails**: Maintaining immutable records of data transformations

## Dependencies

- KegBridgeCore: Core library containing NodeEvent and NodeEventData classes
- System.Collections.Immutable: For immutable collection types
- .NET 8.0: Target framework

## Architecture Notes

The examples demonstrate a typical data processing architecture where:
1. Raw data is ingested and wrapped in NodeEvent messages
2. Data flows through validation, enrichment, and business logic actors
3. Processed data is routed to specialized output actors
4. Each stage maintains immutability while adding/transforming data
5. The dot notation API makes complex nested data manipulation simple and readable

This pattern is particularly effective for building robust, scalable data processing systems where data integrity and traceability are critical.
