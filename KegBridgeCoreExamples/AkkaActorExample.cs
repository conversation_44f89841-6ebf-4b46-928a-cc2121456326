using System;
using System.Collections.Generic;
using KegBridgeCore.Data;

namespace KegBridgeCoreExamples
{
    /// <summary>
    /// Example demonstrating how NodeEvent would be used in Akka.NET actors
    /// Note: This is a conceptual example showing the message patterns, not actual Akka.NET code
    /// </summary>
    public class AkkaActorExample
    {
        public static void RunExample()
        {
            Console.WriteLine("=== Akka.NET Actor Message Example ===\n");

            Console.WriteLine("This example shows how NodeEvent would be used as immutable messages between actors");
            Console.WriteLine("in a data processing pipeline.\n");

            // Simulate messages that would be passed between actors
            Console.WriteLine("1. Raw Data Ingestion Actor -> Data Validation Actor:");
            
            var rawDataMessage = NodeEvent.FromDictionary("DataIngestionActor", new Dictionary<string, object?>
            {
                ["source"] = "api_endpoint",
                ["timestamp"] = DateTime.UtcNow,
                ["payload"] = new Dictionary<string, object?>
                {
                    ["user_id"] = "12345",
                    ["action"] = "purchase",
                    ["product_id"] = "PROD_001",
                    ["amount"] = 99.99,
                    ["currency"] = "USD"
                }
            }, "data.ingested");

            Console.WriteLine($"   Message from: {rawDataMessage.Node}");
            Console.WriteLine($"   Topic: {rawDataMessage.Topic}");
            Console.WriteLine($"   User ID: {rawDataMessage.GetPath("payload.user_id")}");
            Console.WriteLine($"   Action: {rawDataMessage.GetPath("payload.action")}");
            Console.WriteLine($"   Amount: ${rawDataMessage.GetPath("payload.amount")}");

            Console.WriteLine("\n2. Data Validation Actor -> Data Enrichment Actor:");
            
            // Validation actor adds validation results
            var validatedMessage = rawDataMessage
                .WithNode("DataValidationActor")
                .WithTopic("data.validated")
                .SetPaths(new[]
                {
                    new KeyValuePair<string, object?>("validation.user_id_valid", true),
                    new KeyValuePair<string, object?>("validation.amount_valid", true),
                    new KeyValuePair<string, object?>("validation.currency_valid", true),
                    new KeyValuePair<string, object?>("validation.product_exists", true),
                    new KeyValuePair<string, object?>("validation.overall_status", "VALID"),
                    new KeyValuePair<string, object?>("validation.validated_at", DateTime.UtcNow)
                });

            Console.WriteLine($"   Message from: {validatedMessage.Node}");
            Console.WriteLine($"   Topic: {validatedMessage.Topic}");
            Console.WriteLine($"   Validation Status: {validatedMessage.GetPath("validation.overall_status")}");
            Console.WriteLine($"   User ID Valid: {validatedMessage.GetPath("validation.user_id_valid")}");
            Console.WriteLine($"   Product Exists: {validatedMessage.GetPath("validation.product_exists")}");

            Console.WriteLine("\n3. Data Enrichment Actor -> Business Logic Actor:");
            
            // Enrichment actor adds external data
            var enrichedMessage = validatedMessage
                .WithNode("DataEnrichmentActor")
                .WithTopic("data.enriched")
                .SetPaths(new
                {
                    UserProfile = new Dictionary<string, object?>
                    {
                        ["name"] = "John Doe",
                        ["email"] = "<EMAIL>",
                        ["tier"] = "premium",
                        ["lifetime_value"] = 2500.00
                    },
                    ProductDetails = new Dictionary<string, object?>
                    {
                        ["name"] = "Premium Software License",
                        ["category"] = "software",
                        ["cost"] = 45.00,
                        ["margin"] = 54.99
                    },
                    GeoData = new Dictionary<string, object?>
                    {
                        ["country"] = "US",
                        ["state"] = "CA",
                        ["timezone"] = "America/Los_Angeles"
                    },
                    EnrichmentTimestamp = DateTime.UtcNow
                });

            Console.WriteLine($"   Message from: {enrichedMessage.Node}");
            Console.WriteLine($"   Topic: {enrichedMessage.Topic}");
            Console.WriteLine($"   User Name: {enrichedMessage.GetPath("UserProfile.name")}");
            Console.WriteLine($"   User Tier: {enrichedMessage.GetPath("UserProfile.tier")}");
            Console.WriteLine($"   Product Name: {enrichedMessage.GetPath("ProductDetails.name")}");
            Console.WriteLine($"   User Country: {enrichedMessage.GetPath("GeoData.country")}");

            Console.WriteLine("\n4. Business Logic Actor -> Multiple Output Actors:");
            
            // Business logic actor processes and routes to multiple destinations
            var processedMessage = enrichedMessage
                .WithNode("BusinessLogicActor")
                .WithTopic("transaction.processed")
                .SetPaths(new[]
                {
                    new KeyValuePair<string, object?>("business.discount_applied", 10.00),
                    new KeyValuePair<string, object?>("business.final_amount", 89.99),
                    new KeyValuePair<string, object?>("business.commission", 4.50),
                    new KeyValuePair<string, object?>("business.tax_amount", 7.20),
                    new KeyValuePair<string, object?>("business.processing_fee", 2.99),
                    new KeyValuePair<string, object?>("business.net_revenue", 75.30),
                    new KeyValuePair<string, object?>("business.loyalty_points_earned", 90),
                    new KeyValuePair<string, object?>("business.next_tier_progress", 0.15),
                    new KeyValuePair<string, object?>("routing.send_to_payment", true),
                    new KeyValuePair<string, object?>("routing.send_to_analytics", true),
                    new KeyValuePair<string, object?>("routing.send_to_notification", true),
                    new KeyValuePair<string, object?>("routing.send_to_audit", true)
                });

            Console.WriteLine($"   Message from: {processedMessage.Node}");
            Console.WriteLine($"   Topic: {processedMessage.Topic}");
            Console.WriteLine($"   Final Amount: ${processedMessage.GetPath("business.final_amount")}");
            Console.WriteLine($"   Discount Applied: ${processedMessage.GetPath("business.discount_applied")}");
            Console.WriteLine($"   Net Revenue: ${processedMessage.GetPath("business.net_revenue")}");
            Console.WriteLine($"   Loyalty Points: {processedMessage.GetPath("business.loyalty_points_earned")}");

            Console.WriteLine("\n5. Routing to specialized actors:");

            // Payment Actor Message
            var paymentMessage = processedMessage
                .WithNode("PaymentActor")
                .WithTopic("payment.requested")
                .SetPaths(new
                {
                    PaymentAmount = processedMessage.GetPath("business.final_amount"),
                    PaymentMethod = "credit_card",
                    PaymentProcessor = "stripe",
                    PaymentReference = Guid.NewGuid().ToString(),
                    PaymentDueDate = DateTime.UtcNow.AddDays(1)
                });

            // Analytics Actor Message  
            var analyticsMessage = processedMessage
                .WithNode("AnalyticsActor")
                .WithTopic("analytics.transaction")
                .SetPaths(new[]
                {
                    new KeyValuePair<string, object?>("analytics.event_type", "purchase_completed"),
                    new KeyValuePair<string, object?>("analytics.user_segment", processedMessage.GetPath("UserProfile.tier")),
                    new KeyValuePair<string, object?>("analytics.product_category", processedMessage.GetPath("ProductDetails.category")),
                    new KeyValuePair<string, object?>("analytics.revenue", processedMessage.GetPath("business.net_revenue")),
                    new KeyValuePair<string, object?>("analytics.discount_rate", 0.10),
                    new KeyValuePair<string, object?>("analytics.customer_ltv", processedMessage.GetPath("UserProfile.lifetime_value")),
                    new KeyValuePair<string, object?>("analytics.geo_region", processedMessage.GetPath("GeoData.country"))
                });

            Console.WriteLine($"\n   Payment Message: {paymentMessage.Node} - {paymentMessage.Topic}");
            Console.WriteLine($"     Payment Amount: ${paymentMessage.GetPath("PaymentAmount")}");
            Console.WriteLine($"     Payment Reference: {paymentMessage.GetPath("PaymentReference")}");

            Console.WriteLine($"\n   Analytics Message: {analyticsMessage.Node} - {analyticsMessage.Topic}");
            Console.WriteLine($"     Event Type: {analyticsMessage.GetPath("analytics.event_type")}");
            Console.WriteLine($"     User Segment: {analyticsMessage.GetPath("analytics.user_segment")}");
            Console.WriteLine($"     Revenue: ${analyticsMessage.GetPath("analytics.revenue")}");

            Console.WriteLine("\n6. Demonstrating message immutability:");
            Console.WriteLine("   Each actor receives and sends immutable NodeEvent messages");
            Console.WriteLine("   Original data is preserved throughout the pipeline:");
            Console.WriteLine($"     Original amount: ${rawDataMessage.GetPath("payload.amount")}");
            Console.WriteLine($"     Final amount: ${processedMessage.GetPath("business.final_amount")}");
            Console.WriteLine($"     Payment amount: ${paymentMessage.GetPath("PaymentAmount")}");
            Console.WriteLine("   Each transformation creates new immutable instances");

            Console.WriteLine("\n=== Akka.NET Actor Message Example Complete ===");
        }
    }
}
