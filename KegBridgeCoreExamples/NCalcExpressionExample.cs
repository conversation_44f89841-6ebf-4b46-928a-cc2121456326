using System;
using System.Collections.Generic;
using KegBridgeCore.Data;
using KegBridgeCore.Services;

namespace KegBridgeCoreExamples
{
    /// <summary>
    /// Example demonstrating NCalc expression evaluation with NodeEvent and dot notation
    /// </summary>
    public class NCalcExpressionExample
    {
        public static void RunExample()
        {
            Console.WriteLine("=== NCalc Expression Evaluation with NodeEvent Example ===\n");

            // Create a sample NodeEvent with complex nested data
            var sampleData = new Dictionary<string, object?>
            {
                ["user"] = new Dictionary<string, object?>
                {
                    ["id"] = 12345,
                    ["name"] = "John Doe",
                    ["email"] = "<EMAIL>",
                    ["profile"] = new Dictionary<string, object?>
                    {
                        ["age"] = 30,
                        ["department"] = "Engineering",
                        ["salary"] = 75000.50,
                        ["active"] = true,
                        ["skills"] = new[] { "C#", "JavaScript", "SQL" }
                    }
                },
                ["order"] = new Dictionary<string, object?>
                {
                    ["id"] = "ORD-001",
                    ["amount"] = 1250.75,
                    ["tax_rate"] = 0.08,
                    ["items"] = new[]
                    {
                        new Dictionary<string, object?> { ["name"] = "Laptop", ["price"] = 999.99, ["qty"] = 1 },
                        new Dictionary<string, object?> { ["name"] = "Mouse", ["price"] = 25.50, ["qty"] = 2 }
                    }
                },
                ["metadata"] = new Dictionary<string, object?>
                {
                    ["timestamp"] = DateTime.UtcNow,
                    ["source"] = "web_api",
                    ["version"] = "2.1.0"
                }
            };

            var nodeEvent = NodeEvent.FromDictionary("TransformNode", sampleData, "user.order.created");

            Console.WriteLine("1. NodeEvent properties (direct access):");
            EvaluateAndPrint("node", nodeEvent);
            EvaluateAndPrint("topic", nodeEvent);
            EvaluateAndPrint("timestamp", nodeEvent);
            EvaluateAndPrint("id", nodeEvent);

            Console.WriteLine("\n2. Data properties (bracket notation):");
            EvaluateAndPrint("[data.user.name]", nodeEvent);
            EvaluateAndPrint("[data.user.profile.age]", nodeEvent);
            EvaluateAndPrint("[data.order.amount]", nodeEvent);
            EvaluateAndPrint("[data.metadata.source]", nodeEvent);

            Console.WriteLine("\n3. Mathematical calculations:");
            EvaluateAndPrint("[data.order.amount] * [data.order.tax_rate]", nodeEvent);
            EvaluateAndPrint("[data.order.amount] + ([data.order.amount] * [data.order.tax_rate])", nodeEvent);
            EvaluateAndPrint("[data.user.profile.salary] / 12", nodeEvent);
            EvaluateAndPrint("round([data.user.profile.salary] / 12, 2)", nodeEvent);

            Console.WriteLine("\n4. String manipulation:");
            EvaluateAndPrint("upper([data.user.name])", nodeEvent);
            EvaluateAndPrint("concat([data.user.name], ' - ', [data.user.profile.department])", nodeEvent);
            EvaluateAndPrint("substring([data.user.email], 0, indexOf([data.user.email], '@'))", nodeEvent);
            EvaluateAndPrint("replace([data.order.id], 'ORD-', 'ORDER-')", nodeEvent);

            Console.WriteLine("\n5. Conditional logic:");
            EvaluateAndPrint("[data.user.profile.age] >= 18", nodeEvent);
            EvaluateAndPrint("[data.user.profile.salary] > 50000 ? 'High' : 'Standard'", nodeEvent);
            EvaluateAndPrint("[data.user.profile.active] && [data.user.profile.age] > 25", nodeEvent);
            EvaluateAndPrint("[data.order.amount] > 1000 ? 'Premium' : 'Standard'", nodeEvent);

            Console.WriteLine("\n6. Combining NodeEvent properties with data:");
            EvaluateAndPrint("concat('Node: ', node, ', Topic: ', topic)", nodeEvent);
            EvaluateAndPrint("concat('User: ', [data.user.name], ' on node ', node)", nodeEvent);
            EvaluateAndPrint("topic == 'user.order.created' && [data.order.amount] > 1000", nodeEvent);

            Console.WriteLine("\n7. Null handling and coalescing:");
            EvaluateAndPrint("nvl([data.user.profile.bonus], 0)", nodeEvent);
            EvaluateAndPrint("coalesce([data.user.profile.bonus], [data.user.profile.salary] * 0.1)", nodeEvent);
            EvaluateAndPrint("isnull([data.user.profile.bonus])", nodeEvent);
            EvaluateAndPrint("nzs([data.user.profile.nickname], [data.user.name])", nodeEvent);

            Console.WriteLine("\n8. Using new dot notation functions:");
            EvaluateAndPrint("getPath(data, 'user.profile.department')", nodeEvent);
            EvaluateAndPrint("hasPath(data, 'user.profile.bonus')", nodeEvent);
            EvaluateAndPrint("hasPath(data, 'user.profile.salary')", nodeEvent);

            Console.WriteLine("\n9. Date/Time functions:");
            EvaluateAndPrint("formatDate(now(), 'yyyy-MM-dd HH:mm:ss')", nodeEvent);
            EvaluateAndPrint("addDays(today(), 30)", nodeEvent);
            EvaluateAndPrint("addHours([data.metadata.timestamp], 24)", nodeEvent);
            EvaluateAndPrint("formatDate(timestamp, 'yyyy-MM-dd HH:mm:ss')", nodeEvent);

            Console.WriteLine("\n10. Complex business logic examples:");
            
            // Calculate employee bonus based on salary and performance
            var bonusExpression = @"
                [data.user.profile.salary] > 70000 && [data.user.profile.active] ?
                    [data.user.profile.salary] * 0.15 :
                    [data.user.profile.salary] * 0.10";
            EvaluateAndPrint(bonusExpression.Replace("\n", "").Replace("                ", ""), nodeEvent);

            // Determine shipping cost based on order amount
            var shippingExpression = @"
                [data.order.amount] > 1000 ? 0 : ([data.order.amount] > 500 ? 9.99 : 19.99)";
            EvaluateAndPrint(shippingExpression.Replace("\n", "").Replace("                ", ""), nodeEvent);

            // Create a risk score
            var riskExpression = @"
                ([data.user.profile.age] < 25 ? 0.2 : 0) +
                ([data.order.amount] > 2000 ? 0.3 : 0) +
                ([data.user.profile.active] ? 0 : 0.5)";
            EvaluateAndPrint(riskExpression.Replace("\n", "").Replace("                ", ""), nodeEvent);

            Console.WriteLine("\n11. Demonstrating expression-based transformations:");
            
            // Show how expressions could be used in TransformNode configuration
            Console.WriteLine("   Example TransformNode Add rules:");
            Console.WriteLine("   Key: 'calculated.total_with_tax'");
            Console.WriteLine("   Value: '[data.order.amount] + ([data.order.amount] * [data.order.tax_rate])'");
            Console.WriteLine("   Result: " + EvaluateExpression("[data.order.amount] + ([data.order.amount] * [data.order.tax_rate])", nodeEvent));

            Console.WriteLine("\n   Key: 'user.display_name'");
            Console.WriteLine("   Value: 'concat([data.user.name], ' (', [data.user.profile.department], ')')'");
            Console.WriteLine("   Result: " + EvaluateExpression("concat([data.user.name], ' (', [data.user.profile.department], ')')", nodeEvent));

            Console.WriteLine("\n   Key: 'flags.is_premium_customer'");
            Console.WriteLine("   Value: '[data.user.profile.salary] > 60000 && [data.user.profile.active]'");
            Console.WriteLine("   Result: " + EvaluateExpression("[data.user.profile.salary] > 60000 && [data.user.profile.active]", nodeEvent));

            Console.WriteLine("\n   Key: 'order.category'");
            Console.WriteLine("   Value: '[data.order.amount] > 1000 ? \"Premium\" : \"Standard\"'");
            Console.WriteLine("   Result: " + EvaluateExpression("[data.order.amount] > 1000 ? \"Premium\" : \"Standard\"", nodeEvent));

            Console.WriteLine("\n12. Error handling in expressions:");
            try
            {
                EvaluateAndPrint("[data.nonexistent.property]", nodeEvent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    Expression '[data.nonexistent.property]' -> Error: {ex.Message}");
            }

            // Safe navigation
            EvaluateAndPrint("nvl([data.nonexistent.property], 'default_value')", nodeEvent);
            EvaluateAndPrint("hasPath(data, 'nonexistent.property')", nodeEvent);

            Console.WriteLine("\n=== NCalc Expression Evaluation Example Complete ===");
        }

        private static void EvaluateAndPrint(string expression, NodeEvent nodeEvent)
        {
            try
            {
                var result = EvaluateExpression(expression, nodeEvent);
                Console.WriteLine($"    {expression} -> {result}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    {expression} -> Error: {ex.Message}");
            }
        }

        private static object? EvaluateExpression(string expression, NodeEvent nodeEvent)
        {
            var expr = EventNodeRuleExpression.Create(expression);
            if (expr == null) return null;
            return EventNodeRuleExpression.Evaluate(expr, nodeEvent);
        }
    }
}
