using System;
using System.Collections.Generic;
using KegBridgeCore.Data;

namespace KegBridgeCoreExamples
{
    /// <summary>
    /// Example demonstrating the new dot notation functionality for NodeEvent and NodeEventData
    /// </summary>
    public class DotNotationExample
    {
        public static void RunExample()
        {
            Console.WriteLine("=== NodeEvent Dot Notation Example ===\n");

            // Create a complex nested data structure
            var complexData = new Dictionary<string, object?>
            {
                ["user"] = new Dictionary<string, object?>
                {
                    ["profile"] = new Dictionary<string, object?>
                    {
                        ["name"] = "John Doe",
                        ["email"] = "<EMAIL>",
                        ["preferences"] = new Dictionary<string, object?>
                        {
                            ["theme"] = "dark",
                            ["notifications"] = true,
                            ["language"] = "en-US"
                        }
                    },
                    ["permissions"] = new[] { "read", "write", "admin" }
                },
                ["session"] = new Dictionary<string, object?>
                {
                    ["id"] = "sess_12345",
                    ["created_at"] = DateTime.UtcNow,
                    ["expires_in"] = 3600
                },
                ["metadata"] = new Dictionary<string, object?>
                {
                    ["version"] = "1.0.0",
                    ["environment"] = "production"
                }
            };

            // Create NodeEvent with complex data
            var nodeEvent = NodeEvent.FromDictionary("UserService", complexData, "user.login");

            Console.WriteLine("1. Reading nested values using dot notation:");
            Console.WriteLine($"   User name: {nodeEvent.GetPath("user.profile.name")}");
            Console.WriteLine($"   User email: {nodeEvent.GetPath("user.profile.email")}");
            Console.WriteLine($"   Theme preference: {nodeEvent.GetPath("user.profile.preferences.theme")}");
            Console.WriteLine($"   Session ID: {nodeEvent.GetPath("session.id")}");
            Console.WriteLine($"   Environment: {nodeEvent.GetPath("metadata.environment")}");

            Console.WriteLine("\n2. Type-safe access with TryGetPath:");
            if (nodeEvent.TryGetPath<string>("user.profile.name", out var userName))
            {
                Console.WriteLine($"   Successfully got user name: {userName}");
            }

            if (nodeEvent.TryGetPath<bool>("user.profile.preferences.notifications", out var notifications))
            {
                Console.WriteLine($"   Notifications enabled: {notifications}");
            }

            if (nodeEvent.TryGetPath<int>("session.expires_in", out var expiresIn))
            {
                Console.WriteLine($"   Session expires in: {expiresIn} seconds");
            }

            Console.WriteLine("\n3. Handling non-existent paths:");
            Console.WriteLine($"   Non-existent path: {nodeEvent.GetPath("user.profile.nonexistent")} (null)");
            Console.WriteLine($"   Deep non-existent path: {nodeEvent.GetPath("user.settings.display.resolution")} (null)");

            Console.WriteLine("\n4. Setting values using dot notation:");
            var updatedEvent = nodeEvent
                .SetPath("user.profile.preferences.theme", "light")
                .SetPath("user.profile.last_login", DateTime.UtcNow)
                .SetPath("session.activity.last_action", "page_view")
                .SetPath("metadata.client.browser", "Chrome")
                .SetPath("metadata.client.version", "120.0.0");

            Console.WriteLine("   Updated theme: " + updatedEvent.GetPath("user.profile.preferences.theme"));
            Console.WriteLine("   Added last login: " + updatedEvent.GetPath("user.profile.last_login"));
            Console.WriteLine("   Added session activity: " + updatedEvent.GetPath("session.activity.last_action"));
            Console.WriteLine("   Added client browser: " + updatedEvent.GetPath("metadata.client.browser"));

            Console.WriteLine("\n5. Original event remains unchanged (immutability):");
            Console.WriteLine($"   Original theme: {nodeEvent.GetPath("user.profile.preferences.theme")}");
            Console.WriteLine($"   Original has last_login: {nodeEvent.GetPath("user.profile.last_login") != null}");

            Console.WriteLine("\n6. Working with arrays:");
            var permissions = nodeEvent.GetPath("user.permissions");
            Console.WriteLine($"   Permissions type: {permissions?.GetType().Name}");
            Console.WriteLine($"   Permissions: {permissions}");

            Console.WriteLine("\n7. Setting multiple paths efficiently with SetPaths:");
            
            // Using KeyValuePair collection
            var apiPaths = new[]
            {
                new KeyValuePair<string, object?>("api.endpoints.users.get.url", "/api/v1/users"),
                new KeyValuePair<string, object?>("api.endpoints.users.get.method", "GET"),
                new KeyValuePair<string, object?>("api.endpoints.users.post.url", "/api/v1/users"),
                new KeyValuePair<string, object?>("api.endpoints.users.post.method", "POST"),
                new KeyValuePair<string, object?>("api.config.timeout", 30000),
                new KeyValuePair<string, object?>("api.config.retries", 3),
                new KeyValuePair<string, object?>("api.config.base_url", "https://api.example.com")
            };

            var eventWithApi = new NodeEvent("ApiService", NodeEventData.Empty.SetPaths(apiPaths), "api.config");

            Console.WriteLine("   API GET URL: " + eventWithApi.GetPath("api.endpoints.users.get.url"));
            Console.WriteLine("   API POST URL: " + eventWithApi.GetPath("api.endpoints.users.post.url"));
            Console.WriteLine("   API timeout: " + eventWithApi.GetPath("api.config.timeout"));
            Console.WriteLine("   API base URL: " + eventWithApi.GetPath("api.config.base_url"));

            Console.WriteLine("\n8. Using SetPaths with anonymous objects:");
            var configEvent = nodeEvent.SetPaths(new
            {
                DatabaseHost = "db.example.com",
                DatabasePort = 5432,
                CacheEnabled = true,
                LogLevel = "INFO"
            });

            Console.WriteLine("   Database Host: " + configEvent.GetPath("DatabaseHost"));
            Console.WriteLine("   Database Port: " + configEvent.GetPath("DatabasePort"));
            Console.WriteLine("   Cache Enabled: " + configEvent.GetPath("CacheEnabled"));
            Console.WriteLine("   Log Level: " + configEvent.GetPath("LogLevel"));

            Console.WriteLine("\n9. Comparison: SetPaths vs chained SetPath calls:");
            Console.WriteLine("   SetPaths is more efficient for setting multiple values at once");
            Console.WriteLine("   Both approaches produce the same result but SetPaths:");
            Console.WriteLine("   - Requires only one immutable dictionary rebuild");
            Console.WriteLine("   - Is more readable for bulk operations");
            Console.WriteLine("   - Reduces intermediate object creation");

            Console.WriteLine("\n=== Example Complete ===");
        }
    }
}
