using System;
using System.Collections.Generic;
using KegBridgeCore.Data;

namespace KegBridgeCoreExamples
{
    /// <summary>
    /// Example demonstrating how NodeEvent with dot notation can be used in a data processing pipeline
    /// </summary>
    public class DataProcessingPipelineExample
    {
        public static void RunExample()
        {
            Console.WriteLine("=== Data Processing Pipeline Example ===\n");

            // Simulate incoming raw data from different sources
            var rawUserData = new Dictionary<string, object?>
            {
                ["id"] = "user_12345",
                ["name"] = "<PERSON> Johnson",
                ["email"] = "<EMAIL>",
                ["registration_date"] = "2024-01-15T10:30:00Z",
                ["preferences"] = new Dictionary<string, object?>
                {
                    ["newsletter"] = true,
                    ["theme"] = "auto",
                    ["language"] = "en"
                }
            };

            var rawOrderData = new Dictionary<string, object?>
            {
                ["order_id"] = "ord_67890",
                ["user_id"] = "user_12345",
                ["items"] = new[]
                {
                    new Dictionary<string, object?> { ["sku"] = "BOOK001", ["quantity"] = 2, ["price"] = 29.99 },
                    new Dictionary<string, object?> { ["sku"] = "PEN001", ["quantity"] = 5, ["price"] = 2.50 }
                },
                ["total"] = 72.48,
                ["status"] = "pending"
            };

            Console.WriteLine("1. Creating NodeEvents from raw data:");
            var userEvent = NodeEvent.FromDictionary("UserService", rawUserData, "user.registered");
            var orderEvent = NodeEvent.FromDictionary("OrderService", rawOrderData, "order.created");

            Console.WriteLine($"   User Event: {userEvent.Node} - {userEvent.Topic}");
            Console.WriteLine($"   User Name: {userEvent.GetPath("name")}");
            Console.WriteLine($"   User Email: {userEvent.GetPath("email")}");
            Console.WriteLine($"   Newsletter Preference: {userEvent.GetPath("preferences.newsletter")}");

            Console.WriteLine($"\n   Order Event: {orderEvent.Node} - {orderEvent.Topic}");
            Console.WriteLine($"   Order ID: {orderEvent.GetPath("order_id")}");
            Console.WriteLine($"   Order Total: {orderEvent.GetPath("total")}");

            Console.WriteLine("\n2. Enriching events with additional metadata:");
            
            // Enrich user event with processing metadata
            var enrichedUserEvent = userEvent.SetPaths(new[]
            {
                new KeyValuePair<string, object?>("processing.timestamp", DateTime.UtcNow),
                new KeyValuePair<string, object?>("processing.pipeline_version", "2.1.0"),
                new KeyValuePair<string, object?>("processing.source", "user_registration_api"),
                new KeyValuePair<string, object?>("enrichment.geo.country", "US"),
                new KeyValuePair<string, object?>("enrichment.geo.timezone", "America/New_York"),
                new KeyValuePair<string, object?>("enrichment.segment", "premium_user"),
                new KeyValuePair<string, object?>("validation.email_verified", true),
                new KeyValuePair<string, object?>("validation.phone_verified", false)
            });

            // Enrich order event with calculated fields and metadata
            var enrichedOrderEvent = orderEvent.SetPaths(new
            {
                ProcessingTimestamp = DateTime.UtcNow,
                PipelineVersion = "2.1.0",
                Source = "order_api",
                CalculatedTax = 6.52,
                CalculatedShipping = 9.99,
                EstimatedDelivery = DateTime.UtcNow.AddDays(3),
                RiskScore = 0.15,
                PaymentMethod = "credit_card"
            });

            Console.WriteLine("   Enriched User Event:");
            Console.WriteLine($"     Processing Timestamp: {enrichedUserEvent.GetPath("processing.timestamp")}");
            Console.WriteLine($"     Pipeline Version: {enrichedUserEvent.GetPath("processing.pipeline_version")}");
            Console.WriteLine($"     User Segment: {enrichedUserEvent.GetPath("enrichment.segment")}");
            Console.WriteLine($"     Email Verified: {enrichedUserEvent.GetPath("validation.email_verified")}");

            Console.WriteLine("\n   Enriched Order Event:");
            Console.WriteLine($"     Processing Timestamp: {enrichedOrderEvent.GetPath("ProcessingTimestamp")}");
            Console.WriteLine($"     Calculated Tax: ${enrichedOrderEvent.GetPath("CalculatedTax")}");
            Console.WriteLine($"     Risk Score: {enrichedOrderEvent.GetPath("RiskScore")}");
            Console.WriteLine($"     Estimated Delivery: {enrichedOrderEvent.GetPath("EstimatedDelivery")}");

            Console.WriteLine("\n3. Transforming events for different downstream systems:");

            // Transform for analytics system (flatten some nested data)
            var analyticsEvent = enrichedUserEvent.SetPaths(new[]
            {
                new KeyValuePair<string, object?>("analytics.user_id", enrichedUserEvent.GetPath("id")),
                new KeyValuePair<string, object?>("analytics.user_name", enrichedUserEvent.GetPath("name")),
                new KeyValuePair<string, object?>("analytics.user_email", enrichedUserEvent.GetPath("email")),
                new KeyValuePair<string, object?>("analytics.newsletter_opt_in", enrichedUserEvent.GetPath("preferences.newsletter")),
                new KeyValuePair<string, object?>("analytics.preferred_language", enrichedUserEvent.GetPath("preferences.language")),
                new KeyValuePair<string, object?>("analytics.user_segment", enrichedUserEvent.GetPath("enrichment.segment")),
                new KeyValuePair<string, object?>("analytics.country", enrichedUserEvent.GetPath("enrichment.geo.country")),
                new KeyValuePair<string, object?>("analytics.event_type", "user_registration"),
                new KeyValuePair<string, object?>("analytics.processed_at", DateTime.UtcNow)
            });

            // Transform for notification system
            var notificationEvent = enrichedUserEvent.SetPaths(new
            {
                NotificationType = "welcome_email",
                RecipientEmail = enrichedUserEvent.GetPath("email"),
                RecipientName = enrichedUserEvent.GetPath("name"),
                TemplateData = new Dictionary<string, object?>
                {
                    ["user_name"] = enrichedUserEvent.GetPath("name"),
                    ["preferred_language"] = enrichedUserEvent.GetPath("preferences.language"),
                    ["newsletter_subscribed"] = enrichedUserEvent.GetPath("preferences.newsletter")
                },
                Priority = "normal",
                ScheduledFor = DateTime.UtcNow.AddMinutes(5)
            });

            Console.WriteLine("   Analytics Event:");
            Console.WriteLine($"     User ID: {analyticsEvent.GetPath("analytics.user_id")}");
            Console.WriteLine($"     Event Type: {analyticsEvent.GetPath("analytics.event_type")}");
            Console.WriteLine($"     User Segment: {analyticsEvent.GetPath("analytics.user_segment")}");
            Console.WriteLine($"     Country: {analyticsEvent.GetPath("analytics.country")}");

            Console.WriteLine("\n   Notification Event:");
            Console.WriteLine($"     Type: {notificationEvent.GetPath("NotificationType")}");
            Console.WriteLine($"     Recipient: {notificationEvent.GetPath("RecipientEmail")}");
            Console.WriteLine($"     Priority: {notificationEvent.GetPath("Priority")}");
            Console.WriteLine($"     Scheduled For: {notificationEvent.GetPath("ScheduledFor")}");

            Console.WriteLine("\n4. Demonstrating immutability in pipeline:");
            Console.WriteLine("   Original user event name: " + userEvent.GetPath("name"));
            Console.WriteLine("   Enriched user event name: " + enrichedUserEvent.GetPath("name"));
            Console.WriteLine("   Analytics event name: " + analyticsEvent.GetPath("name"));
            Console.WriteLine("   All events maintain the same user name (immutability preserved)");

            Console.WriteLine("\n5. Error handling with safe path access:");
            if (orderEvent.TryGetPath<string>("customer.vip_status", out var vipStatus))
            {
                Console.WriteLine($"   VIP Status: {vipStatus}");
            }
            else
            {
                Console.WriteLine("   VIP Status: Not available (path doesn't exist)");
            }

            if (orderEvent.TryGetPath<decimal>("total", out var orderTotal))
            {
                Console.WriteLine($"   Order Total (decimal): ${orderTotal}");
            }
            else
            {
                Console.WriteLine("   Could not parse order total as decimal");
            }

            Console.WriteLine("\n=== Data Processing Pipeline Example Complete ===");
        }
    }
}
