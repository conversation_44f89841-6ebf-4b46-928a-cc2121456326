using System;
using System.Collections.Generic;
using System.Text;
using KegBridgeCore.Data;
using KegBridgeCore.Services;

namespace KegBridgeCoreExamples
{
    /// <summary>
    /// Example demonstrating byte array extraction functions in NCalc expressions
    /// </summary>
    public class ByteArrayExtractionExample
    {
        public static void RunExample()
        {
            Console.WriteLine("=== Byte Array Extraction Functions Example ===\n");

            ShowBasicExtractionExamples();
            ShowNegativeOffsetExamples();
            ShowDateTimeExtractionExamples();
            ShowProtocolParsingExample();
            ShowSensorDataExample();
            ShowNetworkPacketExample();
            ShowMixedEndiannessExample();

            Console.WriteLine("\n=== Byte Array Extraction Example Complete ===");
        }

        private static void ShowBasicExtractionExamples()
        {
            Console.WriteLine("1. Basic Extraction Examples:\n");

            // Create test data with various byte arrays
            var uint16LeBytes = new byte[] { 0x34, 0x12 }; // 0x1234 in little endian
            var uint16BeBytes = new byte[] { 0x12, 0x34 }; // 0x1234 in big endian
            var uint32LeBytes = new byte[] { 0x78, 0x56, 0x34, 0x12 }; // 0x12345678 in little endian
            var uint32BeBytes = new byte[] { 0x12, 0x34, 0x56, 0x78 }; // 0x12345678 in big endian
            var mixedDataBytes = new byte[] { 0xFF, 0xAA, 0x34, 0x12, 0x78, 0x56, 0x34, 0x12, 0xBB, 0xCC };

            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["uint16_le"] = uint16LeBytes,
                ["uint16_be"] = uint16BeBytes,
                ["uint32_le"] = uint32LeBytes,
                ["uint32_be"] = uint32BeBytes,
                ["mixed_data"] = mixedDataBytes
            });

            var nodeEvent = new NodeEvent("ExtractionNode", data, "byte_extraction");

            Console.WriteLine("   Test Data:");
            Console.WriteLine($"     uint16_le: [{string.Join(", ", uint16LeBytes)}] (0x1234 LE)");
            Console.WriteLine($"     uint16_be: [{string.Join(", ", uint16BeBytes)}] (0x1234 BE)");
            Console.WriteLine($"     uint32_le: [{string.Join(", ", uint32LeBytes)}] (0x12345678 LE)");
            Console.WriteLine($"     uint32_be: [{string.Join(", ", uint32BeBytes)}] (0x12345678 BE)");

            Console.WriteLine("\n   Extraction Results:");

            // Test UInt16 extractions
            var uint16_le = EvaluateExpression("getUInt16LE([data.uint16_le])", nodeEvent);
            var uint16_be = EvaluateExpression("getUInt16BE([data.uint16_be])", nodeEvent);
            Console.WriteLine($"     getUInt16LE([data.uint16_le]) = {uint16_le} (0x{uint16_le:X4})");
            Console.WriteLine($"     getUInt16BE([data.uint16_be]) = {uint16_be} (0x{uint16_be:X4})");

            // Test UInt32 extractions
            var uint32_le = EvaluateExpression("getUInt32LE([data.uint32_le])", nodeEvent);
            var uint32_be = EvaluateExpression("getUInt32BE([data.uint32_be])", nodeEvent);
            Console.WriteLine($"     getUInt32LE([data.uint32_le]) = {uint32_le} (0x{uint32_le:X8})");
            Console.WriteLine($"     getUInt32BE([data.uint32_be]) = {uint32_be} (0x{uint32_be:X8})");

            // Test with offsets
            var uint16_offset = EvaluateExpression("getUInt16LE([data.mixed_data], 2)", nodeEvent);
            var uint32_offset = EvaluateExpression("getUInt32LE([data.mixed_data], 4)", nodeEvent);
            Console.WriteLine($"     getUInt16LE([data.mixed_data], 2) = {uint16_offset} (0x{uint16_offset:X4})");
            Console.WriteLine($"     getUInt32LE([data.mixed_data], 4) = {uint32_offset} (0x{uint32_offset:X8})");

            // Test generic functions with explicit endianness
            var generic_le = EvaluateExpression("getUInt16([data.uint16_le], 0, true)", nodeEvent);
            var generic_be = EvaluateExpression("getUInt16([data.uint16_be], 0, false)", nodeEvent);
            Console.WriteLine($"     getUInt16([data.uint16_le], 0, true) = {generic_le} (0x{generic_le:X4})");
            Console.WriteLine($"     getUInt16([data.uint16_be], 0, false) = {generic_be} (0x{generic_be:X4})");

            // Test negative offsets (count from end)
            var negativeOffset16 = EvaluateExpression("getUInt16LE([data.mixed_data], -2)", nodeEvent);
            var negativeOffset32 = EvaluateExpression("getUInt32LE([data.mixed_data], -4)", nodeEvent);
            Console.WriteLine($"     getUInt16LE([data.mixed_data], -2) = {negativeOffset16} (0x{negativeOffset16:X4}) [last 2 bytes]");
            Console.WriteLine($"     getUInt32LE([data.mixed_data], -4) = {negativeOffset32} (0x{negativeOffset32:X8}) [last 4 bytes]");
        }

        private static void ShowNegativeOffsetExamples()
        {
            Console.WriteLine("\n2. Negative Offset Examples (Count from End):\n");

            // Create test data with known pattern
            var testData = new byte[] { 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xAA };

            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["test_data"] = testData
            });

            var nodeEvent = new NodeEvent("NegativeOffsetNode", data, "negative_offset_test");

            Console.WriteLine("   Test Data (10 bytes):");
            Console.WriteLine($"     [{string.Join(", ", testData.Select(b => $"0x{b:X2}"))}]");
            Console.WriteLine("     Indices: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]");

            Console.WriteLine("\n   Negative Offset Extractions:");

            // UInt16 extractions with negative offsets
            var last2Bytes = EvaluateExpression("getUInt16LE([data.test_data], -2)", nodeEvent);
            var secondLast2Bytes = EvaluateExpression("getUInt16LE([data.test_data], -4)", nodeEvent);
            var thirdLast2Bytes = EvaluateExpression("getUInt16LE([data.test_data], -6)", nodeEvent);

            Console.WriteLine($"     getUInt16LE([data.test_data], -2) = {last2Bytes} (0x{last2Bytes:X4}) [bytes 8,9: 0x99,0xAA]");
            Console.WriteLine($"     getUInt16LE([data.test_data], -4) = {secondLast2Bytes} (0x{secondLast2Bytes:X4}) [bytes 6,7: 0x77,0x88]");
            Console.WriteLine($"     getUInt16LE([data.test_data], -6) = {thirdLast2Bytes} (0x{thirdLast2Bytes:X4}) [bytes 4,5: 0x55,0x66]");

            // UInt32 extractions with negative offsets
            var last4Bytes = EvaluateExpression("getUInt32LE([data.test_data], -4)", nodeEvent);
            var secondLast4Bytes = EvaluateExpression("getUInt32LE([data.test_data], -8)", nodeEvent);

            Console.WriteLine($"     getUInt32LE([data.test_data], -4) = {last4Bytes} (0x{last4Bytes:X8}) [bytes 6,7,8,9: 0x77,0x88,0x99,0xAA]");
            Console.WriteLine($"     getUInt32LE([data.test_data], -8) = {secondLast4Bytes} (0x{secondLast4Bytes:X8}) [bytes 2,3,4,5: 0x33,0x44,0x55,0x66]");

            // Compare with positive offsets
            Console.WriteLine("\n   Comparison with Positive Offsets:");
            var positiveOffset8 = EvaluateExpression("getUInt16LE([data.test_data], 8)", nodeEvent);
            var negativeOffset2 = EvaluateExpression("getUInt16LE([data.test_data], -2)", nodeEvent);

            Console.WriteLine($"     getUInt16LE([data.test_data], 8) = {positiveOffset8} (0x{positiveOffset8:X4}) [positive offset]");
            Console.WriteLine($"     getUInt16LE([data.test_data], -2) = {negativeOffset2} (0x{negativeOffset2:X4}) [negative offset]");
            Console.WriteLine($"     → Both extract the same bytes: {positiveOffset8 == negativeOffset2}");

            // Big endian examples
            Console.WriteLine("\n   Big Endian with Negative Offsets:");
            var last2BytesBE = EvaluateExpression("getUInt16BE([data.test_data], -2)", nodeEvent);
            var last4BytesBE = EvaluateExpression("getUInt32BE([data.test_data], -4)", nodeEvent);

            Console.WriteLine($"     getUInt16BE([data.test_data], -2) = {last2BytesBE} (0x{last2BytesBE:X4}) [bytes 8,9: 0x99,0xAA]");
            Console.WriteLine($"     getUInt32BE([data.test_data], -4) = {last4BytesBE} (0x{last4BytesBE:X8}) [bytes 6,7,8,9: 0x77,0x88,0x99,0xAA]");

            // Practical use case
            Console.WriteLine("\n   Practical Use Case - Extracting Trailer/Footer Data:");
            Console.WriteLine("     Many protocols have checksums or signatures at the end:");
            Console.WriteLine($"     Last 2 bytes as checksum: 0x{last2BytesBE:X4}");
            Console.WriteLine($"     Last 4 bytes as signature: 0x{last4BytesBE:X8}");
        }

        private static void ShowDateTimeExtractionExamples()
        {
            Console.WriteLine("\n3. DateTime Extraction Examples (8-byte Epoch Values):\n");

            // Create test data with various epoch timestamp formats
            var currentTime = DateTimeOffset.UtcNow;

            // Different epoch formats
            var unixSeconds = (ulong)currentTime.ToUnixTimeSeconds();
            var unixMilliseconds = (ulong)currentTime.ToUnixTimeMilliseconds();
            var unixMicroseconds = (ulong)(currentTime.ToUnixTimeMilliseconds() * 1000);
            var unixNanoseconds = 1755872665095018066UL; // Example from user: a few seconds ago

            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["timestamp_seconds"] = BitConverter.GetBytes(unixSeconds),
                ["timestamp_millis"] = BitConverter.GetBytes(unixMilliseconds),
                ["timestamp_micros"] = BitConverter.GetBytes(unixMicroseconds),
                ["timestamp_nanos"] = BitConverter.GetBytes(unixNanoseconds),
                ["mixed_timestamps"] = CreateMixedTimestampArray()
            });

            var nodeEvent = new NodeEvent("TimestampNode", data, "timestamp_test");

            Console.WriteLine("   Test Data:");
            Console.WriteLine($"     Current time: {currentTime:yyyy-MM-dd HH:mm:ss.fff} UTC");
            Console.WriteLine($"     Unix seconds: {unixSeconds}");
            Console.WriteLine($"     Unix milliseconds: {unixMilliseconds}");
            Console.WriteLine($"     Unix microseconds: {unixMicroseconds}");
            Console.WriteLine($"     Unix nanoseconds: {unixNanoseconds}");

            Console.WriteLine("\n   DateTime Extraction Results:");

            // Extract different timestamp formats
            var extractedSeconds = EvaluateExpression("getDateTimeLE([data.timestamp_seconds])", nodeEvent);
            var extractedMillis = EvaluateExpression("getDateTimeLE([data.timestamp_millis])", nodeEvent);
            var extractedMicros = EvaluateExpression("getDateTimeLE([data.timestamp_micros])", nodeEvent);
            var extractedNanos = EvaluateExpression("getDateTimeLE([data.timestamp_nanos])", nodeEvent);

            Console.WriteLine($"     getDateTimeLE([data.timestamp_seconds]) = {extractedSeconds:yyyy-MM-dd HH:mm:ss.fff} UTC");
            Console.WriteLine($"     getDateTimeLE([data.timestamp_millis]) = {extractedMillis:yyyy-MM-dd HH:mm:ss.fff} UTC");
            Console.WriteLine($"     getDateTimeLE([data.timestamp_micros]) = {extractedMicros:yyyy-MM-dd HH:mm:ss.fffffff} UTC");
            Console.WriteLine($"     getDateTimeLE([data.timestamp_nanos]) = {extractedNanos:yyyy-MM-dd HH:mm:ss.fffffff} UTC");

            // Show subsecond precision details
            Console.WriteLine("\n   Subsecond Precision Details:");
            Console.WriteLine($"     Nanoseconds input: {unixNanoseconds}");
            Console.WriteLine($"     Extracted DateTime ticks: {((DateTime)extractedNanos).Ticks}");
            Console.WriteLine($"     Milliseconds: {((DateTime)extractedNanos).Millisecond}");
            Console.WriteLine($"     Microseconds (from ticks): {(((DateTime)extractedNanos).Ticks % 10000000) / 10}");
            Console.WriteLine($"     100-nanosecond precision preserved: {((DateTime)extractedNanos).Ticks % 10 == (long)(unixNanoseconds / 100) % 10}");

            // Test with offsets
            var timestampAtOffset = EvaluateExpression("getDateTimeLE([data.mixed_timestamps], 8)", nodeEvent);
            var timestampFromEnd = EvaluateExpression("getDateTimeLE([data.mixed_timestamps], -8)", nodeEvent);

            Console.WriteLine($"     getDateTimeLE([data.mixed_timestamps], 8) = {timestampAtOffset:yyyy-MM-dd HH:mm:ss.fff} UTC");
            Console.WriteLine($"     getDateTimeLE([data.mixed_timestamps], -8) = {timestampFromEnd:yyyy-MM-dd HH:mm:ss.fff} UTC");

            // Test big endian
            var bigEndianBytes = BitConverter.GetBytes(unixNanoseconds);
            if (BitConverter.IsLittleEndian)
            {
                Array.Reverse(bigEndianBytes);
            }

            var bigEndianData = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["timestamp_be"] = bigEndianBytes
            });
            var bigEndianEvent = new NodeEvent("BigEndianNode", bigEndianData, "be_test");

            var extractedBE = EvaluateExpression("getDateTimeBE([data.timestamp_be])", bigEndianEvent);
            Console.WriteLine($"     getDateTimeBE([data.timestamp_be]) = {extractedBE:yyyy-MM-dd HH:mm:ss.fff} UTC");

            // Test generic function with explicit endianness
            var genericLE = EvaluateExpression("getDateTime([data.timestamp_nanos], 0, true)", nodeEvent);
            var genericBE = EvaluateExpression("getDateTime([data.timestamp_be], 0, false)", bigEndianEvent);

            Console.WriteLine($"     getDateTime([data.timestamp_nanos], 0, true) = {genericLE:yyyy-MM-dd HH:mm:ss.fff} UTC");
            Console.WriteLine($"     getDateTime([data.timestamp_be], 0, false) = {genericBE:yyyy-MM-dd HH:mm:ss.fff} UTC");

            Console.WriteLine("\n   Automatic Format Detection:");
            Console.WriteLine("     → Seconds: Detected as Unix timestamp in seconds");
            Console.WriteLine("     → Milliseconds: Detected as Unix timestamp in milliseconds");
            Console.WriteLine("     → Microseconds: Detected as Unix timestamp in microseconds");
            Console.WriteLine("     → Nanoseconds: Detected as Unix timestamp in nanoseconds");
            Console.WriteLine("     → All formats automatically converted to DateTime objects");
        }

        private static byte[] CreateMixedTimestampArray()
        {
            // Create a 24-byte array with timestamps at different positions
            var array = new byte[24];

            // Timestamp 1 at offset 0 (8 bytes)
            var timestamp1 = BitConverter.GetBytes(1692710265000UL); // Milliseconds
            Array.Copy(timestamp1, 0, array, 0, 8);

            // Timestamp 2 at offset 8 (8 bytes)
            var timestamp2 = BitConverter.GetBytes(1755872665095018066UL); // Nanoseconds
            Array.Copy(timestamp2, 0, array, 8, 8);

            // Timestamp 3 at offset 16 (8 bytes) - from end at -8
            var timestamp3 = BitConverter.GetBytes(1692710265UL); // Seconds
            Array.Copy(timestamp3, 0, array, 16, 8);

            return array;
        }

        private static void ShowProtocolParsingExample()
        {
            Console.WriteLine("\n4. Protocol Parsing Example (MODBUS RTU):\n");

            // MODBUS RTU frame: [Device ID][Function][Data Length][Data...][CRC16]
            // Example: Read holding registers response
            var modbusFrame = new byte[]
            {
                0x01,       // Device ID
                0x03,       // Function code (Read Holding Registers)
                0x04,       // Data length (4 bytes = 2 registers)
                0x12, 0x34, // Register 1 value (0x1234 big endian)
                0x56, 0x78, // Register 2 value (0x5678 big endian)
                0x84, 0x04  // CRC16 (big endian)
            };

            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["modbus_frame"] = modbusFrame
            });

            var nodeEvent = new NodeEvent("ModbusNode", data, "modbus_response");

            Console.WriteLine("   MODBUS RTU Frame:");
            Console.WriteLine($"     Raw bytes: [{string.Join(" ", modbusFrame.Select(b => $"0x{b:X2}"))}]");

            Console.WriteLine("\n   Parsed Fields:");
            // For individual bytes, we'll extract them using the byte array directly
            var deviceId = modbusFrame[0];
            var functionCode = modbusFrame[1];
            var dataLength = modbusFrame[2];
            var register1 = EvaluateExpression("getUInt16BE([data.modbus_frame], 3)", nodeEvent);
            var register2 = EvaluateExpression("getUInt16BE([data.modbus_frame], 5)", nodeEvent);
            var crc = EvaluateExpression("getUInt16BE([data.modbus_frame], 7)", nodeEvent);

            Console.WriteLine($"     Device ID: {deviceId}");
            Console.WriteLine($"     Function Code: {functionCode}");
            Console.WriteLine($"     Data Length: {dataLength}");
            Console.WriteLine($"     Register 1: {register1} (0x{register1:X4})");
            Console.WriteLine($"     Register 2: {register2} (0x{register2:X4})");
            Console.WriteLine($"     CRC16: {crc} (0x{crc:X4})");
        }

        private static void ShowSensorDataExample()
        {
            Console.WriteLine("\n5. Sensor Data Parsing Example:\n");

            // Sensor data packet: [Header][Timestamp][Temperature][Humidity][Pressure][Checksum]
            var sensorPacket = new byte[]
            {
                0xAA, 0xBB,             // Header (0xAABB)
                0x60, 0x23, 0x8F, 0x61, // Unix timestamp (little endian)
                0x5C, 0x01,             // Temperature in 0.1°C (0x015C = 348 = 34.8°C, little endian)
                0x90, 0x19,             // Humidity in 0.1% (0x1990 = 6544 = 65.44%, little endian)
                0x40, 0x8C, 0x01, 0x00, // Pressure in Pa (0x00018C40 = 101440 Pa, little endian)
                0xDE, 0xAD              // Checksum (big endian)
            };

            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["sensor_packet"] = sensorPacket
            });

            var nodeEvent = new NodeEvent("SensorNode", data, "sensor_reading");

            Console.WriteLine("   Sensor Data Packet:");
            Console.WriteLine($"     Raw bytes: [{string.Join(" ", sensorPacket.Select(b => $"0x{b:X2}"))}]");

            Console.WriteLine("\n   Parsed Sensor Data:");
            var header = EvaluateExpression("getUInt16BE([data.sensor_packet], 0)", nodeEvent);
            var timestamp = EvaluateExpression("getUInt32LE([data.sensor_packet], 2)", nodeEvent);
            var tempRaw = EvaluateExpression("getUInt16LE([data.sensor_packet], 6)", nodeEvent);
            var humidityRaw = EvaluateExpression("getUInt16LE([data.sensor_packet], 8)", nodeEvent);
            var pressureRaw = EvaluateExpression("getUInt32LE([data.sensor_packet], 10)", nodeEvent);
            var checksum = EvaluateExpression("getUInt16BE([data.sensor_packet], 14)", nodeEvent);

            Console.WriteLine($"     Header: 0x{header:X4}");
            Console.WriteLine($"     Timestamp: {timestamp} (Unix time)");
            Console.WriteLine($"     Temperature: {tempRaw} raw = {(uint)tempRaw / 10.0:F1}°C");
            Console.WriteLine($"     Humidity: {humidityRaw} raw = {(uint)humidityRaw / 100.0:F2}%");
            Console.WriteLine($"     Pressure: {pressureRaw} Pa = {(uint)pressureRaw / 100.0:F0} hPa");
            Console.WriteLine($"     Checksum: 0x{checksum:X4}");

            // Convert timestamp to readable date
            var dateTime = DateTimeOffset.FromUnixTimeSeconds((long)(uint)timestamp);
            Console.WriteLine($"     Timestamp as date: {dateTime:yyyy-MM-dd HH:mm:ss} UTC");
        }

        private static void ShowNetworkPacketExample()
        {
            Console.WriteLine("\n6. Network Packet Parsing Example (Simplified IP Header):\n");

            // Simplified IPv4 header fields
            var ipHeader = new byte[]
            {
                0x45,                   // Version (4) + IHL (5)
                0x00,                   // Type of Service
                0x00, 0x3C,             // Total Length (60 bytes, big endian)
                0x1C, 0x46,             // Identification (big endian)
                0x40, 0x00,             // Flags + Fragment Offset (big endian)
                0x40,                   // TTL (64)
                0x06,                   // Protocol (TCP = 6)
                0xB1, 0xE6,             // Header Checksum (big endian)
                0xC0, 0xA8, 0x01, 0x64, // Source IP (*************, big endian)
                0xC0, 0xA8, 0x01, 0x01  // Destination IP (***********, big endian)
            };

            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["ip_header"] = ipHeader
            });

            var nodeEvent = new NodeEvent("NetworkNode", data, "ip_packet");

            Console.WriteLine("   IPv4 Header:");
            Console.WriteLine($"     Raw bytes: [{string.Join(" ", ipHeader.Select(b => $"0x{b:X2}"))}]");

            Console.WriteLine("\n   Parsed IP Header:");
            var versionIhl = ipHeader[0];
            var totalLength = EvaluateExpression("getUInt16BE([data.ip_header], 2)", nodeEvent);
            var identification = EvaluateExpression("getUInt16BE([data.ip_header], 4)", nodeEvent);
            var flagsFragment = EvaluateExpression("getUInt16BE([data.ip_header], 6)", nodeEvent);
            var ttl = ipHeader[8];
            var protocol = ipHeader[9];
            var checksum = EvaluateExpression("getUInt16BE([data.ip_header], 10)", nodeEvent);
            var sourceIP = EvaluateExpression("getUInt32BE([data.ip_header], 12)", nodeEvent);
            var destIP = EvaluateExpression("getUInt32BE([data.ip_header], 16)", nodeEvent);

            Console.WriteLine($"     Version: {(byte)versionIhl >> 4}");
            Console.WriteLine($"     IHL: {(byte)versionIhl & 0x0F}");
            Console.WriteLine($"     Total Length: {totalLength} bytes");
            Console.WriteLine($"     Identification: 0x{identification:X4}");
            Console.WriteLine($"     Flags + Fragment: 0x{flagsFragment:X4}");
            Console.WriteLine($"     TTL: {ttl}");
            Console.WriteLine($"     Protocol: {protocol} (TCP)");
            Console.WriteLine($"     Checksum: 0x{checksum:X4}");
            Console.WriteLine($"     Source IP: {FormatIPAddress((uint)sourceIP)}");
            Console.WriteLine($"     Destination IP: {FormatIPAddress((uint)destIP)}");
        }

        private static void ShowMixedEndiannessExample()
        {
            Console.WriteLine("\n7. Mixed Endianness Example:\n");

            // Some protocols mix endianness within the same packet
            var mixedPacket = new byte[]
            {
                0x12, 0x34,             // Big endian uint16 (0x1234)
                0x78, 0x56,             // Little endian uint16 (0x5678)
                0x12, 0x34, 0x56, 0x78, // Big endian uint32 (0x12345678)
                0xEF, 0xCD, 0xAB, 0x90  // Little endian uint32 (0x90ABCDEF)
            };

            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["mixed_packet"] = mixedPacket
            });

            var nodeEvent = new NodeEvent("MixedNode", data, "mixed_endian");

            Console.WriteLine("   Mixed Endianness Packet:");
            Console.WriteLine($"     Raw bytes: [{string.Join(" ", mixedPacket.Select(b => $"0x{b:X2}"))}]");

            Console.WriteLine("\n   Extracted Values:");
            var bigUint16 = EvaluateExpression("getUInt16BE([data.mixed_packet], 0)", nodeEvent);
            var littleUint16 = EvaluateExpression("getUInt16LE([data.mixed_packet], 2)", nodeEvent);
            var bigUint32 = EvaluateExpression("getUInt32BE([data.mixed_packet], 4)", nodeEvent);
            var littleUint32 = EvaluateExpression("getUInt32LE([data.mixed_packet], 8)", nodeEvent);

            Console.WriteLine($"     Big Endian UInt16 (offset 0): {bigUint16} (0x{bigUint16:X4})");
            Console.WriteLine($"     Little Endian UInt16 (offset 2): {littleUint16} (0x{littleUint16:X4})");
            Console.WriteLine($"     Big Endian UInt32 (offset 4): {bigUint32} (0x{bigUint32:X8})");
            Console.WriteLine($"     Little Endian UInt32 (offset 8): {littleUint32} (0x{littleUint32:X8})");

            // Show how the same bytes would be interpreted differently
            Console.WriteLine("\n   Comparison - Same bytes, different endianness:");
            var sameBytes_BE = EvaluateExpression("getUInt16BE([data.mixed_packet], 2)", nodeEvent);
            var sameBytes_LE = EvaluateExpression("getUInt16LE([data.mixed_packet], 2)", nodeEvent);
            Console.WriteLine($"     Bytes [0x78, 0x56] as BE: {sameBytes_BE} (0x{sameBytes_BE:X4})");
            Console.WriteLine($"     Bytes [0x78, 0x56] as LE: {sameBytes_LE} (0x{sameBytes_LE:X4})");
        }

        private static object? EvaluateExpression(string expression, NodeEvent nodeEvent)
        {
            var expr = EventNodeRuleExpression.Create(expression);
            if (expr == null) return null;
            return EventNodeRuleExpression.Evaluate(expr, nodeEvent);
        }

        private static string FormatIPAddress(uint ipAddress)
        {
            return $"{(ipAddress >> 24) & 0xFF}.{(ipAddress >> 16) & 0xFF}.{(ipAddress >> 8) & 0xFF}.{ipAddress & 0xFF}";
        }
    }
}
