using KegBridgeCoreExamples;

Console.WriteLine("KegBridge Core Examples");
Console.WriteLine("=======================\n");

try
{
    // Run all examples
    Console.WriteLine("Running examples...\n");

    // 1. Basic dot notation functionality
    // DotNotationExample.RunExample();

    Console.WriteLine("\n" + new string('=', 80) + "\n");

    // 2. Data processing pipeline example
    // DataProcessingPipelineExample.RunExample();

    Console.WriteLine("\n" + new string('=', 80) + "\n");

    // 3. Akka.NET actor messaging example
    // AkkaActorExample.RunExample();

    Console.WriteLine("\n" + new string('=', 80) + "\n");

    // 4. NCalc expression evaluation example
    NCalcExpressionExample.RunExample();

    Console.WriteLine("\n" + new string('=', 80) + "\n");

    // 5. DeletePath functionality example
    DeletePathExample.RunExample();

    Console.WriteLine("\n" + new string('=', 80) + "\n");

    // 6. Byte array conversion example
    ByteArrayExample.RunExample();

    Console.WriteLine("\n" + new string('=', 80) + "\n");

    // 7. Byte array extraction example
    ByteArrayExtractionExample.RunExample();

    Console.WriteLine("\n" + new string('=', 80) + "\n");

    // 8. Byte array logging example

    Console.WriteLine("\n" + new string('=', 80));
    Console.WriteLine("All examples completed successfully!");
}
catch (Exception ex)
{
    Console.WriteLine($"Error running examples: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    return 1;
}

return 0;
