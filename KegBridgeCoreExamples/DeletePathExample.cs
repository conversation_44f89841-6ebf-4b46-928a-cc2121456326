using System;
using System.Collections.Generic;
using KegBridgeCore.Data;

namespace KegBridgeCoreExamples
{
    /// <summary>
    /// Example demonstrating DeletePath functionality for NodeEvent and NodeEventData
    /// </summary>
    public class DeletePathExample
    {
        public static void RunExample()
        {
            Console.WriteLine("=== DeletePath Functionality Example ===\n");

            // Create sample data with nested structure
            var sampleData = new Dictionary<string, object?>
            {
                ["user"] = new Dictionary<string, object?>
                {
                    ["id"] = 12345,
                    ["profile"] = new Dictionary<string, object?>
                    {
                        ["name"] = "John Doe",
                        ["age"] = 30,
                        ["email"] = "<EMAIL>",
                        ["preferences"] = new Dictionary<string, object?>
                        {
                            ["theme"] = "dark",
                            ["notifications"] = true,
                            ["language"] = "en"
                        }
                    },
                    ["settings"] = new Dictionary<string, object?>
                    {
                        ["privacy"] = "public",
                        ["newsletter"] = false
                    }
                },
                ["metadata"] = new Dictionary<string, object?>
                {
                    ["created"] = DateTime.UtcNow,
                    ["source"] = "web_api",
                    ["version"] = "2.1.0"
                },
                ["temporary"] = "should_be_deleted"
            };

            var nodeEvent = NodeEvent.FromDictionary("UserService", sampleData, "user.profile.updated");

            Console.WriteLine("1. Original NodeEvent structure:");
            PrintNodeEventStructure(nodeEvent);

            Console.WriteLine("\n2. Deleting a simple leaf node (temporary):");
            var step1 = nodeEvent.DeletePath("temporary");
            PrintNodeEventStructure(step1);

            Console.WriteLine("\n3. Deleting a nested leaf node (user.profile.age):");
            var step2 = step1.DeletePath("user.profile.age");
            PrintNodeEventStructure(step2);

            Console.WriteLine("\n4. Deleting multiple leaf nodes at once:");
            var step3 = step2.DeletePaths(new[] { "user.profile.email", "metadata.version" });
            PrintNodeEventStructure(step3);

            Console.WriteLine("\n5. Attempting to delete a non-leaf node without permission:");
            try
            {
                step3.DeletePath("user.profile.preferences");
            }
            catch (ArgumentException ex)
            {
                Console.WriteLine($"   Expected error: {ex.Message}");
            }

            Console.WriteLine("\n6. Deleting a non-leaf node with permission (user.profile.preferences):");
            var step4 = step3.DeletePath("user.profile.preferences", allowNonLeafDeletion: true);
            PrintNodeEventStructure(step4);

            Console.WriteLine("\n7. Deleting the last property in a container (user.settings.newsletter):");
            var step5 = step4.DeletePath("user.settings.newsletter");
            Console.WriteLine("   After deleting user.settings.newsletter:");
            PrintNodeEventStructure(step5);

            Console.WriteLine("\n8. Deleting the remaining property (user.settings.privacy):");
            var step6 = step5.DeletePath("user.settings.privacy");
            Console.WriteLine("   After deleting user.settings.privacy (empty containers are auto-removed):");
            PrintNodeEventStructure(step6);

            Console.WriteLine("\n9. Error handling - attempting to delete non-existent path:");
            try
            {
                step6.DeletePath("user.nonexistent.property");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"   Expected error: {ex.Message}");
            }

            Console.WriteLine("\n10. Practical use case - Data cleanup transformation:");
            var messyData = new Dictionary<string, object?>
            {
                ["user"] = new Dictionary<string, object?>
                {
                    ["name"] = "Alice",
                    ["email"] = "<EMAIL>",
                    ["temp_token"] = "abc123",
                    ["debug_info"] = new Dictionary<string, object?>
                    {
                        ["trace_id"] = "xyz789",
                        ["debug_mode"] = true
                    }
                },
                ["internal"] = new Dictionary<string, object?>
                {
                    ["processing_time"] = 150,
                    ["server_id"] = "srv-001"
                },
                ["sensitive"] = new Dictionary<string, object?>
                {
                    ["password_hash"] = "hashed_password",
                    ["api_key"] = "secret_key"
                }
            };

            var messyEvent = NodeEvent.FromDictionary("DataProcessor", messyData, "data.processed");
            Console.WriteLine("\n   Before cleanup:");
            PrintNodeEventStructure(messyEvent);

            // Clean up sensitive and temporary data
            var cleanEvent = messyEvent.DeletePaths(new[]
            {
                "user.temp_token",
                "internal",
                "sensitive"
            }, allowNonLeafDeletion: true);

            Console.WriteLine("\n   After cleanup (removed temp_token, internal, and sensitive data):");
            PrintNodeEventStructure(cleanEvent);

            Console.WriteLine("\n11. Immutability verification:");
            Console.WriteLine($"   Original nodeEvent still has temporary: {nodeEvent.TryGetPath("temporary", out _)}");
            Console.WriteLine($"   Final cleanEvent has temporary: {cleanEvent.TryGetPath("temporary", out _)}");
            Console.WriteLine($"   Original messyEvent still has sensitive data: {messyEvent.TryGetPath("sensitive", out _)}");
            Console.WriteLine($"   Clean event has sensitive data: {cleanEvent.TryGetPath("sensitive", out _)}");

            Console.WriteLine("\n=== DeletePath Example Complete ===");
        }

        private static void PrintNodeEventStructure(NodeEvent nodeEvent)
        {
            Console.WriteLine($"   Node: {nodeEvent.Node}, Topic: {nodeEvent.Topic}");
            Console.WriteLine("   Data structure:");
            PrintDataStructure(nodeEvent.Data, "     ");
        }

        private static void PrintDataStructure(object? data, string indent)
        {
            switch (data)
            {
                case NodeEventData nodeData:
                    foreach (var kvp in nodeData)
                    {
                        Console.WriteLine($"{indent}{kvp.Key}:");
                        PrintDataStructure(kvp.Value, indent + "  ");
                    }
                    break;
                case IDictionary<string, object?> dict:
                    foreach (var kvp in dict)
                    {
                        Console.WriteLine($"{indent}{kvp.Key}:");
                        PrintDataStructure(kvp.Value, indent + "  ");
                    }
                    break;
                case null:
                    Console.WriteLine($"{indent}null");
                    break;
                default:
                    Console.WriteLine($"{indent}{data} ({data.GetType().Name})");
                    break;
            }
        }
    }
}
