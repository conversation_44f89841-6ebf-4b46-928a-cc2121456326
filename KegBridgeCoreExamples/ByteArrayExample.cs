using System;
using System.Collections.Generic;
using System.Text;
using KegBridgeCore.Data;
using KegBridgeCore.Utilities;
using Newtonsoft.Json;

namespace KegBridgeCoreExamples
{
    /// <summary>
    /// Example demonstrating byte array conversion functionality in EventsController
    /// </summary>
    public class ByteArrayExample
    {
        public static void RunExample()
        {
            Console.WriteLine("=== Byte Array Conversion Example ===\n");

            ShowSupportedFormats();
            ShowBasicConversions();
            ShowComplexPayloadExample();
            ShowEventControllerUsage();
            ShowRealWorldExamples();

            Console.WriteLine("\n=== Byte Array Example Complete ===");
        }

        private static void ShowSupportedFormats()
        {
            Console.WriteLine("1. Supported Byte Array String Signatures:\n");

            var formats = ByteArrayConverter.GetSupportedFormats();
            foreach (var format in formats)
            {
                Console.WriteLine($"   {format.Key}: {format.Value}");
            }
            Console.WriteLine();
        }

        private static void ShowBasicConversions()
        {
            Console.WriteLine("2. Basic Conversion Examples:\n");

            var testData = "Hello World";
            var testBytes = Encoding.UTF8.GetBytes(testData);

            // Show different ways to represent the same data
            var examples = new Dictionary<string, string>
            {
                ["Base64"] = ByteArrayConverter.BytesToBase64Signature(testBytes),
                ["Hex"] = ByteArrayConverter.BytesToHexSignature(testBytes),
                ["Hex with Spaces"] = "hex:48 65 6C 6C 6F 20 57 6F 72 6C 64",
                ["Hex with Hyphens"] = "hex:48-65-6C-6C-6F-20-57-6F-72-6C-64",
                ["Hex Mixed Separators"] = "hex:48-65 6C-6C 6F 20-57 6F-72 6C-64",
                ["Bytes Array"] = ByteArrayConverter.BytesToBytesSignature(testBytes),
                ["UTF-8"] = "utf8:Hello World",
                ["ASCII"] = "ascii:Hello World"
            };

            foreach (var example in examples)
            {
                Console.WriteLine($"   {example.Key}: {example.Value}");
                
                if (ByteArrayConverter.TryConvertString(example.Value, out var converted))
                {
                    var asString = Encoding.UTF8.GetString(converted);
                    Console.WriteLine($"     → Converts to {converted.Length} bytes: \"{asString}\"");
                }
                Console.WriteLine();
            }
        }

        private static void ShowComplexPayloadExample()
        {
            Console.WriteLine("3. Complex Payload Processing Example:\n");

            // Create a complex payload with various byte array signatures
            var complexPayload = new Dictionary<string, object>
            {
                ["device"] = new Dictionary<string, object>
                {
                    ["id"] = "DEVICE_001",
                    ["firmware"] = new Dictionary<string, object>
                    {
                        ["version"] = "1.2.3",
                        ["binary_data"] = "base64:SGVsbG8gRmlybXdhcmU=", // "Hello Firmware"
                        ["checksum"] = "hex:48656C6C6F436865636B73756D" // "HelloChecksum"
                    }
                },
                ["sensor_data"] = new List<object>
                {
                    new Dictionary<string, object>
                    {
                        ["timestamp"] = DateTime.UtcNow,
                        ["raw_reading"] = "bytes:[72,101,108,108,111,82,97,119]", // "HelloRaw"
                        ["calibration"] = "utf8:Calibration Data"
                    }
                },
                ["protocol"] = new Dictionary<string, object>
                {
                    ["header"] = "ascii:HEADER",
                    ["payload_size"] = 1024,
                    ["encrypted_data"] = "base64:RW5jcnlwdGVkUGF5bG9hZA==" // "EncryptedPayload"
                }
            };

            Console.WriteLine("   Original payload (JSON representation):");
            Console.WriteLine($"   {JsonConvert.SerializeObject(complexPayload, Formatting.Indented)}");

            // Process the payload
            var processedPayload = ByteArrayConverter.ProcessObject(complexPayload) as Dictionary<string, object>;

            Console.WriteLine("\n   After byte array conversion:");
            PrintProcessedPayload(processedPayload, "   ");
        }

        private static void ShowEventControllerUsage()
        {
            Console.WriteLine("\n4. EventsController Usage Examples:\n");

            Console.WriteLine("   Basic usage (without conversion):");
            Console.WriteLine("   POST /api/events/sensor_node/binary_data");
            Console.WriteLine("   {");
            Console.WriteLine("     \"sensor_id\": \"TEMP_001\",");
            Console.WriteLine("     \"binary_data\": \"base64:SGVsbG8gV29ybGQ=\"");
            Console.WriteLine("   }");
            Console.WriteLine("   → binary_data remains as string");

            Console.WriteLine("\n   With byte array conversion:");
            Console.WriteLine("   POST /api/events/sensor_node/binary_data?convertByteArrays=true");
            Console.WriteLine("   {");
            Console.WriteLine("     \"sensor_id\": \"TEMP_001\",");
            Console.WriteLine("     \"binary_data\": \"base64:SGVsbG8gV29ybGQ=\"");
            Console.WriteLine("   }");
            Console.WriteLine("   → binary_data becomes byte[] in NodeEvent");

            Console.WriteLine("\n   Validation endpoint:");
            Console.WriteLine("   GET /api/events/byte-array-formats");
            Console.WriteLine("   → Returns supported formats and examples");

            Console.WriteLine("\n   Signature validation:");
            Console.WriteLine("   POST /api/events/validate-byte-signature");
            Console.WriteLine("   { \"signature\": \"base64:SGVsbG8gV29ybGQ=\" }");
            Console.WriteLine("   → Validates signature without processing");
        }

        private static void ShowRealWorldExamples()
        {
            Console.WriteLine("\n5. Real-World Use Cases:\n");

            // IoT Sensor with binary calibration data
            var iotExample = new
            {
                device_id = "ESP32_SENSOR_001",
                readings = new
                {
                    temperature = 23.5,
                    humidity = 65.2
                },
                calibration = new
                {
                    temperature_offset = "bytes:[255,254,0,1]", // Binary calibration data
                    humidity_matrix = "base64:AQIDBAUGBwgJCg==", // 10-byte calibration matrix
                    checksum = "hex:DE-AD-BE-EF", // Readable hex with hyphens
                    device_id_bytes = "hex:45 53 50 33 32" // "ESP32" in hex with spaces
                },
                firmware = new
                {
                    version = "1.0.0",
                    update_available = false,
                    binary_patch = "base64:UGF0Y2hEYXRh" // "PatchData"
                }
            };

            Console.WriteLine("   IoT Sensor Example:");
            Console.WriteLine($"   {JsonConvert.SerializeObject(iotExample, Formatting.Indented)}");

            // Image processing example
            var imageExample = new
            {
                image_id = "IMG_001",
                metadata = new
                {
                    width = 1920,
                    height = 1080,
                    format = "JPEG"
                },
                thumbnail = "base64: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",
                exif_data = "hex:457869660000",
                color_profile = "bytes:[255,216,255,224]"
            };

            Console.WriteLine("\n   Image Processing Example:");
            Console.WriteLine($"   {JsonConvert.SerializeObject(imageExample, Formatting.Indented)}");

            // Protocol communication example
            var protocolExample = new
            {
                message_id = "MSG_001",
                protocol = "MODBUS",
                frame = new
                {
                    header = "hex:01-03-00-00-00-01", // Readable MODBUS header
                    payload = "bytes:[1,3,2,0,100]",
                    checksum = "hex:84-04" // Readable checksum
                },
                encryption = new
                {
                    algorithm = "AES256",
                    key = "base64:SGVsbG9FbmNyeXB0aW9uS2V5", // "HelloEncryptionKey"
                    iv = "hex:00 11 22 33 44 55 66 77 88 99 AA BB CC DD EE FF" // Readable IV with spaces
                }
            };

            Console.WriteLine("\n   Protocol Communication Example:");
            Console.WriteLine($"   {JsonConvert.SerializeObject(protocolExample, Formatting.Indented)}");
        }

        private static void PrintProcessedPayload(Dictionary<string, object> payload, string indent)
        {
            foreach (var kvp in payload)
            {
                if (kvp.Value is byte[] bytes)
                {
                    var asString = Encoding.UTF8.GetString(bytes);
                    Console.WriteLine($"{indent}{kvp.Key}: byte[{bytes.Length}] \"{asString}\"");
                }
                else if (kvp.Value is Dictionary<string, object> nested)
                {
                    Console.WriteLine($"{indent}{kvp.Key}:");
                    PrintProcessedPayload(nested, indent + "  ");
                }
                else if (kvp.Value is List<object> list)
                {
                    Console.WriteLine($"{indent}{kvp.Key}: [");
                    for (int i = 0; i < list.Count; i++)
                    {
                        if (list[i] is Dictionary<string, object> listItem)
                        {
                            Console.WriteLine($"{indent}  [{i}]:");
                            PrintProcessedPayload(listItem, indent + "    ");
                        }
                        else if (list[i] is byte[] listBytes)
                        {
                            var asString = Encoding.UTF8.GetString(listBytes);
                            Console.WriteLine($"{indent}  [{i}]: byte[{listBytes.Length}] \"{asString}\"");
                        }
                        else
                        {
                            Console.WriteLine($"{indent}  [{i}]: {list[i]}");
                        }
                    }
                    Console.WriteLine($"{indent}]");
                }
                else
                {
                    Console.WriteLine($"{indent}{kvp.Key}: {kvp.Value}");
                }
            }
        }
    }
}
