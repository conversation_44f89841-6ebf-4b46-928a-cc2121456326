using KegBridgeCore.Services.BinaryCoder.SchemaParser;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;

namespace KegBridgeCoreTests
{
    [TestFixture]
    public class SchemaParserTests
    {
        [Test]
        public void ParseSimpleStruct_ShouldSucceed()
        {
            var schema = @"
                struct TestStruct {
                    s7.int counter;
                    s7.real value;
                }";

            var result = BinarySchemaParser.Parse(schema);
            
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Structs.Count);
            
            var testStruct = result.Structs[0];
            Assert.AreEqual("TestStruct", testStruct.Name);
            Assert.AreEqual(2, testStruct.Members.Count);
            
            var counterField = testStruct.Members[0] as FieldDeclaration;
            Assert.IsNotNull(counterField);
            Assert.AreEqual("counter", counterField.Name);
            Assert.AreEqual("s7.int", counterField.Type.BaseType);
            
            var valueField = testStruct.Members[1] as FieldDeclaration;
            Assert.IsNotNull(valueField);
            Assert.AreEqual("value", valueField.Name);
            Assert.AreEqual("s7.real", valueField.Type.BaseType);
        }

        [Test]
        public void ParseStructWithArrays_ShouldSucceed()
        {
            var schema = @"
                struct ArrayStruct {
                    s7.real[5] values;
                    s7.byte[10] data;
                }";

            var result = BinarySchemaParser.Parse(schema);
            
            Assert.IsNotNull(result);
            var arrayStruct = result.Structs[0];
            
            var valuesField = arrayStruct.Members[0] as FieldDeclaration;
            Assert.IsNotNull(valuesField);
            Assert.AreEqual("values", valuesField.Name);
            Assert.AreEqual("s7.real", valuesField.Type.BaseType);
            Assert.IsNotNull(valuesField.Type.ArraySpec);
            Assert.AreEqual(5, valuesField.Type.ArraySpec.Size);
            
            var dataField = arrayStruct.Members[1] as FieldDeclaration;
            Assert.IsNotNull(dataField);
            Assert.AreEqual("data", dataField.Name);
            Assert.AreEqual("s7.byte", dataField.Type.BaseType);
            Assert.IsNotNull(dataField.Type.ArraySpec);
            Assert.AreEqual(10, dataField.Type.ArraySpec.Size);
        }

        [Test]
        public void ParseStructWithTypeModifiers_ShouldSucceed()
        {
            var schema = @"
                struct ModifierStruct {
                    be s7.real big_endian_value;
                    le s7.dword little_endian_value;
                    align(4) s7.int aligned_value;
                }";

            var result = BinarySchemaParser.Parse(schema);
            
            Assert.IsNotNull(result);
            var modifierStruct = result.Structs[0];
            
            var beField = modifierStruct.Members[0] as FieldDeclaration;
            Assert.IsNotNull(beField);
            Assert.AreEqual("big_endian_value", beField.Name);
            Assert.AreEqual("s7.real", beField.Type.BaseType);
            Assert.AreEqual(1, beField.Type.Modifiers.Count);
            Assert.IsInstanceOf<EndiannessModifier>(beField.Type.Modifiers[0]);
            Assert.AreEqual("be", ((EndiannessModifier)beField.Type.Modifiers[0]).Endianness);
            
            var leField = modifierStruct.Members[1] as FieldDeclaration;
            Assert.IsNotNull(leField);
            Assert.AreEqual("little_endian_value", leField.Name);
            Assert.AreEqual("s7.dword", leField.Type.BaseType);
            Assert.AreEqual(1, leField.Type.Modifiers.Count);
            Assert.IsInstanceOf<EndiannessModifier>(leField.Type.Modifiers[0]);
            Assert.AreEqual("le", ((EndiannessModifier)leField.Type.Modifiers[0]).Endianness);
            
            var alignField = modifierStruct.Members[2] as FieldDeclaration;
            Assert.IsNotNull(alignField);
            Assert.AreEqual("aligned_value", alignField.Name);
            Assert.AreEqual("s7.int", alignField.Type.BaseType);
            Assert.AreEqual(1, alignField.Type.Modifiers.Count);
            Assert.IsInstanceOf<AlignmentModifier>(alignField.Type.Modifiers[0]);
            Assert.AreEqual(4, ((AlignmentModifier)alignField.Type.Modifiers[0]).Alignment);
        }

        [Test]
        public void ParseStructWithComments_ShouldSucceed()
        {
            var schema = @"
                // This is a test struct
                struct CommentStruct {
                    s7.int counter;     // Line comment
                    /* Block comment */ s7.real value;
                }";

            var result = BinarySchemaParser.Parse(schema);
            
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Structs.Count);
            
            var commentStruct = result.Structs[0];
            Assert.AreEqual("CommentStruct", commentStruct.Name);
            Assert.AreEqual(2, commentStruct.Members.Count);
        }

        [Test]
        public void ParseMultipleStructs_ShouldSucceed()
        {
            var schema = @"
                struct FirstStruct {
                    s7.int value1;
                }
                
                struct SecondStruct {
                    s7.real value2;
                }";

            var result = BinarySchemaParser.Parse(schema);
            
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Structs.Count);
            Assert.AreEqual("FirstStruct", result.Structs[0].Name);
            Assert.AreEqual("SecondStruct", result.Structs[1].Name);
        }

        [Test]
        public void ConvertToSchemas_SimpleStruct_ShouldSucceed()
        {
            var schema = @"
                struct TestStruct {
                    s7.int counter;
                    s7.real value;
                }";

            var schemas = SchemaConverter.ParseAndConvert(schema);
            
            Assert.IsNotNull(schemas);
            Assert.IsTrue(schemas.ContainsKey("TestStruct"));
            
            var testStruct = schemas["TestStruct"] as Dictionary<string, object>;
            Assert.IsNotNull(testStruct);
            Assert.AreEqual("s7.int", testStruct["counter"]);
            Assert.AreEqual("s7.real", testStruct["value"]);
        }

        [Test]
        public void ConvertToSchemas_WithArrays_ShouldSucceed()
        {
            var schema = @"
                struct ArrayStruct {
                    s7.real[5] values;
                }";

            var schemas = SchemaConverter.ParseAndConvert(schema);
            
            Assert.IsNotNull(schemas);
            var arrayStruct = schemas["ArrayStruct"] as Dictionary<string, object>;
            Assert.IsNotNull(arrayStruct);
            
            var valuesField = arrayStruct["values"] as Dictionary<string, object>;
            Assert.IsNotNull(valuesField);
            Assert.AreEqual(5, valuesField["$array"]);
            Assert.AreEqual("s7.real", valuesField["$refs"]);
        }

        [Test]
        public void ConvertToSchemas_WithEndianness_ShouldSucceed()
        {
            var schema = @"
                struct EndiannessStruct {
                    be s7.real big_value;
                    le s7.dword little_value;
                }";

            var schemas = SchemaConverter.ParseAndConvert(schema);
            
            Assert.IsNotNull(schemas);
            var endiannessStruct = schemas["EndiannessStruct"] as Dictionary<string, object>;
            Assert.IsNotNull(endiannessStruct);
            
            Assert.AreEqual("be.real", endiannessStruct["big_value"]);
            Assert.AreEqual("le.dword", endiannessStruct["little_value"]);
        }

        [Test]
        public void TryParse_InvalidSyntax_ShouldFail()
        {
            var schema = @"
                struct InvalidStruct {
                    s7.int counter  // Missing semicolon
                }";

            var success = BinarySchemaParser.TryParse(schema, out var result, out var error);
            
            Assert.IsFalse(success);
            Assert.IsNull(result);
            Assert.IsNotNull(error);
        }

        [Test]
        public void ParseComplexExample_ShouldMatchExistingFormat()
        {
            var schema = @"
                // Main data structure for PLC communication
                struct udt_kb_ce {
                    s7.dint counter;
                    
                    struct seq {
                        s7.int msg_id;
                        s7.int msg_step;
                        s7.int msg_stat;
                        s7.int active_step;
                        s7.real val;
                    };
                    
                    struct CE_IO {
                        s7.dword di_do;
                        s7.real[5] values;
                    };
                    
                    struct ce_status {
                        s7.int Action;
                        s7.int Status;
                    };
                }";

            var schemas = SchemaConverter.ParseAndConvert(schema);
            
            Assert.IsNotNull(schemas);
            Assert.IsTrue(schemas.ContainsKey("udt_kb_ce"));
            
            var udtStruct = schemas["udt_kb_ce"] as Dictionary<string, object>;
            Assert.IsNotNull(udtStruct);
            
            // Check counter field
            Assert.AreEqual("s7.dint", udtStruct["counter"]);
            
            // Check nested seq struct
            var seqStruct = udtStruct["seq"] as Dictionary<string, object>;
            Assert.IsNotNull(seqStruct);
            Assert.AreEqual("s7.int", seqStruct["msg_id"]);
            Assert.AreEqual("s7.real", seqStruct["val"]);
            
            // Check CE_IO struct with array
            var ceIoStruct = udtStruct["CE_IO"] as Dictionary<string, object>;
            Assert.IsNotNull(ceIoStruct);
            Assert.AreEqual("s7.dword", ceIoStruct["di_do"]);
            
            var valuesArray = ceIoStruct["values"] as Dictionary<string, object>;
            Assert.IsNotNull(valuesArray);
            Assert.AreEqual(5, valuesArray["$array"]);
            Assert.AreEqual("s7.real", valuesArray["$refs"]);
        }
    }
}
