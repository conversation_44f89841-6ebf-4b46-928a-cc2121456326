using KegBridgeCore.Services.BinaryCoder.SchemaParser;
using NUnit.Framework;
using System.Collections.Generic;

namespace KegBridgeCoreTests
{
    [TestFixture]
    public class SimpleBinarySchemaDemo
    {
        [Test]
        public void DemonstrateSchemaParserConcept()
        {
            // This demonstrates the concept of what we're building
            // A Parlot parser that can parse struct-like binary schema definitions
            
            var schemaText = @"
                struct TestStruct {
                    s7.int counter;
                    s7.real value;
                    s7.real[5] values;
                }";

            // For now, let's manually create what the parser would produce
            var expectedSchema = new SchemaFile
            {
                Structs = new List<StructDeclaration>
                {
                    new StructDeclaration
                    {
                        Name = "TestStruct",
                        Members = new List<IMember>
                        {
                            new FieldDeclaration
                            {
                                Name = "counter",
                                Type = new FieldType { BaseType = "s7.int" }
                            },
                            new FieldDeclaration
                            {
                                Name = "value",
                                Type = new FieldType { BaseType = "s7.real" }
                            },
                            new FieldDeclaration
                            {
                                Name = "values",
                                Type = new FieldType 
                                { 
                                    BaseType = "s7.real",
                                    ArraySpec = new ArraySpecification { Size = 5 }
                                }
                            }
                        }
                    }
                }
            };

            // Convert to the format expected by the binary decoder
            var schemas = SchemaConverter.ConvertToSchemas(expectedSchema);
            
            Assert.IsNotNull(schemas);
            Assert.IsTrue(schemas.ContainsKey("TestStruct"));
            
            var testStruct = schemas["TestStruct"] as Dictionary<string, object>;
            Assert.IsNotNull(testStruct);
            
            // Check simple fields
            Assert.AreEqual("s7.int", testStruct["counter"]);
            Assert.AreEqual("s7.real", testStruct["value"]);
            
            // Check array field
            var valuesField = testStruct["values"] as Dictionary<string, object>;
            Assert.IsNotNull(valuesField);
            Assert.AreEqual(5, valuesField["$array"]);
            Assert.AreEqual("s7.real", valuesField["$refs"]);
            Assert.AreEqual(0, valuesField["$offset"]);
        }

        [Test]
        public void DemonstrateNestedStructConcept()
        {
            // Demonstrate nested struct concept
            var expectedSchema = new SchemaFile
            {
                Structs = new List<StructDeclaration>
                {
                    new StructDeclaration
                    {
                        Name = "MainStruct",
                        Members = new List<IMember>
                        {
                            new FieldDeclaration
                            {
                                Name = "counter",
                                Type = new FieldType { BaseType = "s7.int" }
                            },
                            new NestedStructDeclaration
                            {
                                Name = "nested",
                                Members = new List<IMember>
                                {
                                    new FieldDeclaration
                                    {
                                        Name = "value1",
                                        Type = new FieldType { BaseType = "s7.real" }
                                    },
                                    new FieldDeclaration
                                    {
                                        Name = "value2",
                                        Type = new FieldType { BaseType = "s7.real" }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            var schemas = SchemaConverter.ConvertToSchemas(expectedSchema);
            
            Assert.IsNotNull(schemas);
            Assert.IsTrue(schemas.ContainsKey("MainStruct"));
            
            var mainStruct = schemas["MainStruct"] as Dictionary<string, object>;
            Assert.IsNotNull(mainStruct);
            
            Assert.AreEqual("s7.int", mainStruct["counter"]);
            
            var nestedStruct = mainStruct["nested"] as Dictionary<string, object>;
            Assert.IsNotNull(nestedStruct);
            Assert.AreEqual("s7.real", nestedStruct["value1"]);
            Assert.AreEqual("s7.real", nestedStruct["value2"]);
        }

        [Test]
        public void DemonstrateTypeModifiersConcept()
        {
            // Demonstrate type modifiers concept
            var expectedSchema = new SchemaFile
            {
                Structs = new List<StructDeclaration>
                {
                    new StructDeclaration
                    {
                        Name = "ModifierStruct",
                        Members = new List<IMember>
                        {
                            new FieldDeclaration
                            {
                                Name = "big_endian_value",
                                Type = new FieldType 
                                { 
                                    BaseType = "s7.real",
                                    Modifiers = new List<TypeModifier>
                                    {
                                        new EndiannessModifier { Endianness = "be" }
                                    }
                                }
                            },
                            new FieldDeclaration
                            {
                                Name = "aligned_value",
                                Type = new FieldType 
                                { 
                                    BaseType = "s7.int",
                                    Modifiers = new List<TypeModifier>
                                    {
                                        new AlignmentModifier { Alignment = 4 }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            var schemas = SchemaConverter.ConvertToSchemas(expectedSchema);
            
            Assert.IsNotNull(schemas);
            var modifierStruct = schemas["ModifierStruct"] as Dictionary<string, object>;
            Assert.IsNotNull(modifierStruct);
            
            // Endianness modifier should change the type
            Assert.AreEqual("be.real", modifierStruct["big_endian_value"]);
            
            // Alignment modifier is currently ignored in conversion but could be extended
            Assert.AreEqual("s7.int", modifierStruct["aligned_value"]);
        }

        [Test]
        public void DemonstrateIntegrationWithExistingBinaryDecoder()
        {
            // Show how this would integrate with the existing binary decoder
            var schemas = new Dictionary<string, object>
            {
                ["TestStruct"] = new Dictionary<string, object>
                {
                    ["counter"] = "s7.int",
                    ["value"] = "s7.real",
                    ["values"] = new Dictionary<string, object>
                    {
                        ["$array"] = 5,
                        ["$refs"] = "s7.byte",  // Use s7.byte like in the existing examples
                        ["$offset"] = 0
                    }
                }
            };

            // This would initialize the binary decoder with our parsed schemas
            KegBridgeCore.Services.BinaryCoder.Schemas.Initialize(schemas);

            // Verify the decoder catalog was populated
            Assert.IsTrue(KegBridgeCore.Services.BinaryCoder.Schemas.DecodeCatalog.ContainsKey("TestStruct"));

            // The decoder can now be used to decode binary data using the "TestStruct" schema
            // byte[] binaryData = ...;
            // var decoded = KegBridgeCore.Services.BinaryCoder.Schemas.Decode("TestStruct", binaryData);
        }
    }
}
