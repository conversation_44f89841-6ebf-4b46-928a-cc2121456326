using KegBridgeCore.Services.BinaryCoder.SchemaParser;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;

namespace KegBridgeCoreTests
{
    [TestFixture]
    public class AstBinaryDecoderTests
    {
        [Test]
        public void ParseAndDecodeSimpleStruct_ShouldWork()
        {
            var schemaText = @"
                struct TestStruct {
                    s7.int counter;
                    s7.real value;
                }";

            // Parse the schema
            var schemaFile = BinarySchemaParser.Parse(schemaText);
            Assert.IsNotNull(schemaFile);
            Assert.AreEqual(1, schemaFile.Structs.Count);
            
            var testStruct = schemaFile.Structs[0];
            Assert.AreEqual("TestStruct", testStruct.Name);
            Assert.AreEqual(2, testStruct.Members.Count);

            // Initialize AST decoder
            AstSchemas.Initialize(schemaFile);
            Assert.IsTrue(AstSchemas.DecodeCatalog.ContainsKey("TestStruct"));

            // Create test binary data
            // s7.int (2 bytes, big endian): 1234 = 0x04D2
            // s7.real (4 bytes, big endian): 123.45f = 0x42F6E666
            var testData = new byte[] { 0x04, 0xD2, 0x42, 0xF6, 0xE6, 0x66 };

            // Decode the data
            dynamic result = AstSchemas.Decode("TestStruct", testData);
            Assert.IsNotNull(result);
            
            // Verify the decoded values
            Assert.AreEqual("TestStruct", result.__schema);
            Assert.AreEqual(1234, (int)result.counter);
            Assert.AreEqual(123.45f, (float)result.value, 0.01f);
        }

        [Test]
        public void ParseAndDecodeStructWithArray_ShouldWork()
        {
            var schemaText = @"
                struct ArrayStruct {
                    s7.int count;
                    s7.byte[3] data;
                }";

            var schemaFile = BinarySchemaParser.Parse(schemaText);
            AstSchemas.Initialize(schemaFile);

            // Create test data: count=5, data=[0x10, 0x20, 0x30]
            var testData = new byte[] { 0x00, 0x05, 0x10, 0x20, 0x30 };

            dynamic result = AstSchemas.Decode("ArrayStruct", testData);
            Assert.IsNotNull(result);
            
            Assert.AreEqual(5, (int)result.count);
            
            var dataArray = result.data as List<object>;
            Assert.IsNotNull(dataArray);
            Assert.AreEqual(3, dataArray.Count);
            Assert.AreEqual((byte)0x10, dataArray[0]);
            Assert.AreEqual((byte)0x20, dataArray[1]);
            Assert.AreEqual((byte)0x30, dataArray[2]);
        }

        [Test]
        public void ParseAndDecodeNestedStruct_ShouldWork()
        {
            var schemaText = @"
                struct MainStruct {
                    s7.int counter;
                    struct nested {
                        s7.real value1;
                        s7.real value2;
                    };
                }";

            var schemaFile = BinarySchemaParser.Parse(schemaText);
            AstSchemas.Initialize(schemaFile);

            // Create test data
            // counter: 100 = 0x0064
            // value1: 1.5f = 0x3FC00000
            // value2: 2.5f = 0x40200000
            var testData = new byte[] { 
                0x00, 0x64,                         // counter
                0x3F, 0xC0, 0x00, 0x00,            // value1
                0x40, 0x20, 0x00, 0x00             // value2
            };

            dynamic result = AstSchemas.Decode("MainStruct", testData);
            Assert.IsNotNull(result);
            
            Assert.AreEqual(100, (int)result.counter);
            
            dynamic nested = result.nested;
            Assert.IsNotNull(nested);
            Assert.AreEqual(1.5f, (float)nested.value1, 0.01f);
            Assert.AreEqual(2.5f, (float)nested.value2, 0.01f);
        }

        [Test]
        public void ParseAndDecodeWithTypeModifiers_ShouldWork()
        {
            var schemaText = @"
                struct ModifierStruct {
                    be s7.real big_endian_value;
                    le s7.dword little_endian_value;
                }";

            var schemaFile = BinarySchemaParser.Parse(schemaText);
            AstSchemas.Initialize(schemaFile);

            // Create test data
            // be s7.real: 3.14f = 0x4048F5C3 (big endian)
            // le s7.dword: 0x12345678 (stored as little endian: 0x78563412)
            var testData = new byte[] { 
                0x40, 0x48, 0xF5, 0xC3,            // big endian real
                0x78, 0x56, 0x34, 0x12             // little endian dword
            };

            dynamic result = AstSchemas.Decode("ModifierStruct", testData);
            Assert.IsNotNull(result);
            
            Assert.AreEqual(3.14f, (float)result.big_endian_value, 0.01f);
            Assert.AreEqual(0x12345678u, (uint)result.little_endian_value);
        }

        [Test]
        public void ParseComplexSchema_ShouldWork()
        {
            var schemaText = @"
                // Main data structure for PLC communication
                struct udt_kb_ce {
                    s7.dint counter;
                    
                    struct seq {
                        s7.int msg_id;
                        s7.int msg_step;
                        s7.real val;
                    };
                    
                    struct CE_IO {
                        s7.dword di_do;
                        s7.byte[3] values;
                    };
                }";

            var schemaFile = BinarySchemaParser.Parse(schemaText);
            Assert.IsNotNull(schemaFile);
            Assert.AreEqual(1, schemaFile.Structs.Count);
            
            var udtStruct = schemaFile.Structs[0];
            Assert.AreEqual("udt_kb_ce", udtStruct.Name);
            Assert.AreEqual(3, udtStruct.Members.Count);

            // Verify structure
            var counterField = udtStruct.Members[0] as FieldDeclaration;
            Assert.IsNotNull(counterField);
            Assert.AreEqual("counter", counterField.Name);
            Assert.AreEqual("s7.dint", counterField.Type.BaseType);

            var seqStruct = udtStruct.Members[1] as NestedStructDeclaration;
            Assert.IsNotNull(seqStruct);
            Assert.AreEqual("seq", seqStruct.Name);
            Assert.AreEqual(3, seqStruct.Members.Count);

            var ceIoStruct = udtStruct.Members[2] as NestedStructDeclaration;
            Assert.IsNotNull(ceIoStruct);
            Assert.AreEqual("CE_IO", ceIoStruct.Name);
            Assert.AreEqual(2, ceIoStruct.Members.Count);

            // Test decoding
            AstSchemas.Initialize(schemaFile);

            // Create test data
            var testData = new byte[] { 
                0x00, 0x00, 0x03, 0xE8,            // counter: 1000 (s7.dint)
                0x00, 0x01,                         // msg_id: 1 (s7.int)
                0x00, 0x02,                         // msg_step: 2 (s7.int)
                0x42, 0x48, 0x00, 0x00,            // val: 50.0f (s7.real)
                0x12, 0x34, 0x56, 0x78,            // di_do: 0x12345678 (s7.dword)
                0xAA, 0xBB, 0xCC                   // values: [0xAA, 0xBB, 0xCC]
            };

            dynamic result = AstSchemas.Decode("udt_kb_ce", testData);
            Assert.IsNotNull(result);
            
            Assert.AreEqual(1000, (int)result.counter);
            
            dynamic seq = result.seq;
            Assert.IsNotNull(seq);
            Assert.AreEqual(1, (int)seq.msg_id);
            Assert.AreEqual(2, (int)seq.msg_step);
            Assert.AreEqual(50.0f, (float)seq.val, 0.01f);
            
            dynamic ceIo = result.CE_IO;
            Assert.IsNotNull(ceIo);
            Assert.AreEqual(0x12345678u, (uint)ceIo.di_do);
            
            var values = ceIo.values as List<object>;
            Assert.IsNotNull(values);
            Assert.AreEqual(3, values.Count);
            Assert.AreEqual((byte)0xAA, values[0]);
            Assert.AreEqual((byte)0xBB, values[1]);
            Assert.AreEqual((byte)0xCC, values[2]);
        }

        [Test]
        public void ExtensionMethods_ShouldWork()
        {
            var schemaText = @"
                struct TestStruct {
                    s7.int value;
                }";

            // Test InitializeFromSchemaText
            AstBinaryDecoderExtensions.InitializeFromSchemaText(schemaText);
            Assert.IsTrue(AstSchemas.DecodeCatalog.ContainsKey("TestStruct"));

            // Test DecodeWithAst - TestStruct has both int and real fields
            var testData = new byte[] { 0x01, 0x23, 0x42, 0x48, 0x00, 0x00 }; // counter: 291, value: 50.0f
            dynamic result = AstBinaryDecoderExtensions.DecodeWithAst("TestStruct", testData);
            Assert.IsNotNull(result);
            Assert.AreEqual(291, (int)result.counter);
            Assert.AreEqual(50.0f, (float)result.value, 0.01f);

            // Test AddSchemasFromText
            var additionalSchema = @"
                struct AnotherStruct {
                    s7.byte flag;
                }";
            
            AstBinaryDecoderExtensions.AddSchemasFromText(additionalSchema);
            Assert.IsTrue(AstSchemas.DecodeCatalog.ContainsKey("TestStruct"));
            Assert.IsTrue(AstSchemas.DecodeCatalog.ContainsKey("AnotherStruct"));
        }

        [Test]
        public void ParseError_ShouldThrowException()
        {
            var invalidSchema = @"
                struct InvalidStruct {
                    s7.int counter  // Missing semicolon
                }";

            Assert.Throws<ParseException>(() => BinarySchemaParser.Parse(invalidSchema));
        }

        [Test]
        public void TryParse_InvalidSchema_ShouldReturnFalse()
        {
            var invalidSchema = @"
                struct InvalidStruct {
                    s7.int counter  // Missing semicolon
                }";

            var success = BinarySchemaParser.TryParse(invalidSchema, out var schema, out var error);
            Assert.IsFalse(success);
            Assert.IsNull(schema);
            Assert.IsNotNull(error);
        }
    }
}
