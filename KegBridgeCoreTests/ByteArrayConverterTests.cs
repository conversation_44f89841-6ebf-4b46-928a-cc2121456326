using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using KegBridgeCore.Utilities;
using NUnit.Framework;

namespace KegBridgeCoreTests
{
    [TestFixture]
    public class ByteArrayConverterTests
    {
        private readonly byte[] _testBytes = { 72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100 }; // "Hello World"
        private const string TestString = "Hello World";

        [Test]
        public void TryConvertString_Base64Format_ShouldConvertCorrectly()
        {
            // Arrange
            var base64Input = "base64:SGVsbG8gV29ybGQ="; // "Hello World" in Base64

            // Act
            var success = ByteArrayConverter.TryConvertString(base64Input, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_HexFormat_ShouldConvertCorrectly()
        {
            // Arrange
            var hexInput = "hex:48656C6C6F20576F726C64"; // "Hello World" in hex

            // Act
            var success = ByteArrayConverter.TryConvertString(hexInput, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_HexFormatWithSpaces_ShouldConvertCorrectly()
        {
            // Arrange
            var hexInput = "hex:48 65 6C 6C 6F 20 57 6F 72 6C 64"; // "Hello World" in hex with spaces

            // Act
            var success = ByteArrayConverter.TryConvertString(hexInput, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_HexFormatWithHyphens_ShouldConvertCorrectly()
        {
            // Arrange
            var hexInput = "hex:48-65-6C-6C-6F-20-57-6F-72-6C-64"; // "Hello World" in hex with hyphens

            // Act
            var success = ByteArrayConverter.TryConvertString(hexInput, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_HexFormatMixedSeparators_ShouldConvertCorrectly()
        {
            // Arrange
            var hexInput = "hex:48-65 6C-6C 6F 20-57 6F-72 6C-64"; // "Hello World" in hex with mixed separators

            // Act
            var success = ByteArrayConverter.TryConvertString(hexInput, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_BytesFormat_ShouldConvertCorrectly()
        {
            // Arrange
            var bytesInput = "bytes:[72,101,108,108,111,32,87,111,114,108,100]";

            // Act
            var success = ByteArrayConverter.TryConvertString(bytesInput, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_Utf8Format_ShouldConvertCorrectly()
        {
            // Arrange
            var utf8Input = "utf8:Hello World";

            // Act
            var success = ByteArrayConverter.TryConvertString(utf8Input, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_AsciiFormat_ShouldConvertCorrectly()
        {
            // Arrange
            var asciiInput = "ascii:Hello World";

            // Act
            var success = ByteArrayConverter.TryConvertString(asciiInput, out var result);

            // Assert
            Assert.That(success, Is.True);
            Assert.That(result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void TryConvertString_InvalidFormat_ShouldReturnFalse()
        {
            // Arrange
            var invalidInput = "invalid:Hello World";

            // Act
            var success = ByteArrayConverter.TryConvertString(invalidInput, out var result);

            // Assert
            Assert.That(success, Is.False);
            Assert.That(result, Is.Null);
        }

        [Test]
        public void TryConvertString_InvalidBase64_ShouldReturnFalse()
        {
            // Arrange
            var invalidBase64 = "base64:InvalidBase64!@#";

            // Act
            var success = ByteArrayConverter.TryConvertString(invalidBase64, out var result);

            // Assert
            Assert.That(success, Is.False);
            Assert.That(result, Is.Null);
        }

        [Test]
        public void TryConvertString_InvalidHex_ShouldReturnFalse()
        {
            // Arrange
            var invalidHex = "hex:48656C6C6F20576F726C6"; // Odd length

            // Act
            var success = ByteArrayConverter.TryConvertString(invalidHex, out var result);

            // Assert
            Assert.That(success, Is.False);
            Assert.That(result, Is.Null);
        }

        [Test]
        public void TryConvertString_InvalidHexWithSpaces_ShouldReturnFalse()
        {
            // Arrange
            var invalidHex = "hex:48 65 6C 6C 6F 20 57 6F 72 6C 6"; // Odd length after removing spaces

            // Act
            var success = ByteArrayConverter.TryConvertString(invalidHex, out var result);

            // Assert
            Assert.That(success, Is.False);
            Assert.That(result, Is.Null);
        }

        [Test]
        public void TryConvertString_InvalidHexCharacters_ShouldReturnFalse()
        {
            // Arrange
            var invalidHex = "hex:48-65-GG-6C-6F"; // Invalid hex characters

            // Act
            var success = ByteArrayConverter.TryConvertString(invalidHex, out var result);

            // Assert
            Assert.That(success, Is.False);
            Assert.That(result, Is.Null);
        }

        [Test]
        public void TryConvertString_InvalidBytes_ShouldReturnFalse()
        {
            // Arrange
            var invalidBytes = "bytes:[72,101,abc,108,111]"; // 'abc' is not a valid number

            // Act
            var success = ByteArrayConverter.TryConvertString(invalidBytes, out var result);

            // Assert
            Assert.That(success, Is.False);
            Assert.That(result, Is.Null);
        }

        [Test]
        public void ProcessObject_SimpleString_ShouldConvertToByteArray()
        {
            // Arrange
            var input = "base64:SGVsbG8gV29ybGQ=";

            // Act
            var result = ByteArrayConverter.ProcessObject(input);

            // Assert
            Assert.That(result, Is.TypeOf<byte[]>());
            Assert.That((byte[])result, Is.EqualTo(_testBytes));
        }

        [Test]
        public void ProcessObject_Dictionary_ShouldConvertNestedByteArrays()
        {
            // Arrange
            var input = new Dictionary<string, object>
            {
                ["normalString"] = "Hello",
                ["byteData"] = "base64:SGVsbG8gV29ybGQ=",
                ["nested"] = new Dictionary<string, object>
                {
                    ["hexData"] = "hex:48656C6C6F20576F726C64",
                    ["regularValue"] = 42
                }
            };

            // Act
            var result = ByteArrayConverter.ProcessObject(input) as Dictionary<string, object>;

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result["normalString"], Is.EqualTo("Hello"));
            Assert.That(result["byteData"], Is.TypeOf<byte[]>());
            Assert.That((byte[])result["byteData"], Is.EqualTo(_testBytes));

            var nested = result["nested"] as Dictionary<string, object>;
            Assert.That(nested, Is.Not.Null);
            Assert.That(nested["hexData"], Is.TypeOf<byte[]>());
            Assert.That((byte[])nested["hexData"], Is.EqualTo(_testBytes));
            Assert.That(nested["regularValue"], Is.EqualTo(42));
        }

        [Test]
        public void ProcessObject_List_ShouldConvertByteArrayElements()
        {
            // Arrange
            var input = new List<object>
            {
                "normalString",
                "base64:SGVsbG8gV29ybGQ=",
                42,
                "hex:48656C6C6F20576F726C64"
            };

            // Act
            var result = ByteArrayConverter.ProcessObject(input) as List<object>;

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result[0], Is.EqualTo("normalString"));
            Assert.That(result[1], Is.TypeOf<byte[]>());
            Assert.That((byte[])result[1], Is.EqualTo(_testBytes));
            Assert.That(result[2], Is.EqualTo(42));
            Assert.That(result[3], Is.TypeOf<byte[]>());
            Assert.That((byte[])result[3], Is.EqualTo(_testBytes));
        }

        [Test]
        public void BytesToBase64Signature_ShouldCreateValidSignature()
        {
            // Act
            var signature = ByteArrayConverter.BytesToBase64Signature(_testBytes);

            // Assert
            Assert.That(signature, Is.EqualTo("base64:SGVsbG8gV29ybGQ="));
        }

        [Test]
        public void BytesToHexSignature_ShouldCreateValidSignature()
        {
            // Act
            var signature = ByteArrayConverter.BytesToHexSignature(_testBytes);

            // Assert
            Assert.That(signature, Is.EqualTo("hex:48656C6C6F20576F726C64"));
        }

        [Test]
        public void BytesToBytesSignature_ShouldCreateValidSignature()
        {
            // Act
            var signature = ByteArrayConverter.BytesToBytesSignature(_testBytes);

            // Assert
            Assert.That(signature, Is.EqualTo("bytes:[72,101,108,108,111,32,87,111,114,108,100]"));
        }

        [Test]
        public void GetSignatureFormat_ShouldIdentifyFormatsCorrectly()
        {
            // Arrange & Act & Assert
            Assert.That(ByteArrayConverter.GetSignatureFormat("base64:SGVsbG8gV29ybGQ="), Is.EqualTo("base64"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("hex:48656C6C6F20576F726C64"), Is.EqualTo("hex"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("hex:48-65-6C-6C-6F"), Is.EqualTo("hex"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("hex:48 65 6C 6C 6F"), Is.EqualTo("hex"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("hex:48-65 6C-6C 6F"), Is.EqualTo("hex"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("bytes:[72,101,108]"), Is.EqualTo("bytes"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("utf8:Hello"), Is.EqualTo("utf8"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("ascii:Hello"), Is.EqualTo("ascii"));
            Assert.That(ByteArrayConverter.GetSignatureFormat("invalid:Hello"), Is.Null);
        }

        [Test]
        public void IsValidByteArraySignature_ShouldValidateCorrectly()
        {
            // Arrange & Act & Assert
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("base64:SGVsbG8gV29ybGQ="), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("hex:48656C6C6F20576F726C64"), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("hex:48-65-6C-6C-6F"), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("hex:48 65 6C 6C 6F"), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("hex:48-65 6C-6C 6F"), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("bytes:[72,101,108]"), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("utf8:Hello"), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("ascii:Hello"), Is.True);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature("invalid:Hello"), Is.False);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature(""), Is.False);
            Assert.That(ByteArrayConverter.IsValidByteArraySignature(null), Is.False);
        }

        [Test]
        public void GetSupportedFormats_ShouldReturnAllFormats()
        {
            // Act
            var formats = ByteArrayConverter.GetSupportedFormats();

            // Assert
            Assert.That(formats.Count, Is.EqualTo(7)); // Updated count for new hex formats
            Assert.That(formats.ContainsKey("base64"), Is.True);
            Assert.That(formats.ContainsKey("hex"), Is.True);
            Assert.That(formats.ContainsKey("hex_spaced"), Is.True);
            Assert.That(formats.ContainsKey("hex_hyphenated"), Is.True);
            Assert.That(formats.ContainsKey("bytes"), Is.True);
            Assert.That(formats.ContainsKey("utf8"), Is.True);
            Assert.That(formats.ContainsKey("ascii"), Is.True);
        }

        [Test]
        public void ProcessObject_ComplexNestedStructure_ShouldConvertAllByteArrays()
        {
            // Arrange
            var input = new Dictionary<string, object>
            {
                ["sensor"] = new Dictionary<string, object>
                {
                    ["id"] = "SENSOR_001",
                    ["calibration_data"] = "base64:SGVsbG8gV29ybGQ=",
                    ["readings"] = new List<object>
                    {
                        new Dictionary<string, object>
                        {
                            ["timestamp"] = DateTime.UtcNow,
                            ["raw_data"] = "hex:48656C6C6F20576F726C64",
                            ["metadata"] = "utf8:Sensor Reading"
                        }
                    }
                },
                ["protocol"] = new Dictionary<string, object>
                {
                    ["header"] = "bytes:[72,101,108,108,111]",
                    ["payload"] = "ascii:World"
                }
            };

            // Act
            var result = ByteArrayConverter.ProcessObject(input) as Dictionary<string, object>;

            // Assert
            Assert.That(result, Is.Not.Null);
            
            var sensor = result["sensor"] as Dictionary<string, object>;
            Assert.That(sensor["calibration_data"], Is.TypeOf<byte[]>());
            
            var readings = sensor["readings"] as List<object>;
            var reading = readings[0] as Dictionary<string, object>;
            Assert.That(reading["raw_data"], Is.TypeOf<byte[]>());
            Assert.That(reading["metadata"], Is.TypeOf<byte[]>());
            
            var protocol = result["protocol"] as Dictionary<string, object>;
            Assert.That(protocol["header"], Is.TypeOf<byte[]>());
            Assert.That(protocol["payload"], Is.TypeOf<byte[]>());
        }
    }
}
