using System.Dynamic;
using System.Reflection;
using Dapper.FastCrud;
using Dapper.Logging;
using Dapper.Logging.Configuration;
using Destructurama;
using KegInsight.Services;
using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Npgsql;
using Serilog;
using Serilog.Debugging;
using Serilog.Exceptions;
using FluentMigrator.Runner;
using FluentMigrator.Runner.Initialization;

OrmConfiguration.DefaultDialect = SqlDialect.PostgreSql;

JsonConvert.DefaultSettings = () => new JsonSerializerSettings
{
    Formatting = Formatting.Indented,
    ContractResolver = new DefaultContractResolver { NamingStrategy = new SnakeCaseNamingStrategy() }
};

SelfLog.Enable(Console.Error);

var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";

var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile(path: "appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{environment}.json", optional: true)
    .Build();

var baseLogger = new LoggerConfiguration()
    .ReadFrom.Configuration(configuration)
    .Destructure.ToMaximumDepth(100)
    .Destructure.UsingAttributes()
    .Destructure.JsonNetTypes()
    // https://stackoverflow.com/questions/48958444/serilog-and-expandoobject 
    .Destructure.ByTransforming<ExpandoObject>(e => new Dictionary<string, object>(e))
    .Enrich.WithThreadId()
    .Enrich.FromLogContext()
    .Enrich.WithExceptionDetails()
    .Enrich.WithProperty("application", "keg_insight")
    .Enrich.WithProperty("startup", DateTime.Now.ToString("yyyyMMddHHmm"))
    .CreateLogger();

Serilog.Log.Logger = baseLogger;

var logger = baseLogger.ForContext("SourceContext", "KegBridgeCore.Main");

try
{
    logger.Information("Starting Webhost for {environment}", environment);

    var builder = WebApplication.CreateBuilder(args);
    builder.Services.AddSerilog();

// Add services to the container.
    builder.Services.AddControllersWithViews();
    builder.Services.AddHttpContextAccessor();
    builder.Services.AddTransient<IRazorPartialToStringRenderer, RazorPartialToStringRenderer>();
    builder.Services.AddSignalR();
    if( builder.Configuration.GetConnectionString("DatabaseContext") != null )
    {
        builder.Services.AddDbConnectionFactory(
            sp => new NpgsqlConnection(builder.Configuration.GetConnectionString("DatabaseContext")),
            options => options
                .WithLogLevel(LogLevel.Debug)
                .WithSensitiveDataLogging() //to show values of the query parameters
                .WithConnectionProjector(c => new { c.DataSource, c.Database })
            , ServiceLifetime.Scoped);

        builder.Services.AddFluentMigratorCore()
            .ConfigureRunner(rb => rb
                .AddPostgres()
                // Set the connection string
                .WithGlobalConnectionString(builder.Configuration.GetConnectionString("DatabaseContext"))
                // Define the assembly containing the migrations, maintenance migrations and other customizations
                .ScanIn(typeof(Program).Assembly).For.All())
            // Enable logging to console in the FluentMigrator way
            .AddLogging(lb => lb.AddFluentMigratorConsole());
    }
    else
    {
        logger.Warning("No database connection string found in configuration");
    }

    var app = builder.Build();

    using (IServiceScope serviceScope = app.Services.CreateScope())
    {
        try
        {
            var serviceProvider = serviceScope.ServiceProvider;
            var runner = serviceProvider.GetRequiredService<IMigrationRunner>();
            
            // Execute the migrations
            runner.MigrateUp();
        }
        catch (System.InvalidOperationException ex)
        {
            logger.Warning("No database migrations done.");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error migrating database");
        }
    }
    

// Configure the HTTP request pipeline.
    if (!app.Environment.IsDevelopment())
    {
        app.UseExceptionHandler("/Home/Error");
        // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
        app.UseHsts();
    }

    app.UseHttpsRedirection();
    app.UseRouting();

    app.UseAuthorization();

    app.MapStaticAssets();

    app.MapControllerRoute(
            name: "default",
            pattern: "{controller=Home}/{action=Index}/{id?}")
        .WithStaticAssets();

    app.Start();

    app.WaitForShutdown();

    logger.Information("Exit");

    return 0;
}
catch (Exception ex)
{
    logger.Fatal(ex, "Host terminated unexpectedly");
    return 1;
}
finally
{
    Log.CloseAndFlush();
}
