import * as Turbo from '@hotwired/turbo'

Turbo.session.drive = true

import 'simplebar'
import 'bootstrap'

import './signalRTurboStreamElement'
import './controllers'

import AppComponents from './app_components.js'
import AppLayout from './app_layout.js'

document.addEventListener('DOMContentLoaded', () => {
    console.log("application.js: DOMContentLoaded");
    
});

document.addEventListener("turbo:load", function () {
    console.log("application.js:", "turbo:load");
    
    new AppComponents().init();
    new AppLayout().init();
});

document.addEventListener("turbo:submit-start", function (event) {
    console.log("application.js: turbo:submit-start", event);
});

document.addEventListener("turbo:submit-end", function (event) {
    console.log("application.js: turbo:submit-end", event);
});

