const STORAGEKEY = "KEG_INSIGHT_CONFIG";

class AppLayout {
    
    constructor() {
        this.html = document.documentElement;
        this.config = {
            theme: "system",
            sidenav: {
                size: "default"
            }
        };
    }

    init() {
        this.initConfig();
        this.initSwitchListener();
        this.initWindowSize();
        this._adjustLayout();
    }

    initConfig() {
        const savedConfig = sessionStorage.getItem(STORAGEKEY);
        this.config = savedConfig ? JSON.parse(savedConfig) : this.config;
        
        // this.defaultConfig = JSON.parse(JSON.stringify(window.defaultConfig));
        // this.config = JSON.parse(JSON.stringify(window.config));
    }
    
    storeConfig() {
        sessionStorage.setItem(STORAGEKEY, JSON.stringify(this.config));
    }

    changeLeftbarSize(size, save = true) {
        this.html.setAttribute("data-sidenav-size", size);
        if (save) {
            this.config.sidenav.size = size;
        }
        this.storeConfig();
    }

    changeTheme(color) {
        const nColor = color === 'system' ? this.getSystemTheme() : color;
        this.config.theme = color
        this.html.setAttribute("data-bs-theme", nColor);
        this.storeConfig();
    }

    getSystemTheme() {
        return window.matchMedia("(prefers-color-scheme: dark)").matches
            ? "dark"
            : "light";
    }

    initSwitchListener() {
        const themeToggle = document.getElementById("light-dark-mode");
        if (themeToggle) {
            themeToggle.addEventListener("click", () => {
                const newTheme = this.config.theme === "light" ? "dark" : "light";
                this.changeTheme(newTheme);
            });
        }

        const toggleBtn = document.querySelector(".sidenav-toggle-button");
        if (toggleBtn) {
            toggleBtn.addEventListener("click", () => this._toggleSidebar());
        }

        const closeBtn = document.querySelector(".button-close-offcanvas");
        if (closeBtn) {
            closeBtn.addEventListener("click", () => {
                this.html.classList.remove("sidebar-enable");
                this.hideBackdrop();
            });
        }

        document.querySelectorAll(".button-on-hover").forEach((el) => {
            el.addEventListener("click", () => {
                const current = this.html.getAttribute("data-sidenav-size");
                this.changeLeftbarSize(
                    current === "on-hover-active" ? "on-hover" : "on-hover-active",
                    true
                );
            });
        });
    }

    _toggleSidebar() {
        const current = this.html.getAttribute("data-sidenav-size");
        const isOffcanvas = current === "offcanvas";
        const configSize = this.config.sidenav.size;

        if (isOffcanvas) {
            this.showBackdrop();
        } else if (configSize === "compact") {
            this.changeLeftbarSize(
                current === "condensed" ? "compact" : "condensed",
                false
            );
        } else {
            this.changeLeftbarSize(
                current === "condensed" ? "default" : "condensed",
                true
            );
        }

        this.html.classList.toggle("sidebar-enable");
    }

    showBackdrop() {
        const backdrop = document.createElement("div");
        backdrop.id = "custom-backdrop";
        backdrop.className = "offcanvas-backdrop fade show";
        document.body.appendChild(backdrop);
        document.body.style.overflow = "hidden";
        if (window.innerWidth > 767) {
            document.body.style.paddingRight = "15px";
        }
        backdrop.addEventListener("click", () => {
            this.html.classList.remove("sidebar-enable");
            this.hideBackdrop();
        });
    }

    hideBackdrop() {
        const backdrop = document.getElementById("custom-backdrop");
        if (backdrop) {
            document.body.removeChild(backdrop);
            document.body.style.overflow = "";
            document.body.style.paddingRight = "";
        }
    }

    _adjustLayout() {
        const width = window.innerWidth;
        const size = this.config.sidenav.size;

        if (width <= 767.98) {
            this.changeLeftbarSize("offcanvas", false);
        } else if (width <= 1140 && !["offcanvas"].includes(size)) {
            this.changeLeftbarSize(
                size === "on-hover" ? "condensed" : "condensed",
                false
            );
        } else {
            this.changeLeftbarSize(size);
        }
    }

    initWindowSize() {
        window.addEventListener("resize", () => this._adjustLayout());
    }
}

export default AppLayout;