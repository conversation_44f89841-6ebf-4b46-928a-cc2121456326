import { application } from "./stimulus_application"

// esbuild-rails enables this
import controllers from "./**/*_controller.js"

application.logFormattedMessage = function(identifier, functionName, detail = {} ) {
    detail = Object.assign({ application: this }, detail);
    this.logger.groupCollapsed(`${identifier}[${detail.element?.tagName}#${detail.element?.id}] -> ${functionName}`);
    this.logger.log("details:", Object.assign({}, detail));
    this.logger.groupEnd();
}
controllers.forEach((controller) => {
    console.log("register controller", controller.name, controller.filename);
    application.register(controller.name, controller.module.default)
})