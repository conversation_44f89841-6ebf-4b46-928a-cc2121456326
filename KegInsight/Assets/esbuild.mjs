#!/usr/bin/env node
//
// `yarn build` - Build JavaScript and exit
// `yarn build --watch` - Rebuild JavaScript on change
//
// Minify is enabled when "RAILS_ENV=production"
// Sourcemaps are enabled in non-production environments

import * as esbuild from "esbuild"
import {sassPlugin} from 'esbuild-sass-plugin'
import rails from "esbuild-rails"

const config = {
    plugins: [
        sassPlugin({
            loadPaths: ["./node_modules"],
            embedded: true
        }),
        rails()
    ],
    external: [
        '/images/*'
    ],
    loader: {
        ".ttf": "file",
        ".eot": "file",
        ".woff2": "file",
        ".woff": "file",
        ".svg": "file",
        ".png": "file",
        ".jpg": "file"
    },
    entryPoints: [
        './js/application.js',
        './css/application.scss'
    ],
    outdir: '../wwwroot/assets',
    target: ['es2020'],
    bundle: true,
    minify: process.env.NODE_ENV === "production",
    metafile: true,
    sourcemap: process.env.NODE_ENV !== "production",
    treeShaking: true,
    logLevel: 'info'
}

if (process.argv.includes("--watch")) {
    let context = await esbuild.context({...config, logLevel: 'info'})
    context.watch()
} else {
    esbuild.build(config)
}