// Dark color mode variables
//
// Custom variables for the `[data-bs-theme="dark"]` theme. Use this as a starting point for your own custom color modes by creating a new theme-specific file like `_variables-dark.scss` and adding the variables you need.

//
// Global colors
//

// scss-docs-start sass-dark-mode-vars
// scss-docs-start theme-text-dark-variables
$primary-text-emphasis-dark:        tint-color($primary, 40%);
$secondary-text-emphasis-dark:      tint-color($secondary, 40%);
$success-text-emphasis-dark:        tint-color($success, 40%);
$info-text-emphasis-dark:           tint-color($info, 40%);
$warning-text-emphasis-dark:        tint-color($warning, 40%);
$danger-text-emphasis-dark:         tint-color($danger, 40%);
$purple-text-emphasis-dark:         tint-color($purple, 40%);
$light-text-emphasis-dark:          $gray-100;
$dark-text-emphasis-dark:           $gray-300;
// scss-docs-end theme-text-dark-variables

$theme-colors-text-dark: (
  "primary":    $primary-text-emphasis-dark,
  "secondary":  $secondary-text-emphasis-dark,
  "success":    $success-text-emphasis-dark,
  "info":       $info-text-emphasis-dark,
  "warning":    $warning-text-emphasis-dark,
  "danger":     $danger-text-emphasis-dark,
  "purple":     $purple-text-emphasis-dark,
  "light":      $light-text-emphasis-dark,
  "dark":       $dark-text-emphasis-dark,
);

// scss-docs-start theme-bg-subtle-dark-variables
$primary-bg-subtle-dark:            rgba($primary, 20%);
$secondary-bg-subtle-dark:          rgba($secondary, 20%);
$success-bg-subtle-dark:            rgba($success, 20%);
$info-bg-subtle-dark:               rgba($info, 20%);
$warning-bg-subtle-dark:            rgba($warning, 20%);
$danger-bg-subtle-dark:             rgba($danger, 20%);
$purple-bg-subtle-dark:             rgba($purple, 20%);
$light-bg-subtle-dark:              #22232c;
$dark-bg-subtle-dark:               mix($gray-800, $black);
// scss-docs-end theme-bg-subtle-dark-variables

$theme-colors-bg-subtle-dark: (
  "primary":    $primary-bg-subtle-dark,
  "secondary":  $secondary-bg-subtle-dark,
  "success":    $success-bg-subtle-dark,
  "info":       $info-bg-subtle-dark,
  "warning":    $warning-bg-subtle-dark,
  "danger":     $danger-bg-subtle-dark,
  "purple":     $purple-bg-subtle-dark,
  "light":      $light-bg-subtle-dark,
  "dark":       $dark-bg-subtle-dark,
);

// scss-docs-start theme-border-subtle-dark-variables
$primary-border-subtle-dark:        shade-color($primary, 40%);
$secondary-border-subtle-dark:      shade-color($secondary, 40%);
$success-border-subtle-dark:        shade-color($success, 40%);
$info-border-subtle-dark:           shade-color($info, 40%);
$warning-border-subtle-dark:        shade-color($warning, 40%);
$danger-border-subtle-dark:         shade-color($danger, 40%);
$purple-border-subtle-dark:         shade-color($purple, 40%);
$light-border-subtle-dark:          $gray-700;
$dark-border-subtle-dark:           $gray-700;
// scss-docs-end theme-border-subtle-dark-variables

$theme-colors-border-subtle-dark: (
  "primary":    $primary-border-subtle-dark,
  "secondary":  $secondary-border-subtle-dark,
  "success":    $success-border-subtle-dark,
  "info":       $info-border-subtle-dark,
  "warning":    $warning-border-subtle-dark,
  "danger":     $danger-border-subtle-dark,
  "purple":     $purple-border-subtle-dark,
  "light":      $light-border-subtle-dark,
  "dark":       $dark-border-subtle-dark,
);

$body-color-dark:                   #aab8c5;
$body-bg-dark:                      #17181e;

$body-secondary-color-dark:         #8391a2;
$body-secondary-bg-dark:            #1e1f27;

$body-tertiary-color-dark:          #aab8c5;
$body-tertiary-bg-dark:             #272832;

$body-emphasis-color-dark:          #b0bbc5;

$border-color-dark:                 #293036;
$border-color-translucent-dark:     #8391a2;

$headings-color-dark:               $body-color-dark;
$link-color-dark:                   tint-color($primary, 40%);
$link-hover-color-dark:             shift-color($link-color-dark, -$link-shade-percentage);
$code-color-dark:                   tint-color($code-color, 40%);

//
// Forms
//

$form-select-indicator-color-dark:  $body-color-dark;
$form-select-indicator-dark:        url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color-dark}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>");

$form-switch-color-dark:            rgba($white, .25);
$form-switch-bg-image-dark:         url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color-dark}'/></svg>");

// scss-docs-start form-validation-colors-dark
$form-valid-color-dark:             $green-300;
$form-valid-border-color-dark:      $green-300;
$form-invalid-color-dark:           $red-300;
$form-invalid-border-color-dark:    $red-300;
// scss-docs-end form-validation-colors-dark


//
// Accordion
//

$accordion-icon-color-dark:         $primary-text-emphasis-dark;
$accordion-icon-active-color-dark:  $primary-text-emphasis-dark;

$accordion-button-icon-dark:         url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color-dark}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>");
$accordion-button-active-icon-dark:  url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color-dark}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>");
// scss-docs-end sass-dark-mode-vars