/**
 * Template Name: INSPINIA - Multipurpose Admin & Dashboard Template
 * By (Author): WebAppLayers
 * Module/App (File Name): App CSS File
 * Version: 4.2.0
*/


// Core files
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";

@import "variables";
@import "variables-dark";

@import "bootstrap/scss/bootstrap";

@import "config/root";
@import "config/theme-classic"; // Default Theme
@import "config/theme-saas";
@import "config/theme-modern";
@import "config/theme-material";
@import "config/theme-minimal";
@import "config/theme-flat";
@import "config/theme-galaxy";

// Structure
@import "structure/topbar";
@import "structure/sidenav";
@import "structure/horizontal";
@import "structure/layout";
@import "structure/footer";

// Components
@import "components/background";
@import "components/accordions";
@import "components/alert";
@import "components/avatar";
@import "components/breadcrumb";
@import "components/buttons";
@import "components/badge";
@import "components/card";
@import "components/dropdown";
@import "components/forms";
@import "components/modal";
@import "components/nav";
@import "components/pagination";
@import "components/popover";
@import "components/print";
@import "components/progress";
@import "components/reboot";
@import "components/tables";
@import "components/tooltip";
@import "components/preloader";
@import "components/widgets";
@import "components/list-group";
