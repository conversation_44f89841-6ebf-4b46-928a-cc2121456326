// 
// classic Theme Mode (Default Theme)
//


@if $theme-classic ==true {

    // Google fonts
    @import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');


    // Background Light left-sidebar
    html[data-menu-color="light"] {
        --#{$prefix}sidenav-bg:                       #{$white};
        --#{$prefix}sidenav-border-color:             #{$gray-300};
        --#{$prefix}sidenav-item-color:               #{$gray-700};
        --#{$prefix}sidenav-item-hover-color:         #{$primary};
        --#{$prefix}sidenav-item-hover-bg:          #e8f7f4;
        --#{$prefix}sidenav-item-active-color:        #{$primary};
        --#{$prefix}sidenav-item-active-bg:         #e8f7f4;
    }

    // Dark Left Sidebar
    html[data-menu-color="dark"] {
        --#{$prefix}sidenav-bg:                     #23303c;
        --#{$prefix}sidenav-border-color:           #23303c;
        --#{$prefix}sidenav-item-color:             #8495ab;
        --#{$prefix}sidenav-item-hover-color:       #bccee4;
        --#{$prefix}sidenav-item-hover-bg:          #2f3742;
        --#{$prefix}sidenav-item-active-color:      #ced6df;
        --#{$prefix}sidenav-item-active-bg:         #1c262f;
    }

    // Background Gray left-sidebar
    html[data-menu-color="gray"] {
        --#{$prefix}sidenav-bg:                       #f1f2f7;
        --#{$prefix}sidenav-border-color:             #e7e9eb;
        --#{$prefix}sidenav-item-color:               #{$gray-700};
        --#{$prefix}sidenav-item-hover-color:         #{$primary};
        --#{$prefix}sidenav-item-hover-bg:          #e8f7f4;
        --#{$prefix}sidenav-item-active-color:        #212b29;
        --#{$prefix}sidenav-item-active-bg:         #e8e8ed;
    }

    // Gradient Menu
    html[data-menu-color="gradient"] {
        --#{$prefix}sidenav-bg:                       #{linear-gradient(135deg, #1a455f, #262549)};
        --#{$prefix}sidenav-border-color:             null;
        --#{$prefix}sidenav-item-color:             #799cb7;
        --#{$prefix}sidenav-item-hover-color:       #bccee4;
        --#{$prefix}sidenav-item-hover-bg:          #2f3742;
        --#{$prefix}sidenav-item-active-color:      #cfdff1;
        --#{$prefix}sidenav-item-active-bg:         #294461;
    }

    // Image Menu
    html[data-menu-color="image"] {
        --#{$prefix}sidenav-bg:                       #{linear-gradient(to bottom right, rgba(16,16,30,0.85), #183c52), url('/images/sidenav-bg.jpg') center/cover no-repeat};
        --#{$prefix}sidenav-border-color:             null;
        --#{$prefix}sidenav-item-color:             #799cb7;
        --#{$prefix}sidenav-item-hover-color:       #bccee4;
        --#{$prefix}sidenav-item-hover-bg:          #2f3742;
        --#{$prefix}sidenav-item-active-color:      #cfdff1;
        --#{$prefix}sidenav-item-active-bg:         #1e3447;
    }

    // Dark Mode
    html[data-bs-theme="dark"][data-menu-color="dark"],
    html[data-bs-theme="dark"][data-menu-color="gray"],
    html[data-bs-theme="dark"][data-menu-color="light"] {
        --#{$prefix}sidenav-bg:                     #1e1f27;
        --#{$prefix}sidenav-border-color:           #2c2d38;
        --#{$prefix}sidenav-item-color:             #8495ab;
        --#{$prefix}sidenav-item-hover-color:       #bccee4;
        --#{$prefix}sidenav-item-hover-bg:          #2f3742;
        --#{$prefix}sidenav-item-active-color:      #ced6df;
        --#{$prefix}sidenav-item-active-bg:         #22232c;
    }

    // Light Topbar
    html[data-topbar-color="light"] {
        --#{$prefix}topbar-bg:                      #ffffff;
        --#{$prefix}topbar-item-color:                #{$gray-700};
        --#{$prefix}topbar-item-hover-color:          #{$primary};
        --#{$prefix}topbar-search-bg:                 transparent;
        --#{$prefix}topbar-search-border:           #e7e9eb;
    }

    // Gray Topbar
    html[data-topbar-color="gray"] {
        --#{$prefix}topbar-bg:                      #f1f2f7;
        --#{$prefix}topbar-item-color:                #{$gray-700};
        --#{$prefix}topbar-item-hover-color:          #{$primary};
        --#{$prefix}topbar-search-bg:               #e8e9ef;
        --#{$prefix}topbar-search-border:           #e8e9ef;
    }

    // Dark Topbar
    html[data-topbar-color="dark"] {
        --#{$prefix}topbar-bg:                      #252630;
        --#{$prefix}topbar-item-color:              #adb5bf;
        --#{$prefix}topbar-item-hover-color:        #e0eeff;
        --#{$prefix}topbar-search-bg:               #2d2e3c;
        --#{$prefix}topbar-search-border:           #2d2e3c;
    }

    // Gradient Topbar
    html[data-topbar-color="gradient"] {
        --#{$prefix}topbar-bg:                        #{linear-gradient(to bottom, #1a455f, #262549)};
        --#{$prefix}topbar-item-color:              rgba(255, 255, 255, 0.7);
        --#{$prefix}topbar-item-hover-color:        #ffffff;
        --#{$prefix}topbar-search-bg:               rgba(255, 255, 255, 0.1);
        --#{$prefix}topbar-search-border:           rgba(255, 255, 255, 0.1);
    }

    // Topbar (Dark Mode)
    html[data-bs-theme="dark"][data-topbar-color="light"],
    html[data-bs-theme="dark"][data-topbar-color="dark"],
    html[data-bs-theme="dark"][data-topbar-color="gray"] {
        --#{$prefix}topbar-bg:                      #252630;
        --#{$prefix}topbar-item-color:              #adb5bf;
        --#{$prefix}topbar-item-hover-color:        #e0eeff;
        --#{$prefix}topbar-search-bg:               #2d2e3c;
        --#{$prefix}topbar-search-border:           #2d2e3c;
    }
}

/* Dark Mode */
@if $enable-dark-mode {
    @include color-mode(dark, true) {
        --#{$prefix}light:                     #252630;
        --#{$prefix}light-rgb:                   #{to-rgb(#252630)};
        --#{$prefix}dark:                      #4b4d5c;
        --#{$prefix}dark-rgb:                    #{to-rgb(#4b4d5c)};
        --#{$prefix}box-shadow:                  #{0px 0px 30px rgba(0, 0, 0, 0.3)};
    }
}