// 
// modern Theme Mode
//

$theme-modern-colors: (
    "primary":   #5e6cc1,
    "secondary": #1c8de3,
    "success":   #13ca6d,
    "info":      #35b9e1,
    "warning":   #f2c24c,
    "danger":    #fb5858,
    "purple":    #6070cc,
);

@if $theme-modern ==true {

    @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

    html[data-skin="modern"] {

        --#{$prefix}font-sans-serif:        "Poppins", sans-serif;

        --#{$prefix}body-font-size:               13px;
        --#{$prefix}body-bg:               #f7f6f7;
        --#{$prefix}secondary-bg:          #fff;

        --#{$prefix}theme-card-border-width:                 1px;
        --#{$prefix}theme-card-border-color:                 transparent;
        --#{$prefix}theme-card-box-shadow:                   0 1px 2px rgba(57, 62, 80, 0.15);
        --#{$prefix}box-shadow:                              0 2px 4px rgba(56, 65, 74, 0.2);
        --#{$prefix}border-radius:             4px;

        --#{$prefix}font-weight-medium:      500;
        --#{$prefix}font-weight-semibold:    500;
        --#{$prefix}font-weight-bold:        600;

        --#{$prefix}font-size-xxs:          11px;
        --#{$prefix}font-size-xs:           12px;
        --#{$prefix}font-size-base:         13px;
        --#{$prefix}font-size-md:           14px;
        --#{$prefix}font-size-lg:           16px;
        --#{$prefix}font-size-xl:           18px;
        --#{$prefix}font-size-xxl:          20px;
        
        // Menu items
        --#{$prefix}sidenav-item-icon-size:                  1.125rem;
        --#{$prefix}sidenav-item-gap:                        12px;
        --#{$prefix}sidenav-item-padding-x:                  10px;
        --#{$prefix}sidenav-item-padding-y:                  10px;
        --#{$prefix}sidenav-item-font-size:                  0.8125rem;
        --#{$prefix}sidenav-item-font-weight:                500;

        // Sub Menu Items
        --#{$prefix}sidenav-sub-item-font-size:              0.8125rem;
        --#{$prefix}sidenav-sub-item-font-weight:            500;
        --#{$prefix}sidenav-sub-item-gap:                    4px;
        --#{$prefix}sidenav-sub-item-padding-x:              10px;
        --#{$prefix}sidenav-sub-item-padding-y:              7px;

        // Background Light left-sidebar
        &[data-menu-color="light"] {
            --#{$prefix}sidenav-bg:                       #{$white};
            --#{$prefix}sidenav-border-color:             #{$white};
            --#{$prefix}sidenav-item-color:               #{$gray-700};
            --#{$prefix}sidenav-item-hover-color:         #3c7de1;
            --#{$prefix}sidenav-item-hover-bg:          #eaf2ff;
            --#{$prefix}sidenav-item-active-color:        #3c7de1;
            --#{$prefix}sidenav-item-active-bg:         #eaf2ff;
        }

        // Background Gray left-sidebar
        &[data-menu-color="gray"] {
            --#{$prefix}sidenav-bg:                       #f1f2f7;
            --#{$prefix}sidenav-border-color:             #e7e9eb;
            --#{$prefix}sidenav-item-color:               #{$gray-700};
            --#{$prefix}sidenav-item-hover-color:         #3c7de1;
            --#{$prefix}sidenav-item-hover-bg:          #e8f7f4;
            --#{$prefix}sidenav-item-active-color:        #212b29;
            --#{$prefix}sidenav-item-active-bg:         #e8e8ed;
        }

        // Dark Left Sidebar
        &[data-menu-color="dark"] {
            --#{$prefix}sidenav-bg:                     #23303c;
            --#{$prefix}sidenav-border-color:           #23303c;
            --#{$prefix}sidenav-item-color:             #8495ab;
            --#{$prefix}sidenav-item-hover-color:       #bccee4;
            --#{$prefix}sidenav-item-hover-bg:          #2f3742;
            --#{$prefix}sidenav-item-active-color:      #ced6df;
            --#{$prefix}sidenav-item-active-bg:         #1c262f;
        }

        // Gradient Menu
        &[data-menu-color="gradient"] {
            --#{$prefix}sidenav-bg:                       #{linear-gradient(135deg, #1a455f, #262549)};
            --#{$prefix}sidenav-border-color:             null;
            --#{$prefix}sidenav-item-color:             #799cb7;
            --#{$prefix}sidenav-item-hover-color:       #bccee4;
            --#{$prefix}sidenav-item-hover-bg:          #2f3742;
            --#{$prefix}sidenav-item-active-color:      #cfdff1;
            --#{$prefix}sidenav-item-active-bg:         #294461;
        }

        // Image Menu
        &[data-menu-color="image"] {
            --#{$prefix}sidenav-bg:                       #{linear-gradient(to bottom right, rgba(16,16,30,0.85), #183c52), url('/images/sidenav-bg.jpg') center/cover no-repeat};
            --#{$prefix}sidenav-border-color:             null;
            --#{$prefix}sidenav-item-color:             #799cb7;
            --#{$prefix}sidenav-item-hover-color:       #bccee4;
            --#{$prefix}sidenav-item-hover-bg:          #2f3742;
            --#{$prefix}sidenav-item-active-color:      #cfdff1;
            --#{$prefix}sidenav-item-active-bg:         #1e3447;
        }

        // Dark Mode
        &[data-bs-theme="dark"][data-menu-color="dark"],
        &[data-bs-theme="dark"][data-menu-color="gray"],
        &[data-bs-theme="dark"][data-menu-color="light"] {
            --#{$prefix}sidenav-bg:                     #1e1f27;
            --#{$prefix}sidenav-border-color:           #2c2d38;
            --#{$prefix}sidenav-item-color:             #8495ab;
            --#{$prefix}sidenav-item-hover-color:       #bccee4;
            --#{$prefix}sidenav-item-hover-bg:          #2f3742;
            --#{$prefix}sidenav-item-active-color:      #ced6df;
            --#{$prefix}sidenav-item-active-bg:         #22232c;
        }

        // Light Topbar
        &[data-topbar-color="light"] {
            --#{$prefix}topbar-bg:                      #ffffff;
            --#{$prefix}topbar-item-color:                #{$gray-700};
            --#{$prefix}topbar-item-hover-color:          #3c7de1;
            --#{$prefix}topbar-search-bg:                 transparent;
            --#{$prefix}topbar-search-border:           #e7e9eb;
        }

        // Gray Topbar
        &[data-topbar-color="gray"] {
            --#{$prefix}topbar-bg:                      #f1f2f7;
            --#{$prefix}topbar-item-color:                #{$gray-700};
            --#{$prefix}topbar-item-hover-color:          #3c7de1;
            --#{$prefix}topbar-search-bg:               #e8e9ef;
            --#{$prefix}topbar-search-border:           #e8e9ef;
        }

        // Dark Topbar
        &[data-topbar-color="dark"] {
            --#{$prefix}topbar-bg:                      #252630;
            --#{$prefix}topbar-item-color:              #adb5bf;
            --#{$prefix}topbar-item-hover-color:        #e0eeff;
            --#{$prefix}topbar-search-bg:               #2d2e3c;
            --#{$prefix}topbar-search-border:           #2d2e3c;
        }

        // Gradient Topbar
        &[data-topbar-color="gradient"] {
            --#{$prefix}topbar-bg:                        #{linear-gradient(to bottom, #1a455f, #262549)};
            --#{$prefix}topbar-item-color:              rgba(255, 255, 255, 0.7);
            --#{$prefix}topbar-item-hover-color:        #ffffff;
            --#{$prefix}topbar-search-bg:               rgba(255, 255, 255, 0.1);
            --#{$prefix}topbar-search-border:           rgba(255, 255, 255, 0.1);
        }

        // Topbar (Dark Mode)
        &[data-bs-theme="dark"][data-topbar-color="dark"],
        &[data-bs-theme="dark"][data-topbar-color="light"],
        &[data-bs-theme="dark"][data-topbar-color="gray"] {
            --#{$prefix}topbar-bg:                      #252630;
            --#{$prefix}topbar-item-color:              #adb5bf;
            --#{$prefix}topbar-item-hover-color:        #e0eeff;
            --#{$prefix}topbar-search-bg:               #2d2e3c;
            --#{$prefix}topbar-search-border:           #2d2e3c;
        }

        @each $name, $value in $theme-modern-colors {
            --#{$prefix}#{$name}: #{$value};
        }

        @each $name, $value in $theme-modern-colors {
            --#{$prefix}#{$name}-rgb: #{to-rgb($value)};
        }

        @each $name, $value in $theme-modern-colors {
            --#{$prefix}#{$name}-bg-subtle: #{rgba($value, 0.15)};
        }

        @each $name, $value in $theme-modern-colors {
            --#{$prefix}#{$name}-border-subtle: #{rgba($value, 0.3)};
        }

        @each $name, $value in $theme-modern-colors {
            --#{$prefix}#{$name}-text-emphasis: #{shade-color($value, 25%)};
        }
    }

    @if $enable-dark-mode {
        @include color-mode(dark, true) {
            &[data-skin="modern"] {
                --#{$prefix}body-bg:                     #{#17181e};
                --#{$prefix}body-bg-rgb:                 #{to-rgb(#17181e)};

                --#{$prefix}secondary-bg:                #{#1b1c22};
                --#{$prefix}secondary-bg-rgb:            #{to-rgb(#1b1c22)};

                --#{$prefix}box-shadow:                  #{0px 0px 30px rgba(0, 0, 0, 0.3)};

                --#{$prefix}theme-card-border-color:                 #293036;
            }
        }
    }
}