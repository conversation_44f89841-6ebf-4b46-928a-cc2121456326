//
// _buttons.scss
//

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &-icon {
        position: relative;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        height: calc(#{$btn-padding-y * 2} + #{$btn-line-height}em + #{$border-width * 2});
        width: calc(#{$btn-padding-y * 2} + #{$btn-line-height}em + #{$border-width * 2});
        padding: 0;

        :is(i, svg, img) {
            vertical-align: middle;
        }

        &.btn-sm {
            height: calc(#{$btn-padding-y-sm * 2} + #{$btn-line-height}em + #{$border-width * 2});
            width: calc(#{$btn-padding-y-sm * 2} + #{$btn-line-height}em + #{$border-width * 2});
        }

        &.btn-lg {
            height: calc(#{$btn-padding-y-lg * 2} + #{$btn-line-height}em + #{$border-width * 2});
            width: calc(#{$btn-padding-y-lg * 2} + #{$btn-line-height}em + #{$border-width * 2});
        }
    }

    &.btn-sm {
        line-height: normal;
    }
}

@each $state in map-keys($theme-colors) {

    .btn-#{$state} {
        --#{$prefix}btn-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-border-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-hover-bg: var(--#{$prefix}#{$state}-text-emphasis);
        --#{$prefix}btn-hover-border-color: var(--#{$prefix}#{$state}-text-emphasis);
        --#{$prefix}btn-focus-shadow-rgb: var(--#{$prefix}#{$state}-rgb);
        --#{$prefix}btn-active-bg: var(--#{$prefix}#{$state}-text-emphasis);
        --#{$prefix}btn-active-border-color: var(--#{$prefix}#{$state}-text-emphasis);
        --#{$prefix}btn-disabled-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-disabled-border-color: var(--#{$prefix}#{$state});
    }

    .btn-outline-#{$state} {
        --#{$prefix}btn-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-bg: transparent;
        --#{$prefix}btn-border-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-hover-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-hover-border-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-focus-shadow-rgb: var(--#{$prefix}#{$state}-rgb);
        --#{$prefix}btn-active-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-active-border-color: var(--#{$prefix}#{$state});
    }

    .btn-soft-#{$state} {
        --#{$prefix}btn-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-bg: var(--#{$prefix}#{$state}-bg-subtle);
        --#{$prefix}btn-border-color: #{transparent};
        --#{$prefix}btn-hover-color: #{$white};
        --#{$prefix}btn-hover-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-hover-border-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-active-color: #{$white};
        --#{$prefix}btn-active-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-active-border-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-disabled-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-disabled-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-disabled-border-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-focus-shadow-rgb: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}#{$state})-rgb, 0.5);
    }

    .btn-ghost-#{$state} {
        --#{$prefix}btn-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-bg: transparent;
        --#{$prefix}btn-border-color: #{transparent};
        --#{$prefix}btn-hover-color: #{$white};
        --#{$prefix}btn-hover-bg: var(--#{$prefix}#{$state});
        --#{$prefix}btn-hover-border-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-active-color: var(--#{$prefix}#{$state});
        --#{$prefix}btn-active-bg: var(--#{$prefix}#{$state}-bg-subtle);
        --#{$prefix}btn-active-border-color: var(--#{$prefix}#{$state}-bg-subtle);
        --#{$prefix}btn-disabled-color: var(--#{$prefix}#{$state}-bg-subtle);
        --#{$prefix}btn-disabled-bg: var(--#{$prefix}#{$state}-bg-subtle);
        --#{$prefix}btn-disabled-border-color: var(--#{$prefix}#{$state}-bg-subtle);
        --#{$prefix}btn-focus-shadow-rgb: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}#{$state})-rgb, 0.5);
    }
}

.btn-default {
    --#{$prefix}btn-color: var(--#{$prefix}dark);
    --#{$prefix}btn-bg: #{transparent};
    --#{$prefix}btn-border-color: rgba(var(--#{$prefix}dark-rgb), 0.1);
    --#{$prefix}btn-hover-color: var(--#{$prefix}dark);
    --#{$prefix}btn-hover-bg: rgba(var(--#{$prefix}dark-rgb), 0.02);
    --#{$prefix}btn-hover-border-color: rgba(var(--#{$prefix}dark-rgb), 0.3);
    --#{$prefix}btn-active-bg: var(--#{$prefix}tertiary-bg);
    --#{$prefix}btn-active-color: var(--#{$prefix}dark);
    --#{$prefix}btn-active-border-color: rgba(var(--#{$prefix}dark-rgb), 0.15);
    --#{$prefix}btn-focus-shadow-rgb: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}light-rgb), 0.5);
}

.btn-light {
    --#{$prefix}btn-color: var(--#{$prefix}dark);
    --#{$prefix}btn-bg: var(--#{$prefix}light);
    --#{$prefix}btn-border-color: #{transparent};
    --#{$prefix}btn-hover-color: var(--#{$prefix}primary);
    --#{$prefix}btn-hover-bg: var(--#{$prefix}light);
    --#{$prefix}btn-hover-border-color: #{transparent};
    --#{$prefix}btn-active-bg: var(--#{$prefix}tertiary-bg);
    --#{$prefix}btn-active-color: var(--#{$prefix}tertiary-color);
    --#{$prefix}btn-active-border-color: var(--#{$prefix}tertiary-bg);
    --#{$prefix}btn-focus-shadow-rgb: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}light-rgb), 0.5);
}

.btn-outline-light {
    --#{$prefix}btn-color: var(--#{$prefix}dark);
    --#{$prefix}btn-bg: #{transparent};
    --#{$prefix}btn-border-color: var(--#{$prefix}light);
    --#{$prefix}btn-hover-color: var(--#{$prefix}dark);
    --#{$prefix}btn-hover-bg: var(--#{$prefix}tertiary-bg);
    --#{$prefix}btn-hover-border-color: var(--#{$prefix}light);
    --#{$prefix}btn-active-bg: var(--#{$prefix}tertiary-bg);
    --#{$prefix}btn-active-color: var(--#{$prefix}dark);
    --#{$prefix}btn-active-border-color: var(--#{$prefix}light);
    --#{$prefix}btn-focus-shadow-rgb: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}light-rgb), 0.5);
}

.btn-outline-dark {
    --#{$prefix}btn-color: var(--#{$prefix}dark);
    --#{$prefix}btn-bg: #{transparent};
    --#{$prefix}btn-border-color: var(--#{$prefix}dark);
    --#{$prefix}btn-hover-color: var(--#{$prefix}light);
    --#{$prefix}btn-hover-bg: var(--#{$prefix}dark);
    --#{$prefix}btn-hover-border-color: var(--#{$prefix}dark);
    --#{$prefix}btn-active-bg: var(--#{$prefix}dark);
    --#{$prefix}btn-active-color: var(--#{$prefix}light);
    --#{$prefix}btn-active-border-color: var(--#{$prefix}dark);
    --#{$prefix}btn-focus-shadow-rgb: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}dark-rgb), 0.5);
}

.btn-soft-dark {
    --#{$prefix}btn-hover-color: var(--#{$prefix}light);
    --#{$prefix}btn-active-color: var(--#{$prefix}light);
}

html[data-bs-theme="dark"] {
    .btn-default {
        --#{$prefix}btn-color: var(--#{$prefix}body-color);
        --#{$prefix}btn-bg: #{transparent};
        --#{$prefix}btn-border-color: rgba(var(--#{$prefix}dark-rgb), 1);
        --#{$prefix}btn-hover-color: var(--#{$prefix}primary);
        --#{$prefix}btn-hover-bg: rgba(var(--#{$prefix}dark-rgb), 0.02);
        --#{$prefix}btn-hover-border-color: rgba(var(--#{$prefix}primary-rgb), 0.75);
        --#{$prefix}btn-active-bg: var(--#{$prefix}tertiary-bg);
        --#{$prefix}btn-active-color: var(--#{$prefix}dark);
        --#{$prefix}btn-active-border-color: rgba(var(--#{$prefix}primary-rgb), 0.75);
        --#{$prefix}btn-focus-shadow-rgb: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}light-rgb), 0.5);
    }

    .btn-light {
        --#{$prefix}btn-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-color: var(--#{$prefix}body-color);
        --#{$prefix}btn-hover-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-hover-border-color: #{transparent};
        --#{$prefix}btn-active-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-active-border-color: #{transparent};
    }
    
    .btn-dark {
        --#{$prefix}btn-bg: var(--#{$prefix}border-color);
        --#{$prefix}btn-border-color: var(--#{$prefix}border-color);
        --#{$prefix}btn-color: var(--#{$prefix}body-color);
        --#{$prefix}btn-hover-color: var(--#{$prefix}white);
        --#{$prefix}btn-hover-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-hover-border-color: #{transparent};
        --#{$prefix}btn-active-color: var(--#{$prefix}white);
        --#{$prefix}btn-active-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-active-border-color: #{transparent};
    }
    
    .btn-outline-light {
        --#{$prefix}btn-color: var(--#{$prefix}white);
        --#{$prefix}btn-bg: #{transparent};
        --#{$prefix}btn-border-color: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-hover-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-hover-border-color: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-active-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-active-border-color: #{transparent};
    }
    
    .btn-outline-dark {
        --#{$prefix}btn-color: var(--#{$prefix}white);
        --#{$prefix}btn-bg: #{transparent};
        --#{$prefix}btn-border-color: var(--#{$prefix}border-color);
        --#{$prefix}btn-hover-color: var(--#{$prefix}white);
        --#{$prefix}btn-hover-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-hover-border-color: #{transparent};
        --#{$prefix}btn-active-color: var(--#{$prefix}white);
        --#{$prefix}btn-active-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-active-border-color: #{transparent};
    }
    
    .btn-soft-dark,
    .btn-ghost-dark {
        --#{$prefix}btn-hover-color: var(--#{$prefix}white);
        --#{$prefix}btn-hover-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-hover-border-color: #{transparent};
        --#{$prefix}btn-active-color: var(--#{$prefix}white);
        --#{$prefix}btn-active-bg: rgba(var(--#{$prefix}white-rgb), 0.1);
        --#{$prefix}btn-active-border-color: #{transparent};
    }
}