//
// _badges.scss
//

.badge {
    vertical-align: middle;
}

// Soft Badges
@each $state in map-keys($theme-colors) {
    .badge-soft-#{$state} {
        --#{$prefix}badge-color: var(--#{$prefix}#{$state});
        background-color: var(--#{$prefix}#{$state}-bg-subtle);

        &[href] {
            color: var(--#{$prefix}#{$state});
            text-decoration: none;
            background-color: var(--#{$prefix}#{$state}-bg-subtle);

            &:is(:hover, :focus) {
                color: var(--#{$prefix}#{$state});
                text-decoration: none;
                background-color: var(--#{$prefix}#{$state}-bg-subtle);
            }
        }
    }
}

// Outline badges
@each $state in map-keys($theme-colors) {
    .badge-outline-#{$state} {
        --#{$prefix}badge-color: var(--#{$prefix}#{$state});
        border: 1px solid var(--#{$prefix}#{$state});
        background-color: transparent;

        &[href] {
            color: var(--#{$prefix}#{$state});
            text-decoration: none;
            background-color: var(--#{$prefix}#{$state}-bg-subtle);

            &:is(:hover, :focus) {
                color: var(--#{$prefix}#{$state});
                text-decoration: none;
                background-color: var(--#{$prefix}#{$state}-bg-subtle);
            }
        }
    }
}

.badge-default {
    --#{$prefix}badge-color: var(--#{$prefix}body-color);
    border: 1px solid rgba(var(--#{$prefix}dark-rgb), 0.2);
}

// Label based Badges
.badge-label {
    border-radius: 0;
    padding: calc(var(--#{$prefix}badge-padding-y) * 1.5) calc(var(--#{$prefix}badge-padding-x) * 2);
}

//Square & Circle badge
.badge-square,
.badge-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 1rem;
    min-width: 1rem;
    padding: 0 .1rem;
    line-height: 0;
}

.badge.badge-circle {
    border-radius: 50%;
    padding: 0;
    min-width: unset;
    width: 1rem;
}