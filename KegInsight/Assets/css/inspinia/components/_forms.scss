//
// _forms.scss
//

// Form Control
.form-control,
.form-select {
    &:hover {
        border-color: $input-focus-border-color;
    }
}

// Form Control Small
.form-control-sm {
    line-height: normal;
}


// Form elements (Color and Range)
input.form-control[type="color"],
input.form-control[type="range"] {
    min-height: $input-height;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

@each $state in map-keys($theme-colors) {
    .form-check-#{$state} {
        .form-check-input {
            &:checked {
                background-color: var(--#{$prefix}#{$state});
                border-color: var(--#{$prefix}#{$state});
            }
        }
    }
}


// card radio
.card-radio {
    padding: 0;

    .form-check-label {
        background-color: $card-bg;
        border: 1px solid var(--#{$prefix}border-color);
        border-radius: $border-radius;
        padding: 1rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
        position: relative;
        padding-right: 32px;

        &:hover {
            cursor: pointer;
        }
    }

    .form-check-input {
        display: none;

        &:checked+.form-check-label {
            &:before {
                content: "\f746";
                font-family: "tabler-icons";
                position: absolute;
                bottom: 2px;
                right: 6px;
                font-size: 28px;
                color: var(--#{$prefix}danger);
            }
        }
    }
}

// Custom Form Check (Gray Styled)
.form-check-input-light {
    background-color: rgba(var(--#{$prefix}light-rgb), 0.9);
    border-color: rgba(var(--#{$prefix}light-rgb), 0.9);
    box-shadow: inset 0 1px 2px rgba(var(--#{$prefix}dark-rgb), 0.05);
}