//
// _modal.scss
//

// Title text within header
.modal-title {
    color: inherit;
}

// Modal full width
.modal-full-width {
    width: 95%;
    max-width: none;
}

// Modal Positions
.modal-top {
    margin: 0 auto;
}

.modal-bottom {
    display: flex;
    flex-flow: column nowrap;
    -ms-flex-pack: end;
    justify-content: flex-end;
    height: 100%;
    margin: 0 auto;
    align-content: center;
}

// Modal Backdrop
.modal-backdrop {
    backdrop-filter: blur(6px);
}


// Modal Animation
.modal {
    .modal-dialog {
        transition: none;
        transform: scale(0.85);
        opacity: 0;
    }

    &.show {
        .modal-dialog {
            animation: zoomInModal 0.1s ease-out forwards;
        }
    }
}

@keyframes zoomInModal {
    from {
        transform: scale(0.85);
        opacity: 0;
    }

    to {
        transform: scale(1);
        opacity: 1;
    }
}