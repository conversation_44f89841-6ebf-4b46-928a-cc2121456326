//
// _horizontal.scss
//

html[data-layout="topnav"] {

    .app-topbar {
        margin: 0;
        z-index: 1005;
        box-shadow: none;
        border-bottom: 0;

        .logo-topbar {
            display: block;
            float: none;
        }

        .sidenav-toggle-button {
            display: none;
        }

        .topbar-menu {
            padding-right: calc(var(--#{$prefix}gutter-x) * .5);
            padding-left: calc(var(--#{$prefix}gutter-x) * .5);
        }
    }

    .content-page {
        margin-left: 0;
        min-height: calc(100vh - $topbar-height - 43px);
    }

    .topnav {
        width: 100%;
        z-index: 1000;
        position: sticky;
        top: $topbar-height;
        background: $sidenav-bg;
        padding-left: $grid-gutter-width * 0.5;
        padding-right: $grid-gutter-width * 0.5;
        box-shadow: var(--#{$prefix}box-shadow);

        .navbar {
            margin: 0;
            padding: 0;
        }

        .navbar-nav {

            @include media-breakpoint-up(lg) {
                .nav-item {
                    &:first-of-type {
                        .nav-link {
                            padding-left: 0;
                        }
                    }
                }
            }
            .nav-link {
                display: flex;
                align-items: center;
                position: relative;
                line-height: $sidenav-item-font-size;
                font-size: $sidenav-item-font-size;
                font-weight: $sidenav-item-font-weight;
                padding: calc($sidenav-item-padding-y * 1.25) calc($sidenav-item-padding-x * 1.5);


                .menu-icon {
                    margin-right: calc($sidenav-item-icon-size * 0.5);

                    i {
                        font-size: $sidenav-item-icon-size;
                    }

                    svg {
                        height: $sidenav-item-icon-size;
                        width: $sidenav-item-icon-size;
                    }
                }


                .menu-arrow {
                    margin-left: calc($sidenav-item-icon-size * 0.5);
                }
            }

            .dropdown {
                .dropdown-menu {
                    font-size: $sidenav-sub-item-font-size;

                    .dropdown-item {
                        display: flex;
                        gap: 8px;
                        align-items: center;
                        padding: calc($sidenav-item-padding-y * 0.75) calc($sidenav-item-padding-x * 1.5);

                        i {
                            font-size: calc($sidenav-sub-item-font-size * 1.25);
                        }
                    }

                    .menu-arrow {
                        transform: rotate(-90deg);
                    }

                }
            }
        }

        .nav-item {

            >a,
            .nav-link {
                color: $sidenav-item-color;

                &:hover {
                    color: $sidenav-item-hover-color;
                }
            }

            &.active {
                >a {
                    color: $sidenav-item-hover-color;
                }
            }

        }
    }

    .page-title-head {
        background-color: transparent;
        border: 0;
        min-height: 60px;
        margin-bottom: 0;
    }
}


@include media-breakpoint-up(lg) {

    .topnav {
        .dropdown {
            .dropdown-menu {
                margin-top: 0;

                .dropdown {
                    .dropdown-menu {
                        position: absolute;
                        top: 0;
                        left: 100%;
                        display: none;
                    }
                }
            }

            &:hover {
                .nav-link {
                    color: $sidenav-item-hover-color !important;
                }

                >.dropdown-menu {
                    display: block;
                }
            }
        }

        .dropdown:hover>.dropdown-menu>.dropdown:hover {
            >.dropdown-item {
                color: $sidenav-item-hover-color !important;
            }
            >.dropdown-menu {
                display: block;
            }
        }
        

        .dropdown.active>a.dropdown-item {
            color: $dropdown-link-active-color;
            @include gradient-bg($dropdown-link-active-bg);
        }
    }
}

@include media-breakpoint-down(lg) {
    html[data-layout="topnav"] {
        .topnav {
            max-height: 360px;
            overflow-y: auto;

            .navbar-nav {
                .nav-link {
                    padding: calc($sidenav-item-padding-y * 0.8) calc($sidenav-item-padding-x * 2);
                }
            }

            .dropdown {
                .dropdown-menu {
                    background-color: transparent;
                    border: none;
                    box-shadow: none;
                    padding-left: calc($sidenav-item-padding-x * 3);
                    margin-top: calc($sidenav-item-padding-y * -0.75);

                    .dropdown-menu {
                        margin-top: calc($sidenav-item-padding-y * -0.5);
                        padding-left: calc($sidenav-item-padding-x * 1.5);
                    }
                }

                .dropdown-item {
                    position: relative;
                    background-color: transparent;
                }
            }

            .dropdown {
                .dropdown-item {
                    color: $sidenav-item-color;

                    &.hover {
                        color: $sidenav-item-hover-color;
                    }

                    &.active,
                    &:active {
                        color: $sidenav-item-active-color;
                    }
                }
            }

            .dropdown.active>a.dropdown-item {
                color: $sidenav-item-active-color;
            }
        }

        .topnav-toggle-button {
            display: block;
        }
    }
}

//  Horizontal Menu Toggle Button
.topnav-toggle-button {
    display: none;
    position: relative;
    cursor: pointer;
    float: left;
    padding: 0;
    background-color: transparent;
    border: none;
    color: $topbar-item-color;
}


@include media-breakpoint-up(xxl) {
    html[data-layout="topnav"] {
        .container-fluid {
            max-width: 85%;
        }
    }
}