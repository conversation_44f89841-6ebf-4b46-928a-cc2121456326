using FluentMigrator;

namespace KegInsight.Migrations;

[Migration(1000, "Create users table")]
public class CreateUsers : Migration
{
    public override void Up()
    {
        Create.Table("users")
            .WithColumn("id").AsGuid().PrimaryKey()
            .WithColumn("username").AsString(255).NotNullable()
            .WithColumn("password").AsString(255).NotNullable();
    }

    public override void Down()
    {
        Delete.Table("users");
    }
}