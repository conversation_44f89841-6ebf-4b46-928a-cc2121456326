 <!-- Sidenav Menu Start -->
<div class="sidenav-menu">

    <!-- Brand Logo -->
    <a href=@Url.Action("Index", "Home") class="logo">
        <span class="logo logo-light">
            <span class="logo-lg"><img src="~/images/logo.png" alt="logo"></span>
            <span class="logo-sm"><img src="~/images/logo-sm.png" alt="small logo"></span>
        </span>

        <span class="logo logo-dark">
            <span class="logo-lg"><img src="~/images/logo-black.png" alt="dark logo"></span>
            <span class="logo-sm"><img src="~/images/logo-sm.png" alt="small logo"></span>
        </span>
    </a>

    <!-- Sidebar Hover Menu Toggle Button -->
    <button class="button-on-hover">
        <i class="ti ti-menu-4 fs-22 align-middle"></i>
    </button>

    <!-- Full Sidebar Menu Close Button -->
    <button class="button-close-offcanvas">
        <i class="ti ti-x align-middle"></i>
    </button>

    <div class="scrollbar" data-simplebar>
        <!--- Sidenav Menu -->
        <ul class="side-nav">
            <li class="side-nav-title" data-lang="menu-title">Menu</li>

            <li class="side-nav-item">
                <a class="side-nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                    <span class="menu-icon"><i class="ti ti-home"></i></span>
                    <span class="menu-text">Home</span>
                </a>
            </li>

            <li class="side-nav-divider"></li>

            <li class="side-nav-item">
                <a class="side-nav-link" asp-area="" asp-controller="Home" asp-action="Privacy">
                    <span class="menu-icon"><i class="ti ti-privacy"></i></span>
                    <span class="menu-text">Privacy</span>
                </a>
            </li>
            
        </ul>
    </div>
</div>
<!-- Sidenav Menu End -->