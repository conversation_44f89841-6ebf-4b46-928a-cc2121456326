@{
    var title = ViewBag.Title;
    var subtitle = ViewBag.SubTitle;
}

<div class="page-title-head d-flex align-items-center">
    <div class="flex-grow-1">
        <h4 class="fs-sm text-uppercase fw-bold m-0">@title</h4>
    </div>

    <div class="text-end">
        <ol class="breadcrumb m-0 py-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">Inspinia</a></li>
            @if (!string.IsNullOrEmpty(subtitle))
            {
                <li class="breadcrumb-item"><a href="javascript: void(0);">@subtitle</a></li>
            }
            <li class="breadcrumb-item active">@title</li>
        </ol>
    </div>
</div>