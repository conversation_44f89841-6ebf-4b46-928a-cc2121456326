<!DOCTYPE html>

@{
    var html_attributes = ViewBag.HTMLAttributes != null ? ViewBag.HTMLAttributes : "";
    var html_class = ViewBag.HTMLClass != null ? ViewBag.HTMLClass : "";
}

<html lang="en" class="@html_class" data-skin="classic" data-bs-theme="light" data-menu-color="gradient" data-topbar-color="light" data-sidenav-size="default" data-layout-position="fixed" @html_attributes>

<head>
    @await Html.PartialAsync("~/Views/Shared/Partials/_TitleMeta.cshtml")
    @await Html.PartialAsync("~/Views/Shared/Partials/_Head.cshtml")
</head>

<body>
<div class="wrapper">
    @await Html.PartialAsync("~/Views/Shared/Partials/_TopBar.cshtml")
    @await Html.PartialAsync("~/Views/Shared/Partials/_SideNav.cshtml")
    <div class="content-page">
        @RenderBody()
        @await Html.PartialAsync("~/Views/Shared/Partials/_Footer.cshtml")
    </div>
</div>
</body>

</html>