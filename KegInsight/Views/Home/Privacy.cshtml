@{
    ViewBag.Title = "Privacy";
}

<div class="d-flex gap-2">
    <button type="button" class="btn btn-info col-md-2" data-bs-toggle="popover" title="Need Help?" data-bs-content="Click here to get support from our team. We're here 24/7 to assist you.">
        Popover
    </button>

    <button type="button" class="btn btn-info col-md-2" data-bs-toggle="tooltip" title="Need Help?" data-bs-content="Click here to get support from our team. We're here 24/7 to assist you.">
        Tooltip
    </button>
</div>
