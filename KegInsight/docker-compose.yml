
services:
  redis:
    image: redis
    volumes:
      - redis:/var/lib/redis/data
    ports:
      - 6379:6379

  postgres:
    image: timescale/timescaledb:latest-pg17
    volumes:
        - postgres:/var/lib/postgresql/data
    environment:
        - PGUSER=postgres
        - PGDATABASE=kegbridge-dev
        - POSTGRES_PASSWORD=postgres
    healthcheck:
        test: pg_isready -U postgres -h 127.0.0.1
        interval: 5s
    ports:
      - 5432:5432

  seq:
    image: datalust/seq
    environment:
      ACCEPT_EULA: Y
    volumes:
      - seq:/data
    ports:
      - "5380:80"
      - "5341:5341"

  # influxdb18:
  #   image: influxdb:1.8
  #   volumes:
  #     - influxdb18:/var/lib/influxdb
  #   environment:
  #     - INFLUXDB_HTTP_FLUX_ENABLED=true
  #   ports:
  #     - '8086:8086'

  grafana:
     image: grafana/grafana-oss:12.1.0
     volumes:
       - grafana:/var/lib/grafana
     environment:
       - GF_SECURITY_ADMIN_USER=lambrechts
       - GF_SECURITY_ADMIN_PASSWORD=Gans2870   
     ports:
       - '3000:3000'

  influxdb21:
    image: influxdb:2.1
    volumes:
      - influxdb2:/var/lib/influxdb2
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=lambrechts
      - DOCKER_INFLUXDB_INIT_PASSWORD=lambrechts
      - DOCKER_INFLUXDB_INIT_ORG=brewery
      - DOCKER_INFLUXDB_INIT_BUCKET=kegline
    ports:
      - '8086:8086'

volumes:
    influxdb2:
    postgres:
    redis:
    seq:
    grafana:
