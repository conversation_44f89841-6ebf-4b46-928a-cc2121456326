<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
      <Content Remove="wwwroot\images\flags\vg.svg" />
      <Content Remove="wwwroot\images\flags\ve.svg" />
      <Content Remove="wwwroot\images\flags\uz.svg" />
      <Content Remove="wwwroot\images\flags\uy.svg" />
      <Content Remove="wwwroot\images\flags\us.svg" />
      <Content Remove="wwwroot\images\flags\un.svg" />
      <Content Remove="wwwroot\images\flags\tz.svg" />
      <Content Remove="wwwroot\images\flags\tv.svg" />
      <Content Remove="wwwroot\images\flags\tr.svg" />
      <Content Remove="wwwroot\images\flags\tn.svg" />
      <Content Remove="wwwroot\images\flags\tm.svg" />
      <Content Remove="wwwroot\images\flags\tk.svg" />
      <Content Remove="wwwroot\images\flags\tj.svg" />
      <Content Remove="wwwroot\images\flags\th.svg" />
      <Content Remove="wwwroot\images\flags\sz.svg" />
      <Content Remove="wwwroot\images\flags\qa.svg" />
      <Content Remove="wwwroot\images\flags\pw.svg" />
      <Content Remove="wwwroot\images\flags\ps.svg" />
      <Content Remove="wwwroot\images\flags\pm.svg" />
      <Content Remove="wwwroot\images\flags\pl.svg" />
      <Content Remove="wwwroot\images\flags\pk.svg" />
      <Content Remove="wwwroot\images\flags\ph.svg" />
      <Content Remove="wwwroot\images\flags\pf.svg" />
      <Content Remove="wwwroot\images\flags\pe.svg" />
      <Content Remove="wwwroot\images\flags\pa.svg" />
      <Content Remove="wwwroot\images\flags\om.svg" />
      <Content Remove="wwwroot\images\flags\nz.svg" />
      <Content Remove="wwwroot\images\flags\nr.svg" />
      <Content Remove="wwwroot\images\flags\nl.svg" />
      <Content Remove="wwwroot\images\flags\ni.svg" />
      <Content Remove="wwwroot\images\flags\ng.svg" />
      <Content Remove="wwwroot\images\flags\nf.svg" />
      <Content Remove="wwwroot\images\flags\nc.svg" />
      <Content Remove="wwwroot\images\flags\na.svg" />
      <Content Remove="wwwroot\images\flags\mw.svg" />
      <Content Remove="wwwroot\images\flags\mq.svg" />
      <Content Remove="wwwroot\images\flags\li.svg" />
      <Content Remove="wwwroot\images\flags\la.svg" />
      <Content Remove="wwwroot\images\flags\kp.svg" />
      <Content Remove="wwwroot\images\flags\km.svg" />
      <Content Remove="wwwroot\images\flags\gy.svg" />
      <Content Remove="wwwroot\images\flags\gp.svg" />
      <Content Remove="wwwroot\images\flags\fk.svg" />
      <Content Remove="wwwroot\images\flags\dm.svg" />
      <Content Remove="wwwroot\images\flags\cz.svg" />
      <Content Remove="wwwroot\images\flags\cy.svg" />
      <Content Remove="wwwroot\images\flags\cx.svg" />
      <Content Remove="wwwroot\images\flags\cw.svg" />
      <Content Remove="wwwroot\images\flags\ci.svg" />
      <Content Remove="wwwroot\images\flags\bo.svg" />
      <Content Remove="wwwroot\images\flags\bn.svg" />
      <Content Remove="wwwroot\images\flags\bm.svg" />
      <Content Remove="wwwroot\images\flags\bj.svg" />
      <Content Remove="wwwroot\images\flags\bi.svg" />
      <Content Remove="wwwroot\images\flags\bh.svg" />
      <Content Remove="wwwroot\images\flags\bg.svg" />
      <Content Remove="wwwroot\images\flags\bf.svg" />
      <Content Remove="wwwroot\images\flags\be.svg" />
      <Content Remove="wwwroot\images\flags\aw.svg" />
      <Content Remove="wwwroot\images\flags\au.svg" />
      <Content Remove="wwwroot\images\flags\at.svg" />
      <Content Remove="wwwroot\images\flags\aq.svg" />
      <Content Remove="wwwroot\images\flags\ao.svg" />
      <Content Remove="wwwroot\images\flags\am.svg" />
      <Content Remove="wwwroot\images\flags\al.svg" />
      <Content Remove="wwwroot\images\flags\af.svg" />
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="wwwroot\css\site.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.js.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\bootstrap\LICENSE" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation-unobtrusive\LICENSE.txt" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation\dist\additional-methods.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation\dist\additional-methods.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation\dist\jquery.validate.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation\dist\jquery.validate.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation\LICENSE.md" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery\dist\jquery.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery\dist\jquery.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery\dist\jquery.min.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery\dist\jquery.slim.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery\dist\jquery.slim.min.js" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery\dist\jquery.slim.min.map" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery\LICENSE.txt" />
      <_ContentIncludedByDefault Remove="wwwroot\assets\css\application.css" />
      <_ContentIncludedByDefault Remove="wwwroot\assets\css\application.css.map" />
    </ItemGroup>

    <ItemGroup>
      <None Include="wwwroot\images\sidenav-bg.jpg" />
      <None Include="wwwroot\images\user-bg-pattern.png" />
      <None Include="wwwroot\images\user-bg-pattern.svg" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Dapper" Version="2.1.66" />
      <PackageReference Include="Dapper.ColumnMapper" Version="1.3.0" />
      <PackageReference Include="Dapper.FastCrud" Version="3.3.2" />
      <PackageReference Include="Dapper.Logging" Version="0.4.3" />
      <PackageReference Include="Destructurama.Attributed" Version="5.1.0" />
      <PackageReference Include="Destructurama.JsonNet" Version="4.0.2" />
      <PackageReference Include="FluentMigrator" Version="7.1.0" />
      <PackageReference Include="FluentMigrator.Runner.Postgres" Version="7.1.0" />
      <PackageReference Include="Microsoft.AspNetCore.Mvc.ViewFeatures" Version="2.3.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="Npgsql" Version="9.0.3" />
      <PackageReference Include="Npgsql.Json.NET" Version="9.0.3" />
      <PackageReference Include="Serilog" Version="4.3.0" />
      <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
      <PackageReference Include="Serilog.Enrichers.ClientInfo" Version="2.3.0" />
      <PackageReference Include="Serilog.Enrichers.Context" Version="4.6.5" />
      <PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1" />
      <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
      <PackageReference Include="Serilog.Enrichers.GlobalLogContext" Version="3.0.0" />
      <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
      <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
      <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
      <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
      <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
      <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
    </ItemGroup>

</Project>
