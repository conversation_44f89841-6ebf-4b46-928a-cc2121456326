using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Dynamic;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

using Opc.Ua;
using Opc.Ua.Client;
using Opc.Ua.Client.ComplexTypes;
using Opc.Ua.Configuration;

namespace OpcUAPlayground
{
    class Program
    {
        static void Main(string[] args)
        {
            PlaygroundClient client = new PlaygroundClient("opc.tcp://192.168.111.2:4840");
            //PlaygroundClient client = new PlaygroundClient("opc.tcp://bdu-tp15:62541/Quickstarts/ReferenceServer");
            client.Run();
        }
    }

    public class PlaygroundClient
    {
        private readonly object m_lock = new object();
        string endpointURL;
        ISession session;
        ComplexTypeSystem complexTypeSystem;
        SessionReconnectHandler reconnectHandler;
        const int ReconnectPeriod = 10;
        public int ReconnectPeriodExponentialBackoff { get; set; } = 15000;


        public PlaygroundClient(string _endpointURL )
        {
            endpointURL = _endpointURL;
            reconnectHandler = new SessionReconnectHandler(true, ReconnectPeriodExponentialBackoff);

        }

        public void Run()
        {
            try
            {
                Play().Wait();
            }
            catch(Exception ex)
            {
                Console.WriteLine("Exception: {0}", ex.Message);
                return;
            }

            ManualResetEvent quitEvent = new ManualResetEvent(false);
            try
            {
                Console.CancelKeyPress += (sender, eArgs) =>
                {
                    quitEvent.Set();
                    eArgs.Cancel = true;
                };
            }
            catch
            {
            }

            // wait for timeout or Ctrl-C
            quitEvent.WaitOne(Timeout.Infinite);
            
            session.KeepAlive -= Session_KeepAlive;
            reconnectHandler?.Dispose();
            reconnectHandler = null;

            session.Close();
            session.Dispose();
            session = null;

            Console.WriteLine("Disconnected");

        }

        private async Task Play()
        {
            ApplicationConfiguration config = new ApplicationConfiguration();

            config.ClientConfiguration = new ClientConfiguration();
            config.CertificateValidator.CertificateValidation += new CertificateValidationEventHandler(CertificateValidator_CertificateValidation);

            //var servers = CoreClientUtils.DiscoverServers(config);
            //Console.WriteLine(servers);

            var selectedEndpoint = CoreClientUtils.SelectEndpoint(endpointURL, false, 15000);
            var endpointConfiguration = EndpointConfiguration.Create(config);
            var endpoint = new ConfiguredEndpoint(null, selectedEndpoint, endpointConfiguration);
            session = await Session.Create(config,
                endpoint,
                false,
                "OPC UA Playground",
                60000,
                new UserIdentity(new AnonymousIdentityToken()),
                null);

            session.DeleteSubscriptionsOnClose = false;
            session.TransferSubscriptionsOnReconnect = true;

            Console.WriteLine("Session created : {0}", session.ToString());
            // register keep alive handler
            session.KeepAlive += Session_KeepAlive;

            await LoadTypeSystemAsync();

            //var regexTypeName = new Regex("^UDT_Seq$");

            //foreach (var type in complexTypeSystem.GetDefinedTypes())
            //{
            //    if( regexTypeName.IsMatch(type.Name) )
            //    {
            //        Console.WriteLine(type.Name);
            //    }
            //}

            //var udt_seq_type_node_id = new NodeId("ns=6;i=100010");

            //Console.WriteLine("Browse start");
            //BrowseObjectsFolder(new NodeId("ns=4;i=21008"));
            //BrowseObjectsFolder(new NodeId("ns=6;i=400005"));
            //Console.WriteLine("Browse done");

            //ReadValueIdCollection nodesToRead = new ReadValueIdCollection()
            //    {
            //        // Value of ServerStatus
            //        new ReadValueId() { NodeId = new NodeId("ns=6;s=::AsGlobalPV:ERP.Input"), AttributeId = Attributes.Value },
            //    };

            //// Read the node attributes
            //Console.WriteLine("Reading nodes...");

            //// Call Read Service
            //session.Read(
            //    null,
            //    0,
            //    TimestampsToReturn.Both,
            //    nodesToRead,
            //    out DataValueCollection resultsValues,
            //    out DiagnosticInfoCollection diagnosticInfos);

            //Console.WriteLine($"read value {resultsValues}");

            //string textbuffer = """
            //    {
            //      "TypeId": {
            //       "Id": 102770,
            //       "Namespace": 6
            //      },
            //      "Body": {
            //       "Name": "Bazooka",
            //       "Amount": 201,
            //       "Select": true
            //      }
            //    }
            //    """;

            //var v = JsonDecoder.DecodeMessage(Encoding.ASCII.GetBytes(textbuffer), new object {}.GetType(), session.MessageContext);


            dynamic eventData = new ExpandoObject();
            eventData.Name = "Bazooka";
            eventData.Amount = 999;
            eventData.ToCap = true;
            eventData.ToCol = true;

            //Console.WriteLine($"decoded value {v}");
            var nodeId1 = new NodeId("ns=6;s=::AsGlobalPV:ERP.Input[9]");
            var node1 = session.NodeCache.Find(nodeId1) as Opc.Ua.VariableNode;
            var type1 = TypeInfo.GetBuiltInType(node1.DataType);
            var expandedNodeId1 = NodeId.ToExpandedNodeId(node1.DataType, session.MessageContext.NamespaceUris); 
            var type1a = session.Factory.GetSystemType(expandedNodeId1);
            var rank = node1.ValueRank;

            //dynamic newValue = Activator.CreateInstance(type1a);

            var nodeId2 = new NodeId("ns=6;s=::AsGlobalPV:ERP.Input[9].Name");
            var node2 = session.NodeCache.Find(nodeId2) as Opc.Ua.VariableNode;
            var type2 = TypeInfo.GetBuiltInType(node2.DataType);
            var expandedNodeId2 = NodeId.ToExpandedNodeId(node2.DataType, session.MessageContext.NamespaceUris); 
            var type2a = session.Factory.GetSystemType(expandedNodeId2);
            var rank2 = node2.ValueRank;

            //var nodeId3 = new NodeId("ns=3;i=2049");
            //var node3 = session.NodeCache.Find(nodeId3) as Opc.Ua.VariableNode;
            //var type3 = TypeInfo.GetBuiltInType(node3.DataType);

            //var dataValue1 = session.ReadValue(nodeId1);
            //var dataValue1Json = JsonEncodeDataValue(session.MessageContext, dataValue1);
            //var dataValue1decoded = JsonDecodeDataValue(session.MessageContext, dataValue1Json);
            //Console.WriteLine($"read value {dataValue1}");
            //Console.WriteLine($"read value encoded {dataValue1Json}");
            //Console.WriteLine($"read value decoded {dataValue1decoded}");

            //var dataValue2 = session.ReadValue(nodeId2);
            //var dataValue2Json = JsonEncodeDataValue(session.MessageContext, dataValue2);
            //var dataValue2decoded = JsonDecodeDataValue(session.MessageContext, dataValue2Json);
            //Console.WriteLine($"read value2 {dataValue2}");
            //Console.WriteLine($"read value2 encoded {dataValue2Json}");
            //Console.WriteLine($"read value2 decoded {dataValue2decoded}");

            //var dataValue3 = session.ReadValue(nodeId3);
            //var dataValue3Json = JsonEncodeDataValue(session.MessageContext, dataValue3);
            //var dataValue3decoded = JsonDecodeDataValue(session.MessageContext, dataValue3Json);
            //Console.WriteLine($"read value3 {dataValue3}");
            //Console.WriteLine($"read value3 encoded {dataValue3Json}");
            //Console.WriteLine($"read value3 decoded {dataValue3decoded}");

            //WriteValueCollection nodesToWrite = new WriteValueCollection();

            //WriteValue writeVal = new WriteValue();
            //writeVal.NodeId = new NodeId("ns=6;s=::AsGlobalPV:ERP.Input[9]");
            //writeVal.AttributeId = Attributes.Value;
            //writeVal.Value = new DataValue();
            //writeVal.Value.Value = newValue;
            //nodesToWrite.Add(writeVal);

            //StatusCodeCollection results = null;

            //DiagnosticInfoCollection diagnosticInfos;

            //session.Write(null,
            //    nodesToWrite,
            //    out results,
            //    out diagnosticInfos);
            //Console.WriteLine("Write Results :");

            //foreach (StatusCode writeResult in results)
            //{
            //    Console.WriteLine("     {0}", writeResult);
            //}

            //BrowseObjectsFolder(ObjectIds.ObjectsFolder);
            //Subscription subscription = new Subscription(session.DefaultSubscription)
            //{
            //    DisplayName = "Console ReferenceClient Subscription",
            //    PublishingEnabled = true,
            //    PublishingInterval = 100,
            //    LifetimeCount = 0,
            //    MinLifetimeInterval = 120_000
            //};
            //session.AddSubscription(subscription);
            //subscription.Create();

            //MonitoredItem monitoredItem1 = new MonitoredItem(subscription.DefaultItem);
            //monitoredItem1.StartNodeId = new NodeId("ns=6;s=::AsGlobalPV:Count_CAPPER.Count");
            //monitoredItem1.AttributeId = Attributes.Value;
            //monitoredItem1.DisplayName = "Count_CAPPER.Count";
            //monitoredItem1.SamplingInterval = 100;
            //monitoredItem1.QueueSize = 10;
            //monitoredItem1.DiscardOldest = true;
            //monitoredItem1.Notification += OnMonitoredItemNotification;
            //subscription.AddItem(monitoredItem1);

            //MonitoredItem monitoredItem2 = new MonitoredItem(subscription.DefaultItem);
            //monitoredItem2.StartNodeId = new NodeId("ns=6;s=::AsGlobalPV:LINE.Seq.HMI.SeqStatus.ActTime");
            //monitoredItem2.AttributeId = Attributes.Value;
            //monitoredItem2.DisplayName = "LINE.Seq.HMI.SeqStatus.ActTime";
            //monitoredItem2.SamplingInterval = 100;
            //monitoredItem2.QueueSize = 10;
            //monitoredItem2.DiscardOldest = true;
            //monitoredItem2.Notification += OnMonitoredItemNotification;
            //subscription.AddItem(monitoredItem2);

            //subscription.ApplyChanges();

        }

        private DataValue DataValueForNode(object o)
        {
            var dataValue = new DataValue();
            dataValue.Value = o;
            return dataValue;
        }

        private void OnMonitoredItemNotification(MonitoredItem monitoredItem, MonitoredItemNotificationEventArgs e)
        {
            try
            {
                // Log MonitoredItem Notification event
                MonitoredItemNotification notification = e.NotificationValue as MonitoredItemNotification;
                Console.WriteLine("Notification: {0} \"{1}\" and Value = {2}.", notification.Message.SequenceNumber, monitoredItem.ResolvedNodeId, notification.Value);
            }
            catch (Exception ex)
            {
                Console.WriteLine("OnMonitoredItemNotification error: {0}", ex.Message);
            }
        }

        public static string JsonEncodeDataValue(IServiceMessageContext messageContext,DataValue value)
        {
            string textbuffer;
            using (var jsonEncoder = new JsonEncoder(messageContext, true))
            {
                jsonEncoder.WriteDataValue("data_value", value);
                textbuffer = jsonEncoder.CloseAndReturnText();
            }
            return textbuffer;
        }

        public static DataValue JsonDecodeDataValue(IServiceMessageContext messageContext, string json)
        {
            DataValue dataValue = null;
            using (var jsonDecoder = new JsonDecoder(json,messageContext))
            {
                dataValue = jsonDecoder.ReadDataValue("data_value");
            }

            return dataValue;
        }

        private void BrowseObjectsFolder(NodeId node, string indent = "")
        {
            Dictionary<string, List<string>> subscribeTypes = new Dictionary<string, List<string>>() 
            {
                { "UDT_SeqHmi", new List<string> { ".Status.HMIStat", ".SeqStatus.ActStp" } },
                { "UDT_ProcessMode", new List<string> { ".sMode" } },
                { "UDT_CM_MTR_IO", new List<string> { ".Pi.ReqFwd", ".Po.ActCurrent" } },
                { "UDT_CM_DV_IO", new List<string> { ".Pi.ReqOpnForUp" } },
                { "UDT_CM_AV_IO", new List<string> { ".Pi.ReqPos" } },
                { "UDT_CM_AI_IO", new List<string> { ".Po.Val" } },
                { "UDT_CM_PENKO1020_IO", new List<string> { ".Po.Val", ".Po.Status.STABLE" } },
                { "UDT_CM_DI_IO", new List<string> { ".Po.Det" } },
                { "UDT_PRDTYPE_PO_INFO", new List<string> { ".ID" } },
                { "UDT_CM_FT_IO", new List<string> { ".Po.FT", ".Po.TotVol1", ".Po.TotVol2", ".Po.TotVol3" } },
            };

            ReferenceDescriptionCollection references;
            Byte[] continuationPoint;

            var resp = session.Browse(
                null,
                null,
                node,
                0u,
                BrowseDirection.Forward,
                ReferenceTypeIds.HierarchicalReferences,
                true,
                (uint)NodeClass.Variable,
                out continuationPoint,
                out references);


            ReadValueIdCollection nodesToRead = new ReadValueIdCollection();

            foreach (var rd in references)
            {
                //Console.WriteLine($"refnode {rd.NodeId}");

                uint[] attributes = [
                    Attributes.DataType
                    //Attributes.ValueRank,
                    //Attributes.ArrayDimensions,
                    //Attributes.MinimumSamplingInterval
                ];

                foreach (uint attributeId in attributes)
                {

                    ReadValueId valueId = new ReadValueId();

                    valueId.NodeId = ExpandedNodeId.ToNodeId(rd.NodeId, session.NamespaceUris);
                    valueId.AttributeId = attributeId;
                    valueId.IndexRange = null;
                    valueId.DataEncoding = null;

                    nodesToRead.Add(valueId);
                }
            }

            if( nodesToRead.Count > 0 )
            {
                DataValueCollection values;
                DiagnosticInfoCollection diagnosticInfos;

                session.Read(
                    null,
                    0,
                    TimestampsToReturn.Neither,
                    nodesToRead,
                    out values,
                    out diagnosticInfos);

                ClientBase.ValidateResponse(values, nodesToRead);
                ClientBase.ValidateDiagnosticInfos(diagnosticInfos, nodesToRead);

                string dataType = null;
                for (int ii = 0; ii < nodesToRead.Count; ii++)
                {
                    // check if node supports attribute.
                    if (values[ii].StatusCode == StatusCodes.BadAttributeIdInvalid)
                    {
                        continue;
                    }

                    //Console.WriteLine("{0} {1}",
                    //    Attributes.GetBrowseName(nodesToRead[ii].AttributeId),
                    //    FormatAttributeValue(nodesToRead[ii].AttributeId, values[ii].Value));

                    if (nodesToRead[ii].AttributeId == Attributes.DataType)
                    {
                        dataType = FormatAttributeValue(nodesToRead[ii].AttributeId, values[ii].Value);
                    }


                    if( subscribeTypes.ContainsKey(dataType) )
                    {
                        foreach( var suffix in subscribeTypes[dataType])
                        {
                            Console.WriteLine("{{ \"node_id\": \"{0}{1}\" }},",
                                nodesToRead[ii].NodeId,
                                suffix);
                        }
                    } 
                    //else if( dataType == "UDT_CMG_HEAD" )
                    //{
                    //    Console.WriteLine("HEAD");
                    //    var cmg_node_cf = new NodeId(nodesToRead[ii].NodeId + ".Cf");
                    //    var cmg_cf = session.ReadValue(cmg_node_cf);
                    //    Console.WriteLine(cmg_cf.ToString());
                    //}
                    else
                    {
                        if (dataType.StartsWith("UDT_") && !dataType.StartsWith("UDT_CM_") )
                        {
                            BrowseObjectsFolder(ExpandedNodeId.ToNodeId(nodesToRead[ii].NodeId, session.NamespaceUris), indent + ".");
                        }
                        else
                        {
                            //Console.WriteLine(dataType);
                        }
                    }
                }
            }


            //Console.WriteLine("{0} {1}, {2}, {3}, {4}", indent, rd.DisplayName, rd.BrowseName, rd.NodeClass, rd.NodeId);

            //if( dataType == "UDT_Seq" )
            //{
            //Console.WriteLine("{0} {1} {2}", indent, rd.NodeId, dataType);
            //}

            //}
        }

        private string FormatAttributeValue(uint attributeId, object value)
        {
            switch (attributeId)
            {
                case Attributes.NodeClass:
                    {
                        if (value != null)
                        {
                            return String.Format("{0}", Enum.ToObject(typeof(NodeClass), value));
                        }

                        return "(null)";
                    }

                case Attributes.DataType:
                    {
                        NodeId datatypeId = value as NodeId;

                        if (datatypeId != null)
                        {
                            INode datatype = session.NodeCache.Find(datatypeId);

                            if (datatype != null)
                            {
                                return String.Format("{0}", datatype.DisplayName.Text);
                            }
                            else
                            {
                                return String.Format("{0}", datatypeId);
                            }
                        }

                        return String.Format("{0}", value);
                    }

                case Attributes.ValueRank:
                    {
                        int? valueRank = value as int?;

                        if (valueRank != null)
                        {
                            switch (valueRank.Value)
                            {
                                case ValueRanks.Scalar: return "Scalar";
                                case ValueRanks.OneDimension: return "OneDimension";
                                case ValueRanks.OneOrMoreDimensions: return "OneOrMoreDimensions";
                                case ValueRanks.Any: return "Any";

                                default:
                                    {
                                        return String.Format("{0}", valueRank.Value);
                                    }
                            }
                        }

                        return String.Format("{0}", value);
                    }

                case Attributes.MinimumSamplingInterval:
                    {
                        double? minimumSamplingInterval = value as double?;

                        if (minimumSamplingInterval != null)
                        {
                            if (minimumSamplingInterval.Value == MinimumSamplingIntervals.Indeterminate)
                            {
                                return "Indeterminate";
                            }

                            else if (minimumSamplingInterval.Value == MinimumSamplingIntervals.Continuous)
                            {
                                return "Continuous";
                            }

                            return String.Format("{0}", minimumSamplingInterval.Value);
                        }

                        return String.Format("{0}", value);
                    }

                case Attributes.AccessLevel:
                case Attributes.UserAccessLevel:
                    {
                        byte accessLevel = Convert.ToByte(value);

                        StringBuilder bits = new StringBuilder();

                        if ((accessLevel & AccessLevels.CurrentRead) != 0)
                        {
                            bits.Append("Readable");
                        }

                        if ((accessLevel & AccessLevels.CurrentWrite) != 0)
                        {
                            if (bits.Length > 0)
                            {
                                bits.Append(" | ");
                            }

                            bits.Append("Writeable");
                        }

                        if ((accessLevel & AccessLevels.HistoryRead) != 0)
                        {
                            if (bits.Length > 0)
                            {
                                bits.Append(" | ");
                            }

                            bits.Append("History");
                        }

                        if ((accessLevel & AccessLevels.HistoryWrite) != 0)
                        {
                            if (bits.Length > 0)
                            {
                                bits.Append(" | ");
                            }

                            bits.Append("History Update");
                        }

                        if (bits.Length == 0)
                        {
                            bits.Append("No Access");
                        }

                        return String.Format("{0}", bits);
                    }

                case Attributes.EventNotifier:
                    {
                        byte notifier = Convert.ToByte(value);

                        StringBuilder bits = new StringBuilder();

                        if ((notifier & EventNotifiers.SubscribeToEvents) != 0)
                        {
                            bits.Append("Subscribe");
                        }

                        if ((notifier & EventNotifiers.HistoryRead) != 0)
                        {
                            if (bits.Length > 0)
                            {
                                bits.Append(" | ");
                            }

                            bits.Append("History");
                        }

                        if ((notifier & EventNotifiers.HistoryWrite) != 0)
                        {
                            if (bits.Length > 0)
                            {
                                bits.Append(" | ");
                            }

                            bits.Append("History Update");
                        }

                        if (bits.Length == 0)
                        {
                            bits.Append("No Access");
                        }

                        return String.Format("{0}", bits);
                    }

                default:
                    {
                        return String.Format("{0}", value);
                    }
            }
        }


        private void Session_KeepAlive(ISession session, KeepAliveEventArgs e)
        {
            try
            {
                // check for events from discarded sessions.
                if (!session.Equals(session))
                {
                    return;
                }

                // start reconnect sequence on communication error.
                if (ServiceResult.IsBad(e.Status))
                {
                    //if (ReconnectPeriod <= 0)
                    //{
                    //    Utils.LogWarning("KeepAlive status {0}, but reconnect is disabled.", e.Status);
                    //    return;
                    //}

                    var state = reconnectHandler.BeginReconnect(session, ReconnectPeriod, Client_ReconnectComplete);
                    if (state == SessionReconnectHandler.ReconnectState.Triggered)
                    {
                        Console.WriteLine("KeepAlive status {0}, reconnect status {1}, reconnect period {2}ms.", e.Status, state, ReconnectPeriod);
                    }
                    else
                    {
                        Console.WriteLine("KeepAlive status {0}, reconnect status {1}.", e.Status, state);
                    }

                    // cancel sending a new keep alive request, because reconnect is triggered.
                    e.CancelKeepAlive = true;

                    return;
                }
            }
            catch (Exception exception)
            {
                Console.WriteLine("Error in OnKeepAlive. {0}", exception);
            }
        }
        public async Task LoadTypeSystemAsync()
        {
            Console.WriteLine("Load the server type system.");

            Stopwatch stopWatch = new Stopwatch();
            stopWatch.Start();

            complexTypeSystem = new ComplexTypeSystem(session);
            await complexTypeSystem.Load().ConfigureAwait(false);

            stopWatch.Stop();

            Console.WriteLine($"Loaded {complexTypeSystem.GetDefinedTypes().Length} types took {stopWatch.ElapsedMilliseconds}ms.");

            if (false)
            {
                Console.WriteLine("Custom types defined for this session:");
                foreach (var type in complexTypeSystem.GetDefinedTypes())
                {
                    
                    Console.WriteLine($"{type.Namespace}.{type.Name}");
                }

                Console.WriteLine($"Loaded {session.DataTypeSystem.Count} dictionaries:");
                foreach (var dictionary in session.DataTypeSystem)
                {
                    Console.WriteLine($" + {dictionary.Value.Name}");
                    foreach (var type in dictionary.Value.DataTypes)
                    {
                        Console.WriteLine($" -- {type.Key}:{type.Value}");
                    }
                }
            }
        }


        /// <summary>
        /// Called when the reconnect attempt was successful.
        /// </summary>
        private void Client_ReconnectComplete(object sender, EventArgs e)
        {
            // ignore callbacks from discarded objects.
            if (!Object.ReferenceEquals(sender, reconnectHandler))
            {
                return;
            }

            lock (m_lock)
            {
                // if session recovered, Session property is null
                if (reconnectHandler.Session != null)
                {
                    // ensure only a new instance is disposed
                    // after reactivate, the same session instance may be returned
                    if (!Object.ReferenceEquals(session, reconnectHandler.Session))
                    {
                        Console.WriteLine("--- RECONNECTED TO NEW SESSION --- {0}", reconnectHandler.Session.SessionId);
                        var _session = session;
                        session = reconnectHandler.Session;
                        Utils.SilentDispose(_session);
                    }
                    else
                    {
                        Console.WriteLine("--- REACTIVATED SESSION --- {0}", reconnectHandler.Session.SessionId);
                    }
                }
                else
                {
                    Console.WriteLine("--- RECONNECT KeepAlive recovered ---");
                }
            }
        }
        private static void CertificateValidator_CertificateValidation(CertificateValidator validator, CertificateValidationEventArgs e)
        {
            var autoAccept = true;
            if (e.Error.StatusCode == StatusCodes.BadCertificateUntrusted)
            {
                e.Accept = autoAccept;
                if (autoAccept)
                {
                    Console.WriteLine("Accepted Certificate: {0}", e.Certificate.Subject);
                }
                else
                {
                    Console.WriteLine("Rejected Certificate: {0}", e.Certificate.Subject);
                }
            }
        }

    }
}
