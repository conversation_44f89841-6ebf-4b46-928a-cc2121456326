{
  "nodes": [
    {
      "id": 1,
      "name": "plc_keg",
      "type": "plc",
      "enabled": true,
      "config": {
        //"ip": "*************",
        "ip": "*************",
        "rack": 0,
        "slot": 1,
        "poll_interval": 100,
        //"poll_interval": 1000,
        "dbs": [
          {
            "topic": "plc.general",
            "db": 10310,
            "length": 124,
            "poll": true
          }, // general
          {
            "topic": "plc.process",
            "db": 10301,
            "length": 1760,
            "poll": true
          }, // process
          {
            "topic": "plc.cip",
            "db": 10302,
            "length": 704,
            "poll": true
          }, // cip
          {
            "topic": "plc.purge",
            "db": 10303,
            "length": 704,
            "poll": true
          }, // purge
          {
            "topic": "plc.tanks",
            "db": 10304,
            "length": 704,
            "poll": true
          }, // tanks
          {
            "topic": "plc.transport_keg",
            "db": 10305,
            "length": 1760,
            "poll": true
          }, // transport_keg
          {
            "topic": "plc.transport_pallet",
            "db": 10306,
            "length": 1760,
            "poll": true
          } // transport_pallet
        ]
      }
      
    }, // PLC KEG
    {
      "id": 3,
      "name": "decoder",
      "type": "decoder",
      "enabled": true,
      "config": {
        "schemas": {
          "udt_kb_ce": {
            "counter": "s7.dint",
            "seq": {
              "msg_id": "s7.int",
              "msg_step": "s7.int",
              "msg_stat": "s7.int",
              "active_step": "s7.int",
              "val": "s7.real"
            },
            "ce_io": {
              "di_do": "s7.dword",
              "values": {
                "$array": 5,
                "$refs": "s7.real"
              }
            },
            "ce_status": {
              "Action": "s7.int",
              "Status": "s7.int"
            }
          },
          "db_general": {
            "watchdog_in": "s7.int",
            "watchdog_out": "s7.int",
            "modes": {
              "retour": "s7.int",
              "ow": "s7.int"
            },
            "kegtypes": {
              "infeed": "s7.int",
              "pw": "s7.int",
              "soaking": "s7.int",
              "mw": "s7.int",
              "switch": "s7.int",
              "f": "s7.int",
              "outfeed": "s7.int",
              "keg_pal_infeed": "s7.int",
              "pal_infeed": "s7.int",
              "pal_buffer": "s7.int",
              "ow": "s7.int"
            },
            "bits": {
              "$array": 2,
              "$refs": "le.dword"
            },
            "reals": {
              "$array": 16,
              "$refs": "s7.real"
            },
            "ints": {
              "$array": 11,
              "$refs": "s7.int"
            }
          },
          "db_process": {
            "ce": {
              "$array": 40,
              "$refs": "udt_kb_ce"
            }
          },
          "db_cip": {
            "ce": {
              "$array": 16,
              "$refs": "udt_kb_ce"
            }
          },
          "db_purge": {
            "ce": {
              "$array": 16,
              "$refs": "udt_kb_ce"
            }
          },
          "db_tanks": {
            "ce": {
              "$array": 16,
              "$refs": "udt_kb_ce"
            }
          },
          "db_transport": {
            "ce": {
              "$array": 40,
              "$refs": "udt_kb_ce"
            }
          }
        },
        "decode": [
          {
            "in_topic": "plc.general",
            "schema": "db_general",
            "out_topic": "db.general"
          },
          {
            "in_topic": "plc.process",
            "schema": "db_process",
            "out_topic": "db.process"
          },
          {
            "in_topic": "plc.purge",
            "schema": "db_purge",
            "out_topic": "db.purge"
          },
          {
            "in_topic": "plc.cip",
            "schema": "db_cip",
            "out_topic": "db.cip"
          },
          {
            "in_topic": "plc.tanks",
            "schema": "db_tanks",
            "out_topic": "db.tanks"
          },
          {
            "in_topic": "plc.transport_keg",
            "schema": "db_transport",
            "out_topic": "db.transport_keg"
          },
          {
            "in_topic": "plc.transport_pallet",
            "schema": "db_transport",
            "out_topic": "db.transport_pallet"
          }
        ]
      }
    }, // DECODER
    {
      "id": 4,
      "type": "script",
      "name": "keg_processor",
      "config": {
        "remoteDebug": true,
        "topics": [
          "db.general",
          "db.tanks",
          "db.cip",
          "db.process",
          "db.purge",
          "db.transport_keg",
          "db.transport_pallet"
        ],
        "script": "HalveMaan/KegProcessor"
      }
    } // SCRIPT KEG
    ,{
      "id": 5,
      "type": "influx",
      "name": "influx_keg",
      "config": {
        "url": "http://localhost:8086",
        "database": "halvemaan_vl",
        "retention": "autogen",
        "topics": [
          "influx.keg"
        ],
        "batch_size": 1000,
        "flush_interval": 1000
      }
    }, // INFLUX KEG
    {
      "id": 6,
      "type": "hmi",
      "name": "hmi_100",
      "config": {
        "url": "http://***************",
        "logfile_path": "/StorageCardSD/Logs/Alarm_log0.csv",
        "username": "Administrator",
        "password": "Gans2870",
        "poll_interval": 300000
      }
    }, // HMI alarm csv scraper
    {
      "type": "robot",
      "name": "r_111",
      "config": {
        "url": "http://***************",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "r_115",
      "config": {
        "url": "http://***************",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "r_117",
      "config": {
        "url": "http://192.168.111.117",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "r_119",
      "config": {
        "url": "http://192.168.111.119",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    }
  ]
}