{
  "nodes": [
    {
      "id": 1,
      "name": "plc_keg",
      "type": "plc",
      "enabled": true,
      "config": {
        "ip": "*************",
        "rack": 0,
        "slot": 1,
        "poll_interval": 100,
        //"poll_interval": 1000,
        "dbs": [
          {
            "topic": "plc.ce_db",
            "db": 6001,
            "length": 2112,
            "poll": true
          },
          {
            "topic": "plc.cm_db",
            "db": 6000,
            "length": 1710,
            "poll": true
          },
          {
            "topic": "plc.general",
            "db": 6002,
            "length": 148,
            "poll": true
          },
           {
            "topic": "plc.cheops_to_lam",
            "db": 76,
            "length": 6,
            "poll": true
          },
          {
            "topic": "plc.lam_to_cheops",
            "db": 77,
            "length": 86,
            "poll": true
          }
        ]
      }
    }, // PLC KEG
    {
      "id": 3,
      "name": "decoder",
      "type": "decoder",
      "enabled": true,
      "config": {
        "schema_dbs": [
          "scripts/Belhaven/DB_CheopsToLam.db",
          "scripts/Belhaven/DB_LamToCheops.db",
          "scripts/Belhaven/KegbridgeCE.db",
          "scripts/Belhaven/KegbridgeCM.db",
          "scripts/Belhaven/KegbridgeGeneral.db"
        ],
        "schemas": {
        },
        "decode": [
          {
            "in_topic": "plc.ce_db",
            "schema": "KegbridgeCE",
            "out_topic": "db.ces"
          },
          {
            "in_topic": "plc.cm_db",
            "schema": "KegbridgeCM",
            "out_topic": "db.cms"
          },
          {
            "in_topic": "plc.general",
            "schema": "KegbridgeGeneral",
            "out_topic": "db.general"
          },
           {
            "in_topic": "plc.cheops_to_lam",
            "schema": "DB_CheopsToLam",
            "out_topic": "db.cheops_to_lam"
          },
          {
            "in_topic": "plc.lam_to_cheops",
            "schema": "DB_LamToCheops",
            "out_topic": "db.lam_to_cheops"
          }
        ]
      }
    }, // DECODER
    {
      "id": 4,
      "type": "script",
      "name": "keg_processor",
      "enabled": true,
      "config": {
        "remoteDebug": true,
        "topics": [
          "db.cms",
          "db.ces",
          "db.general",
          "db.cheops_to_lam",
          "db.lam_to_cheops"
        ],
        "script": "Belhaven/KegProcessor"
      }
    }, // SCRIPT KEG
    {
      "id": 5,
      "type": "influx",
      "name": "influx_keg",
      "enabled": true,
      "config": {
        "url": "http://**************:8086?org=belhaven&bucket=kegline&token=lFS_r6NKhThm7DYRNeVrY6UUjFP-Q9VmI-WQ1Tz_E750r05Sb66dqkkBSHyjYH_pG54j-geUH_pDJ1vlRThuew==",
        "topics": [
          "influx.keg"
        ],
        "batch_size": 1000,
        "flush_interval": 1000
      }
    }, // INFLUX KEG
    {
      "id": 8,
      "type": "hmi",
      "name": "hmi_100",
      "enabled": true,
      "config": {
        "url": "http://***************",
        "logfile_path": "/StorageCardSD/Logs/Alarm_log0.csv",
        "username": "kegbridge",
        "password": "kb2870",
        "poll_interval": 60000 
      }
    },
    {
      "type": "robot",
      "name": "depal",
      "enabled": true,
      "config": {
        "url": "http://***************",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "pal",
      "enabled": true,
      "config": {
        "url": "http://192.168.111.112",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    }
  ]
}
