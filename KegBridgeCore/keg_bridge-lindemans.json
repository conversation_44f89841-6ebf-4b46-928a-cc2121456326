{"nodes": [{"id": 1, "name": "plc_keg", "type": "plc", "enabled": true, "config": {"ip": "*************", "rack": 0, "slot": 1, "poll_interval": 100, "dbs": [{"topic": "plc.general_io", "db": 10310, "length": 146, "poll": true}, {"topic": "plc.transport", "db": 10305, "length": 704, "poll": false}, {"topic": "plc.filler_process", "db": 10301, "length": 704, "poll": true}, {"topic": "plc.filler_cip", "db": 10302, "length": 352, "poll": true}, {"topic": "plc.filler_purge", "db": 10303, "length": 352, "poll": true}, {"topic": "plc.tanks", "db": 10304, "length": 704, "poll": true}, {"topic": "plc.safety", "db": 10320, "length": 80, "poll": false}]}}, {"id": 3, "name": "decoder", "type": "decoder", "enabled": true, "config": {"schemas": {"udt_kb_ce": {"counter": "s7.dint", "seq": {"msg_id": "s7.int", "msg_step": "s7.int", "msg_stat": "s7.int", "active_step": "s7.int", "val": "s7.real"}, "CE_IO": {"di_do": "s7.dword", "values": {"$array": 5, "$refs": "s7.real"}}, "ce_status": {"Action": "s7.int", "Status": "s7.int"}}, "transport": {"ce": {"$array": 16, "$refs": "udt_kb_ce"}}, "general": {"watchdog_in": "s7.int", "watchdog_out": "s7.int", "mode": {"retour": "s7.int", "ow": "s7.int", "_2": "s7.int", "_3": "s7.int", "_4": "s7.int", "_5": "s7.int", "_6": "s7.int", "_7": "s7.int"}, "kegtype": {"infeed": "s7.int", "pw": "s7.int", "f": "s7.int", "ow": "s7.int", "_4": "s7.int", "_5": "s7.int", "_6": "s7.int", "_7": "s7.int"}, "media": {"product_retour": "s7.int", "product_ow": "s7.int", "cip_retour": "s7.int", "cip_ow": "s7.int", "_4": "s7.int", "_5": "s7.int", "_6": "s7.int", "_7": "s7.int"}, "bits": {"$array": 2, "$refs": "le.dword"}, "reals": {"$array": 16, "$refs": "s7.real"}, "ints": {"$array": 11, "$refs": "s7.int"}}, "safety": {"ce": {"$array": 40, "$refs": "s7.word"}}, "tanks": {"ce": {"$array": 16, "$refs": "udt_kb_ce"}}, "filler_process": {"ce": {"$array": 16, "$refs": "udt_kb_ce"}}, "filler_cip": {"ce": {"$array": 8, "$refs": "udt_kb_ce"}}, "filler_purge": {"ce": {"$array": 8, "$refs": "udt_kb_ce"}}}, "decode": [{"in_topic": "plc.general_io", "schema": "general", "out_topic": "db.general_io"}, {"in_topic": "plc.transport", "schema": "transport", "out_topic": "db.transport"}, {"in_topic": "plc.filler_process", "schema": "filler_process", "out_topic": "db.filler_process"}, {"in_topic": "plc.filler_purge", "schema": "filler_purge", "out_topic": "db.filler_purge"}, {"in_topic": "plc.filler_cip", "schema": "filler_cip", "out_topic": "db.filler_cip"}, {"in_topic": "plc.tanks", "schema": "tanks", "out_topic": "db.tanks"}, {"in_topic": "plc.safety", "schema": "safety", "out_topic": "db.safety"}]}}, {"id": 4, "type": "script", "name": "keg_processor", "config": {"topics": ["db.general_io", "db.transport", "db.filler_process", "db.filler_purge", "db.filler_cip", "db.tanks", "db.safety"], "script": "Lindemans/KegProcessor"}}, {"id": 5, "type": "influx", "name": "influx_keg", "config": {"url": "http://localhost:8086", "database": "lindemans_vl", "retention": "autogen", "topics": ["influx.keg"], "batch_size": 1000, "flush_interval": 1000}}]}