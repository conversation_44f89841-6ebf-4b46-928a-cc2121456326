import '../css/style.css'

import * as Turbo from '@hotwired/turbo'
Turbo.session.drive = true

import './signalRTurboStreamElement'

import { Application } from "@hotwired/stimulus"
import { registerControllers } from 'stimulus-vite-helpers'

const application = Application.start();
window.Stimulus = application

const controllers = import.meta.glob('./**/*_controller.js', { eager: true })
console.log('Stimulus controllers:', controllers);

registerControllers(application, controllers);
