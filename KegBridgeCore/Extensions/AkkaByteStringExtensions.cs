using Akka.IO;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace KegBridgeCore.Extensions;

public static class AkkaByteStringExtensions
{

    /// <summary>
    /// Finds the index of the first occurrence of <paramref name="pattern"/> in the <paramref name="source"/> 
    /// at or after <paramref name="fromIndex"/>. Returns -1 if not found.
    /// </summary>
    public static int IndexOfSlice(this ByteString source, ByteString pattern, int fromIndex = 0)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (pattern == null) throw new ArgumentNullException(nameof(pattern));
        if (fromIndex < 0 || fromIndex >= source.Count) return -1;
        if (pattern.IsEmpty) return fromIndex;

        int last = source.Count - pattern.Count;
        for (int i = fromIndex; i <= last; i++)
        {
            if (source.HasSubstring(pattern, i))
                return i;
        }
        return -1;
    }

    /// <summary>
    /// Extracts the first frame delimited by <paramref name="startMarker"/> and <paramref name="endMarker"/>.
    /// Returns the frame (optionally including delimiters) and the buffer with that frame removed.
    /// Uses only ByteString slicing and concatenation (no payload copies).
    /// </summary>
    public static bool TryExtractFrame(
        this ByteString buffer,
        ByteString startMarker,
        ByteString endMarker,
        int fromIndex,
        bool includeDelimiters,
        out ByteString frame,
        out ByteString newBuffer)
    {
        if (buffer == null) throw new ArgumentNullException(nameof(buffer));
        if (startMarker == null) throw new ArgumentNullException(nameof(startMarker));
        if (endMarker == null) throw new ArgumentNullException(nameof(endMarker));

        frame = default!;
        newBuffer = buffer;

        if (buffer.IsEmpty || startMarker.IsEmpty || endMarker.IsEmpty) return false;
        if (fromIndex < 0 || fromIndex >= buffer.Count) return false;

        // Find start
        int start = buffer.IndexOfSlice(startMarker, fromIndex);
        if (start < 0) return false;

        // Find end after start
        int contentStart = start + startMarker.Count;
        int end = buffer.IndexOfSlice(endMarker, contentStart);
        if (end < 0) return false;

        // Build frame slice
        int frameStart = includeDelimiters ? start : contentStart;
        int frameLength = includeDelimiters
            ? (end + endMarker.Count - start)
            : (end - contentStart);
        frame = buffer.Slice(frameStart, frameLength);

        // Remove the [start .. end+endMarker.Count) region
        int removeStart = start;
        int removeLength = (end - start) + endMarker.Count;

        var left = removeStart > 0 ? buffer.Slice(0, removeStart) : ByteString.Empty;
        var rightStart = removeStart + removeLength;
        var right = rightStart < buffer.Count
            ? buffer.Slice(rightStart, buffer.Count - rightStart)
            : ByteString.Empty;

        newBuffer = left.Concat(right);
        return true;
    }

    /// <summary>
    /// Extracts all frames in sequence until none remain.
    /// </summary>
    public static IReadOnlyList<ByteString> ExtractAllFrames(
        this ByteString buffer,
        ByteString startMarker,
        ByteString endMarker,
        bool includeDelimiters,
        out ByteString remainder)
    {
        var frames = new List<ByteString>();
        var current = buffer;

        while (current.TryExtractFrame(startMarker, endMarker, 0, includeDelimiters, out var frame, out var next))
        {
            frames.Add(frame);
            current = next;
        }

        remainder = current;
        return frames;
    }


    /// <summary>
    /// Hex dump similar to `hexdump -C`, with 8-digit offsets, grouped hex, and ASCII.
    /// </summary>
    public static string ToHexDump(this ByteString data, int bytesPerLine = 16)
    {
        if (data == null || data.Count == 0)
            return string.Empty;

        if (bytesPerLine <= 0)
            bytesPerLine = 16;

        var sb = new StringBuilder();

        for (int i = 0; i < data.Count; i += bytesPerLine)
        {
            // Offset
            sb.Append(i.ToString("X8")).Append("  ");

            // Hex bytes (with middle spacer)
            for (int j = 0; j < bytesPerLine; j++)
            {
                if (i + j < data.Count)
                    sb.Append(data[i + j].ToString("X2")).Append(' ');
                else
                    sb.Append("   ");

                if (j == (bytesPerLine / 2) - 1)
                    sb.Append(' ');
            }

            sb.Append(' ');

            // ASCII
            for (int j = 0; j < bytesPerLine; j++)
            {
                if (i + j < data.Count)
                {
                    byte b = data[i + j];
                    sb.Append(b >= 32 && b <= 126 ? (char)b : '.');
                }
                else
                {
                    sb.Append(' ');
                }
            }

            sb.AppendLine();
        }

        return sb.ToString();
    }

    /// <summary>
    /// Simple space-separated hex (no offsets/ASCII), wrapped to bytesPerLine columns.
    /// </summary>
    public static string ToHexRows(this ByteString data, int bytesPerLine = 16)
    {
        if (data == null || data.Count == 0)
            return string.Empty;

        if (bytesPerLine <= 0)
            bytesPerLine = 16;

        var sb = new StringBuilder(data.Count * 3);
        for (int i = 0; i < data.Count; i++)
        {
            sb.Append(data[i].ToString("X2"));
            if ((i + 1) % bytesPerLine == 0)
                sb.AppendLine();
            else
                sb.Append(' ');
        }
        return sb.ToString().TrimEnd();
    }
}