enabled: true
nodes:
  - name: plc
    type: plc
    enabled: true
    config:
      ip: "************"
      rack: 0
      slot: 1
      poll_interval: 0
      dbs: 
        - db_number: 200
          length: 118
        - db_number: 201
          length: 72
  
  - name: opcua
    type: opcua
    enabled: false
    config:
      server_url: "opc.tcp://************:4840"
      publishing_interval: 10000
      monitored_items:
        - node_id: ns=3;s="DBTEST1"."struct"
          sampling_interval: 10000
        - node_id: ns=3;s="DBTEST1"."struct"."Counter"
          sampling_interval: 10000
        - node_id: ns=3;s="DBTEST2"."struct"
          sampling_interval: 10000
        - node_id: ns=3;s="DBTEST2"."struct"."Counter"
          sampling_interval: 10000
  
  - name: udp
    type: udp_server
    enabled: false
    config:
      local_endpoint: *************:5000

  - name: tcp
    type: tcp_server
    enabled: true
    config:
      local_endpoint: *************:5001
      start_byte_frame_marker: "0B36"
      end_byte_frame_marker: "1B36"

  - name: identify_db
    type: transform
    enabled: true
    config:
      modify:
        - key: start_marker
          value_expression: "getUInt16BE([data.frame], 0)"
        - key: end_marker
          value_expression: "getUInt16BE([data.frame], -2)"
        - key: plc_db_number
          value_expression: "getUInt16BE([data.frame], 2)"
        - key: timestamp
          value_expression: "getDateTimeBE([data.frame], 4)"

  - name: decoder
    type: decoder
    enabled: true
    config:
      schemas:
        DBTEST1:
          _start: s7.word
          _db_id: s7.word
          _epoch_ns: s7.ulong
          ArData:
            $array: 100
            $refs: s7.byte
          Counter: s7.word
          _end: s7.word
        DBTEST2:
          _start: s7.word
          _db_id: s7.word
          _epoch_ns: s7.ulong
          ArData:
            $array: 52
            $refs: s7.byte
          Counter: s7.real
          _end: s7.word

  - name: dummy
    type: dummy
    config:
      identifier: "dummy_identifier"

  - name: transform
    type: transform
    config:
      modify:
        - key: first
          value: true
          if: "{data.nested.value} > 100"
        - key: second
          value: "3"
          if: "0"
        - key: topic
          value: bart
          if: "[data.value] == 100"
        - key: fourth
          if: "[node] == 'erp'"
        - key: fifth
          value_expression: "[data.value] * 2"
          if: "topic == 'erp_order'"
        - key: sixth
          value_expression: "concat(node,topic,id,timestamp)"
          if: true
      delete:
        - key: 'nested.value'
          
  - name: erp
    type: dummy
    config:
      identifier: "erp_identifier"

wires:
  - from: plc
    to: dummy
  - from: opcua
    to: dummy
    
  - from: tcp
    to: identify_db
  - from: identify_db
    to: dummy
    
  - from: erp
    to: transform
  - from: transform  
    to: dummy



