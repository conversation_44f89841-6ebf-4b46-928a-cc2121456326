using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Akka.Actor;
using KegBridgeCore.Actors;
using KegBridgeCore.Data;
using KegBridgeCore.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Microsoft.AspNetCore.Http;
using System;
using Akka.Event;
using Akka.Hosting;
using Akka.Logger.Serilog;

namespace KegBridgeCore.Controllers;

[Route("api/[controller]")]
[ApiController]
public class EventsController(IRequiredActor<FlowManager> flowManager, ILogger<EventsController> logger)
    : ControllerBase
{
    private readonly IActorRef _flowManager = flowManager.ActorRef;
    private readonly ILogger<EventsController> _logger = logger;
    
    [HttpPost("{*nodeTopic}")]
    public async Task<IActionResult> PublishEvent(string nodeTopic)
    {
        ExpandoObject payload = new ExpandoObject();

        if (Request.HasJsonContentType())
        {
            using StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8);
            var jsonStringBody = await reader.ReadToEndAsync();

            try
            {
                var converter = new ExpandoObjectConverter();
                payload = JsonConvert.DeserializeObject<ExpandoObject>(jsonStringBody, converter);

            }
            catch (JsonReaderException ex)
            {
                return UnprocessableEntity(new { error = ex.Message });
            }
        }
        else if (Request.HasFormContentType)
        {
            var payloadDict = payload as IDictionary<string, object>;
            foreach( var f in Request.Form)
            {
                var values = f.Value;
                Object value = null;
                if( values.Count == 1 )
                {
                    value = values[0];
                } 
                else
                {
                    value = values;
                }

                payloadDict[f.Key] = value;
            }
                
        }

        if (payload != null)
        {
            // Convert byte array signatures
            try
            {
                var processedPayload = ByteArrayConverter.ProcessObject(payload);
                if (processedPayload is ExpandoObject expandoResult)
                {
                    payload = expandoResult;
                }
                else if (processedPayload is IDictionary<string, object> dictResult)
                {
                    // Convert dictionary back to ExpandoObject
                    payload = new ExpandoObject();
                    var payloadDict = payload as IDictionary<string, object>;
                    foreach (var kvp in dictResult)
                    {
                        payloadDict[kvp.Key] = kvp.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to convert byte array signatures in payload");
                return BadRequest(new { error = "Invalid byte array signature format", details = ex.Message });
            }

            string[] parts = nodeTopic.Split('/', 2);
            string eventNode = null, eventTopic = null;

            if (parts.Length > 0)
            {
                eventNode = parts[0];
                if (parts.Length > 1)
                {
                    eventTopic = parts[1];
                }
            }
            else
            {
                eventNode = "http_event_node";
            }

            if (eventTopic == null)
            {
                eventTopic = "http_event_data";
            }

            NodeEvent nodeEvent = NodeEvent.FromExpando(eventNode, payload, eventTopic);

            if (nodeEvent != null)
            {
                _flowManager.Tell(nodeEvent);
            }

            return Ok();
        }

        return BadRequest();
    }

}
