<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>KegBridgeCore</RootNamespace>
    <StartupObject>KegBridgeCore.Program</StartupObject>
    <ImplicitUsings>disable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="erp_data\**" />
    <Compile Remove="logs\**" />
    <Compile Remove="scripts\**" />
    <Content Remove="erp_data\**" />
    <Content Remove="logs\**" />
    <Content Remove="scripts\**" />
    <EmbeddedResource Remove="erp_data\**" />
    <EmbeddedResource Remove="logs\**" />
    <EmbeddedResource Remove="scripts\**" />
    <None Remove="erp_data\**" />
    <None Remove="logs\**" />
    <None Remove="scripts\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="appsettings-belhaven.json" />
    <Content Remove="keg_bridge-belhaven.json" />
    <Content Remove="keg_bridge-development.json" />
    <Content Remove="keg_bridge-haacht.json" />
    <Content Remove="keg_bridge-halvemaan.json" />
    <Content Remove="keg_bridge-lindemans.json" />
    <Content Remove="keg_bridge-moortgat-vl2.json" />
    <Content Remove="keg_bridge-pietra.json" />
    <Content Remove="keg_bridge.vansteenberge.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Akka" Version="1.5.46" />
    <PackageReference Include="Akka.Hosting" Version="1.5.46" />
    <PackageReference Include="Akka.Logger.Serilog" Version="1.5.25" />
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Dapper.FastCrud" Version="3.3.2" />
    <PackageReference Include="Dapper.Logging" Version="0.4.3" />
    <PackageReference Include="DeepCopy" Version="1.0.3" />
    <PackageReference Include="Destructurama.Attributed" Version="5.1.0" />
    <PackageReference Include="Destructurama.JsonNet" Version="4.0.2" />
    <PackageReference Include="FluentModbus" Version="5.3.2" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.2" />
    <PackageReference Include="InfluxDB.Client" Version="4.18.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="9.0.8" />
    <PackageReference Include="Microsoft.ClearScript" Version="7.5.0" />
    <PackageReference Include="Microsoft.ClearScript.linux-x64" Version="7.5.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="NCalcSync" Version="5.6.0" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
    <PackageReference Include="OPCFoundation.NetStandard.Opc.Ua.Client" Version="1.5.376.244" />
    <PackageReference Include="OPCFoundation.NetStandard.Opc.Ua.Client.ComplexTypes" Version="1.5.376.244" />
    <PackageReference Include="Parlot" Version="1.4.2" />
    <PackageReference Include="ReadLine" Version="2.0.1" />
    <PackageReference Include="S7netplus" Version="0.20.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.Grafana.Loki" Version="8.3.1" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageReference Include="Simple.Migrations" Version="0.9.21" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="9.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="9.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.3" />
    <PackageReference Include="System.Text.Json" Version="9.0.8" />
    <PackageReference Include="YamlDotNet" Version="16.3.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="keg_bridge.Development.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\" />
  </ItemGroup>


</Project>
