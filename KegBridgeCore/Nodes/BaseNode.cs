using Akka.Actor;
using Akka.Event;
using Akka.IO;
using Akka.Logger.Serilog;
using KegBridgeCore.Actors;
using KegBridgeCore.Actors.Interfaces;
using KegBridgeCore.Actors.Messages;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Dynamic;
using System.Reflection;

namespace KegBridgeCore.Nodes;

public class NodeException : Exception
{
    public NodeException()
    {
    }

    public NodeException(string message)
        : base(message)
    {
    }

    public NodeException(string message, Exception inner)
        : base(message, inner)
    {
    }
}

public class NodeRestartException : Exception
{
    public NodeRestartException()
    { }
}

public class MsgNodeError
{
    public Exception Exception { get; private set; }
    public MsgNodeError( Exception ex)
    {
        Exception = ex;
    }
}

public partial class BaseNode : ReceiveActor, IWithUnboundedStash
{
    public string NodeName { get; private set; }
    public Guid NodeId { get; private set; }
    protected ILoggingAdapter Logger { get; private set; }

    public IStash Stash { get; set; }
    protected bool enableStash { get; set; } = false;

    protected class NodeState : INodeState
    {
        private string _behavior;
        private DateTime? _behaviorSince = null;

        private Exception _errorException;
        private DateTime? _errorExceptionAt = null;

        private ulong _eventsIn = 0;
        private DateTime? _latestEventInAt = null;
        private ulong _eventsOut = 0;
        private DateTime? _latestEventOutAt = null;

        public ulong EventsIn {  get { return _eventsIn; } }
        public ulong EventsOut { get { return _eventsOut; } }

        public void IncEventsIn()
        {
            _eventsIn++;
            _latestEventInAt = DateTime.UtcNow;
        }
        public void IncEventsOut()
        {
            _eventsOut++;
            _latestEventOutAt = DateTime.UtcNow;
        }

        public string Behavior
        {
            get
            {
                return _behavior;
            }
            set
            {
                _behavior = value;
                _behaviorSince = DateTime.UtcNow;
            }
        }
        public DateTime? BehaviorSince
        {
            get
            {
                return _behaviorSince;
            }
        }
        public Exception ErrorException
        {
            get
            {
                return _errorException;
            }
            set
            {
                _errorException = value;
                _errorExceptionAt = DateTime.UtcNow;
            }
        }
        public DateTime? ErrorExceptionAt
        {
            get
            {
                return _errorExceptionAt;
            }
        }
        public dynamic NodeContext { get; set; } = new ExpandoObject();
    }

    protected BaseNode.NodeState State = new();

    public BaseNode(Guid nodeId, string nodeName)
    {
        NodeName = nodeName;
        NodeId = nodeId;

        State.Behavior = "Constructor";

        SetLogger();

        Logger.Info("Created node {nodeType} #{NodeId} {NodeName}", GetType().Name, NodeId, NodeName);

        BecomeLogged(WaitForConfig);
    }

    protected void SetLogger()
    {
        ILoggingAdapter contextLogger = Context.GetLogger<SerilogLoggingAdapter>();
        Logger = contextLogger
            .ForContext("NodeId", NodeId)
            .ForContext("NodeName", NodeName)
            .ForContext("Behavior", State.Behavior);
    }

    protected void BecomeLogged(Action behavior)
    {
        MethodInfo methodInfo = behavior.GetMethodInfo();
        State.Behavior = methodInfo.Name;

        Logger.Info("Become {become}", State.Behavior);
            
        SetLogger();

        Become(behavior);
    }

    private void WaitForConfig()
    {
        Logger.Debug(">>WaitForConfig");

        enableStash = true;

        Receive<MsgNodeConfig>(config =>
        {
            try
            {
                if( Configure(config.Config) )
                {
                    BecomeLogged(Ready);
                }
                else
                {
                    throw new NodeException("Configure failed.");
                }
            }
            catch( Exception ex )
            {
                Logger.Error(ex, "WaitForConfig failed.");
                State.ErrorException = ex;
                BecomeLogged(Error);
            }
        }, config => config.Id == NodeId);
        // ask NodeManager for configuration
        Context.Parent.Tell(new MsgGetNodeConfig());
    }

    protected void Error()
    {
    }

    virtual protected bool Configure(object config)
    {
        if( config != null )
        {
            var results = new List<ValidationResult>();

            bool isValid = Validator.TryValidateObject(
                config,
                new ValidationContext(config, null, null),
                results,
                true);

            Logger.Warning("Config validation valid? {valid} {@results}", isValid, results);

            return isValid;
        }

        return false;
    }

    virtual protected void Ready()
    {
        Logger.Debug(">>Ready");
        Receive<MsgNodeStart>(msg =>
        {
            BecomeLogged(Running);
        });
        UnstashAll();
    }

    protected void UnstashAll()
    {
        Stash.UnstashAll();
        enableStash = false;
    }

    virtual protected void Running()
    {
        Logger.Debug("Running");
    }

    virtual protected void ChildTerminated(Terminated msgTerminated)
    {
        Logger.Debug("Child terminated {terminatedActorRef}", msgTerminated.ActorRef);
    }

    protected override void Unhandled(object message)
    {
        var msgNodeRestart = message as MsgNodeRestart;
        if (msgNodeRestart != null && msgNodeRestart.Id == NodeId )
        {
            throw new NodeRestartException();
        }
        var stringMsg = message as string;
        if( stringMsg != null )
        {
            Logger.Info("Receive<string> {message}", stringMsg);
            if (stringMsg == "boem")
            {
                throw new Exception("BOEM!");
            }
            return;
        }
        var terminatedMessage = message as Terminated;
        if( terminatedMessage != null ) 
        {
            ChildTerminated(terminatedMessage);
            return;
        }
        var msgNodeError = message as MsgNodeError;
        if (msgNodeError != null)
        {
            State.ErrorException = msgNodeError.Exception;
            BecomeLogged(Error);
            return;
        }
        var msgStopNode = message as MsgNodeStop;
        if (msgStopNode != null && msgStopNode.Id == NodeId)
        {
            BecomeLogged(Ready);
            return;
        }
        var msgStateNode = message as MsgGetNodeState;
        if (msgStateNode != null && msgStateNode.Id == NodeId)
        {
            Sender.Tell((INodeState)State);
            return;
        }

        if ( enableStash )
        {
            Logger.Debug("Stashed message {type}", message.GetType().ToString());
            // stash all other messages 
            Stash.Stash();
        } 
        else
        {
            Logger.Info("Unhandled message {type}", message.GetType().ToString());
        }
    }

    // TODO remove this
    public void Subscribe(string topic)
    {
        Logger.Info("Subscribe to {subscribeTopic}", topic);
        //Context.Parent.Tell(new EventRouter.MsgSubscribe(topic));
    }

    // TODO remove this
    public void UnsubscribeAll()
    {
        //Context.Parent.Tell(new EventRouter.MsgUnsubscribe());
    }

    public void PostNodeEvent(Data.NodeEvent nodeEvent)
    {
        LogNodeEventOut(nodeEvent);
        Context.Parent.Tell(nodeEvent);
    }

    public void LogNodeEventOut(Data.NodeEvent nodeEvent)
    {
        State.IncEventsOut();
        Logger.ForContext("NodeEvent", nodeEvent, true).ForContext("NodeEventDir","out")
            .Debug("NodeEvent out {@event}", nodeEvent);
    }
    public void LogNodeEventIn(Data.NodeEvent nodeEvent)
    {
        State.IncEventsIn();
        Logger.ForContext("NodeEvent", nodeEvent, true).ForContext("NodeEventDir", "in")
            .Debug("NodeEvent in {@event}", nodeEvent);
    }

    protected override void PostRestart(Exception reason)
    {
        base.PostRestart(reason);
        Self.Tell(new MsgNodeStart(NodeId));
    }

    protected override void PostStop()
    {
        UnsubscribeAll();
        base.PostStop();
    }

}