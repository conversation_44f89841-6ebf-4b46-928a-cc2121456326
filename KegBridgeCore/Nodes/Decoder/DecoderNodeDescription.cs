using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.Decoder;

public class DecoderNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "decoder";
    public string Description { get; private set; } = "Decoder node";
    public Type NodeConfigType { get; private set; } = typeof(DecoderNodeConfig);
    public Type NodeType { get; private set; } = typeof(DecoderNode);
    public Type NodeConfigComponent { get; private set; } = null;
}