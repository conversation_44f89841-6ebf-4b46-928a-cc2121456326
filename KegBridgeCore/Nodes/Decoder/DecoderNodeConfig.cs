using KegBridgeCore.Actors.Interfaces;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace KegBridgeCore.Nodes.Decoder;

public class DecoderNodeConfig : INodeTypeConfig
{
    public Dictionary<string,object> Schemas { get; set; }
    public string[] SchemaDbs { get; set; }

    public class Decode
    {
        public string InTopic { get; set; }
        public string InAttribute { get; set; } = "binary_data";
        public string Schema { get; set; }
        public string OutTopic { get; set; }
    }
        
    public List<Decode> Decodes { get; set; } = new List<Decode>();

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }

}