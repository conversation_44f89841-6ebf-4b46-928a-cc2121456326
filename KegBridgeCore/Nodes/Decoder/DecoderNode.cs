using Akka.Event;
using System;
using System.Collections.Generic;

namespace KegBridgeCore.Nodes.Decoder;

public class DecoderNode : BaseNode
{
    private DecoderNodeConfig Config { get; set; }

    #region Messages
    #endregion

    public class DecodeState
    {
        private ulong  _decodedCount = 0;
        private object _latest;
        private DateTime? _latestSince = null;

        public ulong Count { get { return _decodedCount; } }

        public object Latest
        {
            get
            {
                return _latest;
            }
            set
            {
                _latest = value;
                _decodedCount++;
                _latestSince = DateTime.UtcNow;
            }
        }
        public DateTime? LatestSince
        {
            get
            {
                return _latestSince;
            }
        }
    }

    public DecoderNode(Guid id, string name) : base(id,name)
    {
        State.NodeContext.Decoded = new Dictionary<string,DecodeState>();
    }

    protected override bool Configure(object config)
    {
        Config = config as DecoderNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            KegBridgeCore.Services.BinaryCoder.Schemas.Initialize(Config.Schemas);
            // KegBridgeCore.Services.BinaryCoder.Schemas.ReadPlcDbFiles(Config.SchemaDbs);

            Logger.Info("Node {nodeName} configured read {schema_count} schemas {schemas} decoding {decoder_count} topics",
                NodeName,
                KegBridgeCore.Services.BinaryCoder.Schemas.DecodeCatalog.Keys.Count,
                KegBridgeCore.Services.BinaryCoder.Schemas.DecodeCatalog.Keys,
                Config.Decodes.Count);

            State.NodeContext.Schemas = KegBridgeCore.Services.BinaryCoder.Schemas.DecodeCatalog;

            UnsubscribeAll();
            foreach (var decode in Config.Decodes)
            {
                if (KegBridgeCore.Services.BinaryCoder.Schemas.DecodeCatalog.ContainsKey(decode.Schema))
                {
                    Subscribe(decode.InTopic);
                } 
                else
                {
                    Logger.Error("Unknown schema {schema} to decode {in_topic}", decode.Schema, decode.InTopic);
                }
            }

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        foreach (var decode in Config.Decodes)
        {
            if (KegBridgeCore.Services.BinaryCoder.Schemas.DecodeCatalog.ContainsKey(decode.Schema))
            {
                State.NodeContext.Decoded.Add(decode.OutTopic, new DecodeState());

                Receive<Data.NodeEvent>(nodeEvent =>
                {
                    LogNodeEventIn(nodeEvent);

                    if( nodeEvent.Data.ContainsKey(decode.InAttribute) )
                    {
                        dynamic in_data = nodeEvent.Data[decode.InAttribute];

                        byte[] bin_data = null;
                        if (in_data is string @string)
                        {
                            bin_data = KegBridgeCore.Services.BinaryCoder.BinaryStreamReader.HexStringToByteArray(@string);
                        }
                        else if (in_data is byte[] @binary)
                        {
                            bin_data = (byte[])in_data;
                        }

                        var decoded = KegBridgeCore.Services.BinaryCoder.Schemas.Decode(decode.Schema, bin_data);

                        var newNodeEvent = new Data.NodeEvent(
                            NodeName,
                            decoded,
                            decode.OutTopic,
                            nodeEvent.Timestamp
                        );

                        State.NodeContext.Decoded[decode.OutTopic].Latest = newNodeEvent;

                        PostNodeEvent(newNodeEvent);
                    }
                    else
                    {
                        Logger.Info("in_attribute {in_attribue} not present in {in_topic}", decode.InAttribute, decode.InTopic );
                    }

                }, ev => ev.Topic == decode.InTopic);
            }
        }
    }
}