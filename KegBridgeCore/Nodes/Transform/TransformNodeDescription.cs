using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.Transform;

public class TransformNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "transform";
    public string Description { get; private set; } = "Transform node";
    public Type NodeConfigType { get; private set; } = typeof(TransformNodeConfig);
    public Type NodeType { get; private set; } = typeof(TransformNode);
    public Type NodeConfigComponent { get; private set; } = null;
    //public Type NodeConfigComponent { get; private set; } = typeof(DummyNodeConfigComponent);
}