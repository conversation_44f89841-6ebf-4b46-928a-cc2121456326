using System.Collections.Generic;
using KegBridgeCore.Actors.Interfaces;
using System.ComponentModel.DataAnnotations;

namespace KegBridgeCore.Nodes.Transform;

public class TransformNodeConfig : INodeTypeConfig
{
    public class KeyValueIf
    {
        public string Key { get; set; } = null;
        public string KeyExpression { get; set; } = null;
        public string Value { get; set; } = null;
        public string ValueExpression { get; set; } = null;
        public string If { get; set; } = null;
    }

    public List<KeyValueIf> Modify { get; set; } = new();
    public List<KeyValueIf> Delete { get; set; } = new();
}