using Akka.Actor;
using Akka.Event;
using DeepCopy;
using KegBridgeCore.Data;
using System;
using System.Collections.Generic;
using System.Dynamic;
using KegBridgeCore.Services;
using NCalc;
using NCalc.Handlers;
using Opc.Ua;

namespace KegBridgeCore.Nodes.Transform;

public class TransformNode(Guid nodeId, string nodeName) : BaseNode(nodeId, nodeName)
{
    private TransformNodeConfig Config { get; set; }

    private record KeyValueIfExpressions(
        TransformNodeConfig.KeyValueIf Config,
        Expression Key,
        Expression If = null,
        Expression Value = null);

    private List<KeyValueIfExpressions> ModifyRules { get; set; } = new List<KeyValueIfExpressions>();
    private List<KeyValueIfExpressions> DeleteRules { get; set; } = new List<KeyValueIfExpressions>();

    protected override bool Configure(object config)
    {
        Logger.Info("Configuring with config: {@config}", config);
        Config = config as TransformNodeConfig;
        if (base.Configure(Config))
        {
            // if config is valid then prepare the expressions
            foreach (var modify in Config.Modify)
            {
                ModifyRules.Add(new KeyValueIfExpressions(
                    modify,
                    EventNodeRuleExpression.Create(modify.KeyExpression),
                    EventNodeRuleExpression.Create(modify.If),
                    EventNodeRuleExpression.Create(modify.ValueExpression)
                ));
            }

            foreach (var del in Config.Delete)
            {
                DeleteRules.Add(new KeyValueIfExpressions(
                    del,
                    EventNodeRuleExpression.Create(del.KeyExpression),
                    EventNodeRuleExpression.Create(del.If)
                ));
            }

            return true;
        }

        return false;
    }

    protected override void Running()
    {
        Logger.Info(">>Running");
        Receive<NodeEvent>(ProcessNodeEvent);
    }

    protected void ProcessNodeEvent(NodeEvent nodeEvent)
    {
        LogNodeEventIn(nodeEvent);

        var currentEvent = nodeEvent;
        var pathsToAdd = new List<KeyValuePair<string, object?>>();
        var pathsToDelete = new List<string>();
        string newTopic = null;
        DateTimeOffset? newTimestamp = null;
        
        // Process Add Rules
        foreach (var kvi in ModifyRules)
        {
            Logger.Debug("Processing Modify Rule {@config}", kvi.Config);

            bool evaluateRule = true;
            // evaluate if expression
            if (kvi.If != null)
            {
                var ifResult = EventNodeRuleExpression.Evaluate(kvi.If, currentEvent);
                Logger.Debug("  IfResult {if_result}", ifResult);
                evaluateRule = Convert.ToBoolean(ifResult);
            }
            else
            {
                Logger.Debug("  If null -> evaluating");
            }

            if (evaluateRule)
            {
                Logger.Debug("  If true -> evaluating");
                // key creation
                string newKey = kvi.Config.Key;

                if (kvi.Key != null)
                {
                    var keyResult = EventNodeRuleExpression.Evaluate(kvi.Key, currentEvent);
                    Logger.Debug("  KeyResult {result}", keyResult);
                    newKey = Convert.ToString(keyResult);
                }

                if (newKey == null)
                {
                    Logger.Warning("  Key is null, skipping");
                    continue;
                }

                dynamic newValue = kvi.Config.Value;

                if (kvi.Value != null)
                {
                    var valueResult = EventNodeRuleExpression.Evaluate(kvi.Value, currentEvent);
                    Logger.Debug("  ValueResult {result}", valueResult);
                    newValue = valueResult;
                }

                if (newKey.StartsWith("data."))
                {
                    newKey = newKey.Substring(5);
                }

                if (newKey == "topic")
                {
                    newTopic = Convert.ToString(newValue);
                    Logger.Debug("  Topic = {newValue}", newTopic);
                }
                else if (newKey == "timestamp")
                {
                    newTimestamp = new DateTimeOffset(newValue,TimeSpan.Zero);
                    Logger.Debug("  Timestamp = {newValue}", newTimestamp);
                }
                else
                {
                    pathsToAdd.Add(new KeyValuePair<string, object?>(newKey, newValue));
                    Logger.Debug("  Add {newKey} = {newValue}", newKey, (object)newValue);
                }
            }
            else
            {
                Logger.Debug("  If false -> skipping");
            }
        }

        foreach (var kvi in DeleteRules)
        {
            Logger.Debug("Processing Delete Rule {@config}", kvi.Config);

            // evaluate if expression
            bool evaluateRule = true;
            // evaluate if expression
            if (kvi.If != null)
            {
                var ifResult = EventNodeRuleExpression.Evaluate(kvi.If, currentEvent);
                Logger.Debug("  IfResult {if_result}", ifResult);
                evaluateRule = Convert.ToBoolean(ifResult);
            }
            else
            {
                Logger.Debug("  If null -> evaluating");
            }

            if (evaluateRule)
            {
                // key creation
                string deleteKey = kvi.Config.Key;

                if (kvi.Key != null)
                {
                    var keyResult = EventNodeRuleExpression.Evaluate(kvi.Key, currentEvent);
                    Logger.Debug("  KeyResult {key_result}", keyResult);
                    deleteKey = Convert.ToString(keyResult);
                }

                if (deleteKey == null)
                {
                    Logger.Warning("  Key is null, skipping");
                    continue;
                }

                if (deleteKey.StartsWith("data."))
                {
                    deleteKey = deleteKey.Substring(5);
                }

                pathsToDelete.Add(deleteKey);
                Logger.Debug("  Delete {newKey}", deleteKey);
            }
            else
            {
                Logger.Debug("  If false, skipping");
            }
        }

        // Apply traditional key-value additions
        if (pathsToAdd.Count > 0)
        {
            currentEvent = currentEvent.SetPaths(pathsToAdd);
        }

        // Apply deletions
        if (pathsToDelete.Count > 0)
        {
            currentEvent = currentEvent.DeletePaths(pathsToDelete, allowNonLeafDeletion: true);
        }
        
        if( newTopic != null )
        {
            currentEvent = currentEvent.WithTopic(newTopic);
        }
        if( newTimestamp != null )
        {
            currentEvent = currentEvent.WithTimestamp((DateTimeOffset)newTimestamp);
        }

        PostNodeEvent(currentEvent);
    }
}