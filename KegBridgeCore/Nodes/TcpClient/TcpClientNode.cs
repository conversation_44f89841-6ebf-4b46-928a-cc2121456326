using Akka.Actor;
using Akka.Event;
using Akka.IO;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Differencing;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Dynamic;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;

namespace KegBridgeCore.Nodes.TcpClient;

public class TcpClientNode : BaseNode
{
    private TcpClientNodeConfig Config { get; set; }
    private IActorRef connection;
    private StringBuilder receivedData;
    private Regex pattern;
    public TcpClientNode(Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
        receivedData = new StringBuilder();
    }

    protected override bool Configure(object config)
    {
        Config = config as TcpClientNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            if( Config.Pattern != null && Config.Pattern.Length > 0 )
            {
                pattern = new Regex(Config.Pattern);
            }

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        Receive<Tcp.Connected>(m => {
            Logger.Info("Tcp.Connected");
            Sender.Tell(new Tcp.Register(Self));
            connection = Sender;

            receivedData.Clear();

            BecomeLogged(Connected);
        });
        Receive<Tcp.CommandFailed>(m => {
            Logger.Info("Tcp.CommandFailed");
            BecomeLogged(Running);
        });
            
        var endpoint = IPEndPoint.Parse(Config.RemoteEndpoint);
        Context.System.Tcp().Tell(new Tcp.Connect(endpoint));
    }

    protected void Connected()
    {
        Receive<Tcp.Received>(m => {

            var received = String.Empty;

            if( Config.Encoding == TcpClientNodeConfig.DataEncoding.Utf8)
            {
                received = Encoding.UTF8.GetString(m.Data.ToArray());
            } else if( Config.Encoding == TcpClientNodeConfig.DataEncoding.Ascii ) {
                received = Encoding.ASCII.GetString(m.Data.ToArray());
            }

            Logger.Info("Received {data}", received);

            if( pattern != null )
            {
                receivedData.Append(received);
                while (true)
                {
                    var match = pattern.Match(receivedData.ToString());
                    if (match.Success)
                    {
                        Logger.Info("Event Data {data}", match);

                        dynamic data = new ExpandoObject();
                        var data_dict = data as IDictionary<string, Object>;

                        if ( match.Groups.Count == 1)
                        {
                            data.data = match.Groups[0].Value;
                        } 
                        else
                        {
                            for(var i=1; i<match.Groups.Count; i++)
                            {
                                var name = match.Groups[i].Name;
                                if( name.Length == 1 )
                                {
                                    name = "data" + name;
                                }
                                data_dict.Add(name, match.Groups[i].Value);
                            }
                        }
                        PostNodeEvent(new Data.NodeEvent(NodeName, data, Config.Topic));

                        receivedData = receivedData.Remove(0, match.Index + match.Length);
                    } 
                    else
                    {
                        break;
                    }
                }
                Logger.Info("Buffered {data}", receivedData);
            }
            else
            {
                Logger.Info("Event Data {data}", received);
                dynamic data = new ExpandoObject();
                data.data = received;
                PostNodeEvent(new Data.NodeEvent(NodeName, data, Config.Topic));
            }
        });
        Receive<Tcp.PeerClosed>(m => {
            Logger.Info("Tcp.PeerClosed");
            BecomeLogged(Running);
        });
    }
}