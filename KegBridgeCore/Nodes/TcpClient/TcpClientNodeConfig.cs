using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;
using System;

namespace KegBridgeCore.Nodes.TcpClient;

public class TcpClientNodeConfig : INodeTypeConfig
{
    public enum DataEncoding { Utf8, Ascii }

    [Required]
    public string RemoteEndpoint { get; set; }
    [Required]
    public string Topic { get; set; } = "tcp_client.data";
    public DataEncoding Encoding { get; set; } = DataEncoding.Utf8;
    public string Pattern { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }
}