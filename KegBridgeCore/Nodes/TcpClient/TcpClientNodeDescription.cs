using KegBridgeCore.Nodes.Hmi;
using KegBridgeCore.Services;
using System;
namespace KegBridgeCore.Nodes.TcpClient;

public class TcpClientNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "tcp_client";
    public string Description { get; private set; } = "TCP client node";
    public Type NodeConfigType { get; private set; } = typeof(TcpClientNodeConfig);
    public Type NodeType { get; private set; } = typeof(TcpClientNode);
    public Type NodeConfigComponent { get; private set; } = null;

}