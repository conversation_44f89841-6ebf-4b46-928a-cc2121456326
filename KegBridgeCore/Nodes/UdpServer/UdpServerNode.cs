using Akka.Actor;
using Akka.Event;
using Akka.IO;
using DeepCopy;
using KegBridgeCore.Data;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Net;
using static Akka.IO.Inet.SO;

namespace KegBridgeCore.Nodes.UdpServer;

public class UdpServerNode : BaseNode
{
    private UdpServerNodeConfig Config { get; set; }

    public UdpServerNode(Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
    }

    protected override bool Configure(object config)
    {
        Logger.Info("Configuring with config: {@config}", config);
        Config = config as UdpServerNodeConfig;
        return base.Configure(Config); 
    }

    protected override void Running()
    {
        Logger.Info(">>Running");
        Receive<Udp.Bound>(udp_bound => {
            Logger.Debug("Udp.Bound to {address}", udp_bound.LocalAddress);
        });
        Receive<Udp.Received>(udp_msg => {
            Logger.Debug("Udp.Received {data} bytes from {remote}", udp_msg.Data.Count, udp_msg.Sender);

            dynamic data = new ExpandoObject();
            data.content = udp_msg.Data.ToArray();
            var nodeEvent = new Data.NodeEvent(NodeName, data, "udp");
            PostNodeEvent(nodeEvent);
        });
        //var remoteEndpoint = IPEndPoint.Parse(Config.RemoteEndpoint);
        var localEndpoint = IPEndPoint.Parse(Config.LocalEndpoint);
        Context.System.Udp().Tell(new Udp.Bind(Self,localEndpoint,new[]
        {
            new ReceiveBufferSize(8192)
        }));
    }

    protected override void PostStop()
    {
        base.PostStop();
    }

}