using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.UdpServer;

public class UdpServerNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "udp_server";
    public string Description { get; private set; } = "UDP server";
    public Type NodeConfigType { get; private set; } = typeof(UdpServerNodeConfig);
    public Type NodeType { get; private set; } = typeof(UdpServerNode);
    public Type NodeConfigComponent { get; private set; } = null;
    //public Type NodeConfigComponent { get; private set; } = typeof(DummyNodeConfigComponent);
}