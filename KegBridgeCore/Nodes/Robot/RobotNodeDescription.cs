using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.Robot;

public class RobotNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "robot";
    public string Description { get; private set; } = "Robot node";
    public Type NodeConfigType { get; private set; } = typeof(RobotNodeConfig);
    public Type NodeType { get; private set; } = typeof(RobotNode);
    public Type NodeConfigComponent { get; private set; } = null;

}