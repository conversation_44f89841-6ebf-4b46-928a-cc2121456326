using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;

namespace KegBridgeCore.Nodes.Robot;

public class RobotNodeConfig : INodeTypeConfig
{
    [Required]
    public string Url { get; set; }
    [Required]
    public string ErrallPath { get; set; }
    public int PollInterval { get; set; } = 15000;

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }
}