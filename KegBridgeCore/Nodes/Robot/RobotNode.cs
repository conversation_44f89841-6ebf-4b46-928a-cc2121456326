using Akka.Actor;
using Akka.Event;
using Dapper.FastCrud;
using Dapper.Logging;
using HtmlAgilityPack;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace KegBridgeCore.Nodes.Robot;

public partial class RobotNode : BaseNode
{
    private RobotNodeConfig Config { get; set; }
    private HttpClientHandler httpClientHandler;
    private HttpClient httpClient;
    private ICancelable pollCancel;
    private IServiceScopeFactory serviceScopeFactory;

    #region Messages
    public class MsgGetAlarms
    {
    }
    public class MsgReadAlarms
    {
        public string Filename { get; private set; }
        public MsgReadAlarms(string filename)
        {
            Filename = filename;
        }
    }
    public class MsgStartPoll
    {
        public int IntervalMs { get; set; }

        public MsgStartPoll(int intervalMs)
        {
            IntervalMs = intervalMs;
        }
    }
    public class MsgStopPoll
    {

    }
    #endregion


    public RobotNode(IServiceScopeFactory _serviceScopeFactory, Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
        serviceScopeFactory = _serviceScopeFactory;
        State.NodeContext.DownloadCount = 0;
        State.NodeContext.DownloadSize = 0;
        State.NodeContext.LoginCount = 0;
    }

    protected override bool Configure(object config)
    {
        Config = config as RobotNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            httpClientHandler = new HttpClientHandler();
            httpClientHandler.ServerCertificateCustomValidationCallback = (requestMsg, x509cert, x509chain, sslPolicy) =>
            {
                return true;
            };
            httpClient = new HttpClient(httpClientHandler) { BaseAddress = new Uri(Config.Url) };

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        Receive<MsgGetAlarms>(GetAlarms);
        Receive<MsgReadAlarms>(msg =>
        {
            ReadAlarms(msg);
        });
        Receive<MsgStartPoll>(m =>
        {
            Logger.Info("StartPoll {ms}ms", m.IntervalMs);

            if (pollCancel != null)
            {
                pollCancel.Cancel();
            }

            pollCancel = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                1000,
                m.IntervalMs,
                Self,
                new MsgGetAlarms(),
                Self);
        });
        Receive<MsgStopPoll>(m =>
        {
            pollCancel.Cancel();
            pollCancel = null;
        });
        // Initial read of the alarms
        if (Config.PollInterval > 0 && pollCancel == null )
        {
            Self.Tell(new MsgStartPoll(Config.PollInterval));
        }
    }

    protected void PendingDownload()
    {
        Receive<Status.Failure>(msg =>
        {
            var exception = msg.Cause;
            Logger.Error(msg.Cause, "Download failed {reason}", msg.Cause.Message);
            BecomeLogged(Running);
        });
        Receive<MsgReadAlarms>(msg =>
        {
            ReadAlarms(msg);
            BecomeLogged(Running);
        });
    }

    protected void GetAlarms(MsgGetAlarms msg)
    {
        BecomeLogged(PendingDownload);
        DownloadAlarm(msg).ContinueWith( task =>
            {
                // if download succeeded then return the csv filename in a new message
                // to process
                return new MsgReadAlarms(task.Result);
                // exceptions will be catched by the Status.Failure message of PipeTo
            }, TaskContinuationOptions.ExecuteSynchronously)
            .PipeTo(Self);
    }

    //3764540" 28-JAN-22 10:10:40 " R E S E T                                         " "                               00000000"    "
    //3764541" 28-JAN-22 10:09:56 " SRVO-407 DCS SSO Fence Open 1,1                   " " SERVO                         00110110"    "
    //3764542" 28-JAN-22 10:09:56 " SRVO-037 IMSTP input(Group:1)                    " " SERVO                         00110110"    "
    //3764543" 28-JAN-22 10:09:56 " SRVO-199 Controlled Stop                          " " STOP.G                        00100110"    "
    //3764544" 28-JAN-22 10:09:56 " SYST-034 HOLD signal from SOP/UOP is lost         " " WARN                          00000000"    "
    //3764545" 28-JAN-22 10:09:56 " SYST-032 ENBL signal from UOP is lost             " " WARN                          00000000"    "
    //3764546" 28-JAN-22 10:09:56 " SYST-033 SFSPD signal from UOP is lost            " " WARN                          00000000"    "
    //3764547" 28-JAN-22 10:01:56 " SRVO-171 MotorSpd lim/DVC(G:1 A:1)                " " NONE                          10000000"    "
    //3764548" 28-JAN-22 10:01:46 " SRVO-171 MotorSpd lim/DVC(G:1 A:1)                " " NONE                          10000000"    "
    //3764549" 28-JAN-22 10:01:36 " SRVO-171 MotorSpd lim/DVC(G:1 A:1)                " " NONE                          10000000"    "
    protected void ReadAlarms(MsgReadAlarms msg)
    {
        Logger.Info("Processing alarms from file {file}", msg.Filename);

        try
        {

            using (IServiceScope serviceScope = serviceScopeFactory.CreateScope())
            using (var reader = new StreamReader(msg.Filename))
            {
                var errallPage = reader.ReadToEnd();

                var errallHtmlDoc = new HtmlDocument();
                errallHtmlDoc.LoadHtml(errallPage);

                var errallList = errallHtmlDoc.DocumentNode.SelectSingleNode("//xmp");

                var errallLog = errallList.InnerText;

                var dbConnFactory = serviceScope.ServiceProvider.GetService<IDbConnectionFactory>();

                using (var connection = dbConnFactory.CreateConnection())
                {
                    var lines = errallLog.Trim().Split("\n");

                    var firstLine = lines[0];

                    var firstParts = firstLine.Trim().Split(" ");
                    var robotTimeStr = firstParts[^2] + " " + firstParts[^1];
                    var robotTime = DateTime.SpecifyKind(DateTime.Parse(robotTimeStr), DateTimeKind.Local);

                    var robotName = firstParts[^3].ToUpper();

                    var latestEvents = connection.Find<RobotDbAlarm>(sql => sql
                        .Where($"({nameof(RobotDbAlarm.Source):C} = @Source)")
                        .OrderBy($"{nameof(RobotDbAlarm.EventAt):C} DESC")
                        .Top(1)
                        .WithParameters(new
                        {
                            Source = robotName
                        })
                    );

                    DateTime? latestEventAt = null;
                    if (latestEvents.Count() > 0)
                    {
                        latestEventAt = latestEvents.First<RobotDbAlarm>().EventAt.ToUniversalTime();
                    }
                    Logger.Info("Latest event at {latestEventAt}", latestEventAt);

                    var timeDifference = DateTime.Now - robotTime;
                    timeDifference = new TimeSpan(timeDifference.Days, timeDifference.Hours, timeDifference.Minutes, 0, 0);

                    Logger.Info("TimeCorrection {@timeDifference}", timeDifference);

                    int duplicateCount = 0, insertedCount = 0, skippedCount = 0;

                    // REVERSE loop over the log lines (most recent is on top!)
                    for (var i = lines.Length - 1; i > 0; i--)
                    {
                        var line = lines[i];

                        if (line.Length == 0)
                        {
                            continue;
                        }

                        var parts = line.Split("\"");

                        var robotAlarm = new RobotDbAlarm();

                        robotAlarm.Source = robotName;

                        robotAlarm.EventAt = DateTime.SpecifyKind(DateTime.Parse(parts[1]), DateTimeKind.Local);
                        robotAlarm.EventAt = robotAlarm.EventAt + timeDifference;
                        robotAlarm.EventAt = robotAlarm.EventAt.ToUniversalTime();

                        Logger.Debug("Parts {seq} {timestr} {msg} | {event_at} ", parts[0], parts[1], parts[2], robotAlarm.EventAt);

                        parts[2] = parts[2].Trim();
                        if (parts[2].StartsWith("R E S E T"))
                        {
                            robotAlarm.MsgText = parts[2];
                        }
                        else
                        {
                            var code_msg = parts[2].Split(" ", 2);

                            robotAlarm.MsgCode = code_msg[0];
                            robotAlarm.MsgText = code_msg[1];
                        }

                        robotAlarm.MsgData = parts[3].Trim();

                        var severity_bits = parts[4].Trim().Split(" ", 2);
                        if (severity_bits.Length == 2)
                        {
                            robotAlarm.MsgSeverity = severity_bits[0].Trim();
                            robotAlarm.MsgBits = severity_bits[1].Trim();
                        }
                        else
                        {
                            robotAlarm.MsgBits = severity_bits[0].Trim();
                        }

                        robotAlarm.MsgAct = parts[5].Trim();

                        robotAlarm.Digest = RobotDbAlarm.ComputeSHA256Hash(
                            robotName +
                            parts[1] +
                            robotAlarm.MsgText
                        );

                        try
                        {
                            if ((latestEventAt == null) || ((latestEventAt != null) && (robotAlarm.EventAt >= latestEventAt)))
                            {
                                connection.Insert(robotAlarm);

                                latestEventAt = robotAlarm.EventAt.ToUniversalTime();
                                insertedCount++;
                            }
                            else
                            {
                                skippedCount++;
                            }
                        }
                        catch (Npgsql.PostgresException ex) when (ex.ConstraintName == "robot_alarms_digest")
                        {
                            //Logger.Debug("Duplicate {@alarm}", robotAlarm);
                            duplicateCount++;
                        }
                    }
                    Logger.Info("Inserted {inserted} skipped {skipped} duplicate {duplicate} robot alarms from file {file}", insertedCount, skippedCount, duplicateCount, msg.Filename);
                }
            }
        }
        catch(Exception ex)
        {
            Logger.Error(ex, "Error processing");
        }
        finally
        {
            try
            {
                File.Delete(msg.Filename);
                Logger.Info("Deleted file {filename}", msg.Filename);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error deleting {filename}", msg.Filename);
            }
        }
    }

    protected async Task<string> DownloadAlarm(MsgGetAlarms msg)
    {            
        var response = await httpClient.GetAsync(Config.ErrallPath);

        response.EnsureSuccessStatusCode();

        if (response.RequestMessage.RequestUri.PathAndQuery == Config.ErrallPath)
        {
            using (FileStream fs = File.Create(Path.GetTempFileName()))
            {
                await response.Content.CopyToAsync(fs);
                Logger.Info("Downloaded to {fname}", fs.Name);
                return fs.Name;
            }
        }
        else
        {
            throw new Exception($"Download of {Config.ErrallPath} failed.");
        }
    }
}