using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Text;

namespace KegBridgeCore.Nodes.Robot;

[Table("robot_alarms")]
public class RobotDbAlarm
{
    private DateTime? _created_at = null;

    [Column("id")]
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    [Column("created_at")]
    public DateTime CreatedAt
    {
        get
        {
            if( _created_at == null )
            {
                _created_at = DateTime.UtcNow;
            }
            return (DateTime)_created_at;
        }
        set
        {
            _created_at = value;
        }
    }
    [Column("source")]
    public string Source { get; set; }
    [Column("digest")]
    public string Digest { get; set; }
    [Column("event_at")]
    public DateTime EventAt{ get; set; }
    [Column("msg_code")]
    public string MsgCode { get; set; }
    [Column("msg_text")]
    public string MsgText { get; set; }
    [Column("msg_severity")]
    public string MsgSeverity { get; set; }
    [Column("msg_data")]
    public string MsgData { get; set; }
    [Column("msg_bits")]
    public string MsgBits { get; set; }
    [Column("msg_act")]
    public string MsgAct { get; set; }

    public static string ComputeSHA256Hash(string text)
    {
        using (var sha256 = SHA256.Create())
        {
            return BitConverter.ToString(sha256.ComputeHash(Encoding.UTF8.GetBytes(text))).Replace("-", "");
        }
    }
        
}