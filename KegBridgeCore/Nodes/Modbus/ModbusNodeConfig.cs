using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;

namespace KegBridgeCore.Nodes.Modbus;

public class ModbusNodeConfig : INodeTypeConfig
{
    [Required]
    public string Ip { get; set; }
    public int PollInterval { get; set; } = 0;
    public ushort StartRegister { get; set; }
    public ushort Length { get; set; }
    public string Topic { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }
}