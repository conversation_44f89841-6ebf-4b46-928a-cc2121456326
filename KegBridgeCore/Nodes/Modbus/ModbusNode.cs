using Akka.Actor;
using Akka.Event;
using FluentModbus;
using System;
using System.Dynamic;

namespace KegBridgeCore.Nodes.Modbus;

public class ModbusNode : BaseNode
{
    private ModbusNodeConfig Config { get; set; }
    private ICancelable pollCancel;

    #region Messages
    public class MsgReadData
    {
    }
    public class MsgStartPoll
    {
        public int IntervalMs { get; set; }

        public MsgStartPoll(int intervalMs)
        {
            IntervalMs = intervalMs;
        }
    }
    public class MsgStopPoll
    {

    }
    #endregion

    public ModbusNode(Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
    }

    protected override bool Configure(object config)
    {
        Config = config as ModbusNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        Receive<MsgReadData>(ReadData);
        Receive<MsgStartPoll>(m =>
        {
            Logger.Info("StartPoll {ms}ms", m.IntervalMs);

            if (pollCancel != null)
            {
                pollCancel.Cancel();
            }

            pollCancel = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                1000,
                m.IntervalMs,
                Self,
                new MsgReadData(),
                Self);
        });
        Receive<MsgStopPoll>(m =>
        {
            pollCancel.Cancel();
            pollCancel = null;
        });
        // Initial read of the alarms
        if (Config.PollInterval > 0 && pollCancel == null )
        {
            Self.Tell(new MsgStartPoll(Config.PollInterval));
        }
    }

    protected void ReadData(MsgReadData msg)
    {
        var client = new ModbusTcpClient();
        client.Connect(Config.Ip);
        var buffer = client.ReadInputRegisters(1, Config.StartRegister, Config.Length);

        dynamic data = new ExpandoObject();
        data.binary_data = buffer.ToArray();
        var nodeEvent = new Data.NodeEvent(NodeName, data, Config.Topic);

        PostNodeEvent(nodeEvent);

        client.Disconnect();
    }
}