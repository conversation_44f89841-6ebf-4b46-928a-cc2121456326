using KegBridgeCore.Services;
using System;
namespace KegBridgeCore.Nodes.Modbus;

public class ModbusNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "modbus";
    public string Description { get; private set; } = "Modbus node";
    public Type NodeConfigType { get; private set; } = typeof(ModbusNodeConfig);
    public Type NodeType { get; private set; } = typeof(ModbusNode);
    public Type NodeConfigComponent { get; private set; } = null;

}