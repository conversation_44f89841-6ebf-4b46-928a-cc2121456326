using Akka.Actor;
using Akka.Event;
using Akka.Logger.Serilog;
using KegBridgeCore.Nodes.Plc.Communicators;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;

namespace KegBridgeCore.Nodes.Plc;

public class PlcNode : BaseNode
{
    private PlcNodeConfig Config { get; set; }
    private IActorRef communicatorActor ;
    private ICancelable pollCancel;

    #region Messages
    public class MsgConnect
    {
    }
    public class MsgDisconnect
    {
    }
    public class MsgDoPollDbs
    {
    }
    public class MsgStartPoll
    {
        public int IntervalMs { get; set; }

        public MsgStartPoll(int intervalMs)
        {
            IntervalMs = intervalMs;
        }
    }
    public class MsgStopPoll
    {

    }
    #endregion

    class DbState
    {
        [JsonIgnore]
        public PlcNodeConfig.Db config { get; private set; }

        [JsonIgnore]
        public byte[] data { get; set; } = null;

        public bool disabled { get; set; } = false;

        public class ByteStats
        {
            public ulong bytesWritten { get; set; } = 0;
            public ulong bytesRead { get; set; } = 0;
            public ulong bytesUpdated { get; set; } = 0;

            public ulong totalReadErrors { get; set; } = 0;
            public string latestReadError { get; set; } = "";
            public DateTime? latestReadErrorAt { get; set; } = null;

            public ulong totalWriteErrors { get; set; } = 0;
            public string latestWriteError { get; set; } = "";
            public DateTime? latestWriteErrorAt { get; set; } = null;

            public int latestDuration { get; set; } = 0;
        }

        public ByteStats byteStats = new ByteStats();

        public DbState(PlcNodeConfig.Db _config)
        {
            config = _config;
        }

        public bool UpdateData(byte[] newdata)
        {
            if (newdata == null)
                return false;

            if ((data == null) || Services.BinaryCoder.BinaryStreamReader.ByteArrayCompare(data, newdata)!=0 )
            {
                data = newdata;
                return true;
            }

            return false;
        }
    }

    private List<DbState> DbStates { get; set; } = new List<DbState>();
    private Dictionary<string,DbState> TopicToDbState = new Dictionary<string,DbState>();

    public PlcNode(Guid nodeId, string nodeName) : base(nodeId,nodeName)
    {
    }

    protected override bool Configure(object config)
    {
        Config = config as PlcNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            DbStates.Clear();
            TopicToDbState.Clear();

            foreach( var db in Config.Dbs )
            {
                var dbState = new DbState(db);
                DbStates.Add(dbState);
                TopicToDbState.Add(db.Topic, dbState);
            }

            State.NodeContext.Dbs = TopicToDbState;

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        Receive<string>(m =>
        {
            BecomeLogged(Disconnected);
            Self.Tell(new MsgConnect());
        }, m => m == "disconnected" );

        Self.Tell("disconnected");
    }

    protected override void ChildTerminated(Terminated terminated)
    {
        Logger.Error("Communicator stopped");
        BecomeLogged(Disconnected);
    }

    private void Disconnected()
    {
        Receive<MsgConnect>(m =>
        {
            communicatorActor = Context.ActorOf(SiemensS7.Props(NodeId, NodeName, Config.CpuType, 
                Config.Ip, Config.Rack, Config.Slot), Config.Ip);
            Context.Watch(communicatorActor);
        });
        Receive<SiemensS7.MsgConnected>(m =>
        {
            foreach (var dbs in DbStates)
            {
                dbs.disabled = false;
            }
            BecomeLogged(Connected);
        });
    }

    private void Connected()
    {
        Receive<MsgDoPollDbs>(m =>
        {
            foreach (var dbs in DbStates)
            {
                if (dbs.config.Poll && ! dbs.disabled && dbs.config.DbNumber != 0)
                {
                    communicatorActor.Tell(
                        new SiemensS7.MsgReadDb(dbs.config.Topic,
                            dbs.config.DbNumber,
                            dbs.config.Offset,
                            dbs.config.Length));
                }
            }
        });

        Receive<SiemensS7.MsgDisconnected>(m =>
        {
            BecomeLogged(Disconnected);
        });

        Receive<MsgDisconnect>(m =>
        {
            StopPoll();

            Context.Stop(communicatorActor);

            BecomeLogged(Disconnected);
        });
        Receive<MsgStartPoll>(m =>
        {
            Logger.Info("StartPoll {ms}ms", m.IntervalMs);

            StopPoll();

            pollCancel = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                0, 
                m.IntervalMs, 
                Self, 
                new MsgDoPollDbs(), 
                Self);
        });
        Receive<MsgStopPoll>(m =>
        {
            StopPoll();
        });
        Receive<SiemensS7.MsgReadDbResult>(m =>
        {
            UpdateDbState(m);
        });
        // Initial read of the dbs
        if (Config.PollInterval > 0)
        {
            Self.Tell(new MsgStartPoll(Config.PollInterval));
        }
        else
        {
            Self.Tell(new MsgDoPollDbs());
        }
    }

    private void StopPoll()
    {
        if( pollCancel != null )
        {
            pollCancel.Cancel();
            pollCancel = null;
        }
    }

    protected override void PostStop()
    {
        StopPoll();
        base.PostStop();
    }


    private void UpdateDbState(SiemensS7.MsgReadDbResult m)
    {
        var logger = Logger.ForContext("Method", "UpdateDbState");

        DbState dbState;

        if (TopicToDbState.TryGetValue(m.Id, out dbState))
        {
            if (m.Error == 0)
            {
                dbState.byteStats.bytesRead += (ulong)m.Data.Length;
                dbState.byteStats.latestDuration = m.Duration;
                if (dbState.UpdateData(m.Data))
                {
                    dbState.byteStats.bytesUpdated += (ulong)m.Data.Length;
                    
                    var nodeEventData = Data.NodeEventData.FromDictionary(new Dictionary<string, object?>
                    {
                        ["frame"] = m.Data,
                        ["plc_db_number"] = dbState.config.DbNumber
                    });
                    var nodeEvent = new Data.NodeEvent(NodeName, nodeEventData, dbState.config.Topic);

                    PostNodeEvent(nodeEvent);
                }
            }
            else
            {
                dbState.byteStats.totalReadErrors += 1;
                dbState.byteStats.latestReadError = m.ErrorString;
                dbState.byteStats.latestReadErrorAt = DateTime.UtcNow;
                    
                if(dbState.byteStats.totalReadErrors > 5)
                {
                    dbState.disabled = true;
                }
                logger.Error("ReadData failed for {id} with error {error}", m.Id, m.Error);
            }
        }
        else
        {
            logger.Error("ReadData {id} not found", m.Id);
        }
    }

}