using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;

namespace KegBridgeCore.Nodes.Plc;

public class PlcNodeConfig : INodeTypeConfig
{
    public string CpuType { get; set; } = "S71500";
    public Int16 Rack { get; set; } = 0;
    public Int16 Slot { get; set; } = 2;
    [Required]
    public string Ip { get; set; }
    public short PollInterval { get; set; } = 500;

    public class Db
    {
        public string Topic { get; set; }
        [Required]
        public int DbNumber { get; set; }
        public int Offset { get; set; } = 0;
        [Required]
        public int Length { get; set; }
        public int WriteOffset { get; set; } = 0;
        [Required]
        public bool Poll { get; set; } = true;

        public Db()
        {
            Offset = 0;
            Length = 0;
            Poll = true;
        }
    }
    public List<Db> Dbs { get; set; } = new List<Db>();

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }

}