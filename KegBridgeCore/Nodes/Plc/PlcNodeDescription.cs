using KegBridgeCore.Nodes.Hmi;
using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.Plc;

public class PlcNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "plc";
    public string Description { get; private set; } = "Plc node";
    public Type NodeConfigType { get; private set; } = typeof(PlcNodeConfig);
    public Type NodeType { get; private set; } = typeof(PlcNode);
    public Type NodeConfigComponent { get; private set; } = null;

}