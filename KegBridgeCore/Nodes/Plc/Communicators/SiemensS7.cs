using Akka.Actor;
using Akka.Event;
using Akka.Logger.Serilog;
using S7.Net;
using System;
using System.Diagnostics;

namespace KegBridgeCore.Nodes.Plc.Communicators;

public class SiemensS7 : ReceiveActor
{
    private S7.Net.Plc plc = null;
    private ICancelable connectCancelable = null;
    private ErrorCode lastConnectErrorCode = ErrorCode.NoError;
    private ILoggingAdapter Logger { get; set; }

    public class MsgConnect
    {
        public MsgConnect() { }
    }
    public class MsgConnected
    {
        public MsgConnected() { }
    }
    public class MsgDisconnected
    {
        public MsgDisconnected() { }
    }
    public class MsgReadDb
    {
        public string Id { get; private set; }
        public int Db { get; private set; }
        public int Offset { get; private set; }
        public int Length { get; private set; }

        public MsgReadDb(string _id, int _db, int _offset, int _length)
        {
            Id = _id;
            Db = _db;
            Offset = _offset;
            Length = _length;
        }
    }
    public class MsgReadDbResult
    {
        public string Id { get; private set; } = null;
        public int Db { get; private set; } = 0;
        public int Offset { get; private set; } = 0;
        public int Length { get; private set; } = 0;
        public byte[] Data { get; private set; } = null;
        public int Error { get; private set; } = 0;
        public string ErrorString { get; private set; } = null;
        public int Duration { get; private set; } = 0; 
        public MsgReadDbResult(string _id, int _db, int _offset, int _length, byte[] _data, int _duration)
        {
            Id = _id;
            Data = _data;
            Db = _db;
            Offset = _offset;
            Length = _length;
            Duration = _duration;
        }
        public MsgReadDbResult(string _id, int _db, int _error, string _errorString)
        {
            Id = _id;
            Db = _db;
            Error = _error;
            ErrorString = _errorString;
        }

    }
    public class MsgWriteDb
    {
        public string Id { get; private set; }
        public int Db { get; private set; }
        public int Offset { get; private set; }
        public byte[] Data { get; private set; }

        public MsgWriteDb(string _id, int _db, int _offset, byte[] _data)
        {
            Id = _id;
            Db = _db;
            Offset = _offset;
            Data = _data;
        }
    }
    public class MsgWriteDbResult
    {
        public string Id { get; private set; }
        public int Db { get; private set; }
        public int Offset { get; private set; }
        public byte[] Data { get; private set; }

        public MsgWriteDbResult(string _id, int _db, int _offset, byte[] _data)
        {
            Id = _id;
            Data = _data;
            Db = _db;
            Offset = _offset;
        }
    }

    public SiemensS7( Guid nodeId, string nodeName, string type, string ip, Int16 rack, Int16 slot)
    {
        ILoggingAdapter contextLogger = Context.GetLogger<SerilogLoggingAdapter>();
        Logger = contextLogger.ForContext("NodeName", nodeName).ForContext("NodeId",nodeId);
            
        Logger.Info("Created {commType} {ip}", GetType().Name, ip);
        plc = new S7.Net.Plc((CpuType)Enum.Parse(typeof(CpuType), type, true), ip, rack, slot);
            
        Become(Disconnected);
    }

    private void CommonReceive(ILoggingAdapter logger)
    {
        Receive<string>(command =>
        {
            logger.Info("Receive<string> {command}", command);
            if (command == "boem")
            {
                throw new Exception("BOEM!");
            }
        });
    }

    private void Disconnected()
    {
        var logger = Logger.ForContext("Method", "Disconnected");

        CommonReceive(logger);
        Receive<MsgConnect>(m => Connect());

        Disconnect();
        Context.Parent.Tell(new MsgDisconnected());

        connectCancelable = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(100, 1000, Self, new MsgConnect(), Self);
    }

    private void Connected()
    {
        var logger = Logger.ForContext("Method", "Connected");
        CommonReceive(logger);
        Receive<MsgReadDb>(m => ReadDb(m));
        Receive<MsgWriteDb>(m => WriteDb(m));
    }

    private void Connect()
    {
        var logger = Logger.ForContext("Method", "Connect");
        try
        {
            if( ! plc.IsConnected )
            {
                logger.Debug("Connecting...");

                plc.Open();

                // TODO get CpuInfo
                logger.Info("Connected");
                    
                if ( connectCancelable != null )
                {
                    connectCancelable.Cancel();
                    connectCancelable = null;
                }

                Context.Parent.Tell(new MsgConnected());

                lastConnectErrorCode = ErrorCode.NoError;

                Become(Connected);
            }
            else
            {
                logger.Info("Already connected");
            }

        }
        catch( PlcException ex )
        {
            if( ex.ErrorCode != lastConnectErrorCode )
            {
                logger.Error(ex, "Connect failed");
                lastConnectErrorCode = ex.ErrorCode;
            }
        }
    }

    private void ReadDb(MsgReadDb readDb)
    {
        var logger = Logger.ForContext("Method", "ReadDb");
        try
        {
            Stopwatch sw = Stopwatch.StartNew();
            byte[] buffer = plc.ReadBytes(DataType.DataBlock, readDb.Db, readDb.Offset, readDb.Length);
            sw.Stop();
            Sender.Tell(new MsgReadDbResult(readDb.Id, readDb.Db, readDb.Offset, readDb.Length, buffer, (int)(sw.Elapsed.TotalMilliseconds*1000)));
        }
        catch( PlcException ex )
        {
            logger.Error(ex, "ReadDb failed");

            Sender.Tell(new MsgReadDbResult(readDb.Id, readDb.Db, (int)ex.ErrorCode, ex.Message));

            if( (ex.InnerException != null) && (ex.InnerException is System.IO.IOException))
            {
                Become(Disconnected);
            }
        }
    }
    private void WriteDb(MsgWriteDb writeDb)
    {
        var logger = Logger.ForContext("Method", "WriteDb");
        try
        {
            Stopwatch sw = Stopwatch.StartNew();
            plc.WriteBytes(DataType.DataBlock, writeDb.Db, writeDb.Offset, writeDb.Data);
            sw.Stop();
            Sender.Tell(new MsgWriteDbResult(writeDb.Id, writeDb.Db, writeDb.Offset,writeDb.Data ));
        }
        catch (PlcException ex)
        {
            logger.Error(ex, "WriteDb failed");

            if ((ex.InnerException != null) && (ex.InnerException is System.IO.IOException))
            {
                Become(Disconnected);
            }
        }
    }

    protected override void PostStop()
    {
        var logger = Logger.ForContext("Method", "PostStop");
        Disconnect();
    }

    private void Disconnect()
    {
        var logger = Logger.ForContext("Method", "Disconnect");
        if (plc != null)
        {
            try
            {
                logger.Info("Close connection");
                plc.Close();
                logger.Info("Connection closed");
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error closing connection.");
            }
        }
    }

    public static Props Props(Guid nodeId, string nodeName, string type, string ip, Int16 rack, Int16 slot)
    {
        return Akka.Actor.Props.Create(() => new SiemensS7(nodeId, nodeName, type, ip, rack, slot));
    }
}