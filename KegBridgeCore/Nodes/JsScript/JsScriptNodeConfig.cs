using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;

namespace KegBridgeCore.Nodes.JsScript;

public class JsScriptNodeConfig : INodeTypeConfig
{
    [Required]
    public string Script { get; set; }
    [Required]
    public List<string> Topics { get; set; } = new List<string>();
    public bool RemoteDebug { get; set; } = false;
    public bool WaitForDebugger { get; set; } = false;

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }

}