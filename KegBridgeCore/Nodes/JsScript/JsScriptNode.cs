using Akka.Actor;
using Akka.Event;
using Newtonsoft.Json;
using System;
using Microsoft.ClearScript.V8;
using Microsoft.ClearScript;
using Microsoft.ClearScript.JavaScript;
using System.IO;
using System.Collections.Generic;
using System.Dynamic;
using System.Net.Sockets;
using System.Text;
using KegBridgeCore.Actors.Messages;

namespace KegBridgeCore.Nodes.JsScript;

public class JsScriptNode : BaseNode
{
    private JsScriptNodeConfig Config { get; set; }
    private V8ScriptEngine v8;

    public class MsgTimer
    {
        public dynamic Callback { get; private set; }

        public MsgTimer(dynamic callback)
        {
            Callback = callback;
        }
    }

    public class ScriptHelper
    {
        //public List<Data.NodeEvent> NodeEvents { get; private set; }
        private BaseNode Node { get; set; }
        private string NodeName { get; set; }
        private IActorRef NodeActor { get; set; }
        private ActorSystem ActorSystem { get; set; }
        public List<ICancelable> RecurringTimers { get; private set; }
        private ICancelable CancelTimers;    
        public ScriptHelper(BaseNode node, IActorRef nodeActor, ActorSystem actorSystem, ICancelable cancelTimers)
        {
            Node = node;
            NodeName = node.NodeName;
            NodeActor = nodeActor;
            ActorSystem = actorSystem;
            //NodeEvents = new List<Data.NodeEvent>();
            RecurringTimers = new List<ICancelable>();
            CancelTimers = cancelTimers;
        }

        public static dynamic ConvertScriptObject(object arg)
        {
            if( arg is ScriptObject )
            {
                dynamic scriptObject = arg;
                if (scriptObject.constructor.name == "Array")
                {
                    int length = Convert.ToInt32(scriptObject.length);
                    var array = new List<object>(length);
                    for (var index = 0; index < length; ++index)
                    {
                        array.Add(ConvertScriptObject(scriptObject[index]));
                    }
                    return array;
                }
                else
                {
                    dynamic o = new ExpandoObject();
                    var oDict = o as IDictionary<string, object>;

                    foreach (var name in scriptObject.GetDynamicMemberNames())
                    {
                        var value = ((dynamic)scriptObject)[name];

                        var nestedObject = value as ScriptObject;
                        if (nestedObject != null)
                        {
                            value = ConvertScriptObject(nestedObject);
                        }

                        oDict[name]=value;
                    }

                    return o;
                }
            }
            return arg;
        }

        public void PostEvent(string topic, ScriptObject data)
        {
            var _data = ConvertScriptObject(data);

            Node.PostNodeEvent(new Data.NodeEvent(NodeName, _data, topic));
        }
        public void PostEvent(DateTime timestamp, string topic, ScriptObject data)
        {
            var _data = ConvertScriptObject(data);

            Node.PostNodeEvent(new Data.NodeEvent(NodeName,_data, topic,timestamp));
        }

        //public void Clear()
        //{
        //    NodeEvents.Clear();
        //}

        public void OneShot(int timeMs, ScriptObject callback)
        {
            ActorSystem.Scheduler.ScheduleTellOnce(timeMs, NodeActor, new MsgTimer(callback), NodeActor, CancelTimers);
        }

        public int Recurring(int timeMs, dynamic callback)
        {
            RecurringTimers.Add(ActorSystem.Scheduler.ScheduleTellRepeatedlyCancelable(0, timeMs, NodeActor, new MsgTimer(callback), NodeActor));
            return RecurringTimers.Count;
        }

        public void CancelRecurring(int i)
        {
            if( i>0 && i<=RecurringTimers.Count )
            {
                RecurringTimers[i - 1].Cancel();
            }
        }
    }

    private ScriptHelper scriptHelper { get; set; }
    private FileSystemWatcher scriptFileWatcher;
    private List<string> LoadedScriptFiles;
    private bool ReloadScripts;
    private Cancelable CancelTimers;

    public JsScriptNode(Guid id, string name) : base(id,name)
    {
        LoadedScriptFiles = new List<string>();
        ReloadScripts = false;
    }

    protected override bool Configure(object config)
    {
        Config = config as JsScriptNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            UnsubscribeAll();
            foreach( var topic in Config.Topics )
            {
                Subscribe(topic);
            }

            var flags = V8ScriptEngineFlags.EnableDynamicModuleImports
                        | V8ScriptEngineFlags.EnableStringifyEnhancements
                        | V8ScriptEngineFlags.DisableGlobalMembers
                        | V8ScriptEngineFlags.EnableDateTimeConversion;

            if( Config.RemoteDebug )
            {
                flags |= V8ScriptEngineFlags.EnableDebugging | V8ScriptEngineFlags.EnableRemoteDebugging;
            }

            if( Config.WaitForDebugger )
            {
                flags |= V8ScriptEngineFlags.AwaitDebuggerAndPauseOnStart;
            }

            v8 = new V8ScriptEngine(NodeName,flags);

            v8.DocumentSettings.AccessFlags = DocumentAccessFlags.EnableFileLoading;
                
            v8.DocumentSettings.SearchPath = Path.Combine(Directory.GetCurrentDirectory(), "scripts");

            LoadedScriptFiles.Clear();

            v8.DocumentSettings.LoadCallback += (ref DocumentInfo info) =>
            {
                Logger.Info("import script {@info}", info.Uri.LocalPath);
                LoadedScriptFiles.Add(info.Uri.LocalPath);
            };

            DocumentLoader.Default.DiscardCachedDocuments();

            //
            // Watch files in scripts folder

            scriptFileWatcher = new FileSystemWatcher();
            scriptFileWatcher.Path = v8.DocumentSettings.SearchPath;
            scriptFileWatcher.IncludeSubdirectories = true;
            scriptFileWatcher.Filter = "*.js";
            scriptFileWatcher.NotifyFilter = NotifyFilters.DirectoryName 
                                             | NotifyFilters.LastWrite 
                                             | NotifyFilters.FileName;

            IActorRef MySelf = Self;
            IScheduler MyScheduler = Context.System.Scheduler;

            FileSystemEventHandler s = (s, e) =>
            {
                Logger.Info("script changed {script}", e.FullPath);
                if( LoadedScriptFiles.Contains(e.FullPath) )
                {
                    if( ! ReloadScripts )
                    {
                        MyScheduler.ScheduleTellOnce(200, MySelf, new MsgNodeRestart(NodeId), MySelf);
                        ReloadScripts = true;
                    }
                }
            };
            RenamedEventHandler sr = (s, e) =>
            {
                Logger.Info("script renamed {script}", e.FullPath);
                if (LoadedScriptFiles.Contains(e.FullPath))
                {
                    if (! ReloadScripts)
                    {
                        MyScheduler.ScheduleTellOnce(200, MySelf, new MsgNodeRestart(NodeId), MySelf);
                        ReloadScripts = true;
                    }
                }
            };

            scriptFileWatcher.Changed += s;
            scriptFileWatcher.Renamed += sr;

            scriptFileWatcher.EnableRaisingEvents = true;

            // Prepare script environment and load the script

            CancelTimers = new Cancelable(Context.System.Scheduler);
            scriptHelper = new ScriptHelper(this, Self, Context.System, CancelTimers);

            v8.AddHostObject("Host", new HostFunctions());
            v8.AddHostObject("Lib", new HostTypeCollection("mscorlib", "System.Core"));
            v8.AddHostObject("Logger", Serilog.Log.Logger.ForContext("SourceContext", "KegBridgeCore.Nodes.JsScript.JsScriptNode.Scripts"));
            v8.AddHostObject("Node", scriptHelper);
            v8.AddHostObject("State", State.NodeContext);
            v8.AddHostObject("Utf8", new UTF8Encoding());

            v8.AddHostType("File", typeof(File));
            v8.AddHostType("Directory", typeof(Directory));
            v8.AddHostType("TcpClient", typeof(System.Net.Sockets.TcpClient));
            v8.AddHostType("NetworkStream", typeof(NetworkStream));

            try
            {
                v8.Script.ProcessEvent = v8.Evaluate(new DocumentInfo { Category = ModuleCategory.Standard },
                    $"import handler from '{Config.Script}'; (function(tm,n,t,jd) {{ return handler(tm,n,t,jd); }})"
                );

                Logger.Info("script loaded.");

                return true;
            }
            catch( ScriptEngineException ex )
            {
                Logger.Error("Error loading script {script} : {error}", Config.Script, ex.ErrorDetails);
            }
        }
        return false;
    }

    protected override void Running()
    {
        Receive<Data.NodeEvent>(nodeEvent =>
        {
            LogNodeEventIn(nodeEvent);

            try
            {
                var v8_scriptobject = v8.Script.JSON.parse(JsonConvert.SerializeObject(nodeEvent.Data));

                var script_result = v8.Script.ProcessEvent(
                    nodeEvent.Timestamp, 
                    nodeEvent.Node, 
                    nodeEvent.Topic,
                    v8_scriptobject );
                    
                dynamic host_result = ScriptHelper.ConvertScriptObject(script_result);

                Logger.Debug("ProcessEvent result {@host_result}", (object)host_result);

            }
            catch (ScriptEngineException ex)
            {
                Logger.Error("Error in {script} ProcessEvent : {error}", Config.Script, ex.ErrorDetails);
            }
            finally
            {
                //foreach (var newNodeEvent in scriptHelper.NodeEvents)
                //{
                //    PostNodeEvent(newNodeEvent);
                //}
            }
        });

        Receive<string>(cmd =>
        {
            try
            {
                var script_result = v8.Script.Evaluate(cmd);
                dynamic host_result = ScriptHelper.ConvertScriptObject(script_result);
                Logger.Info("script result {@host_result}", (object)host_result);
            }
            catch (ScriptEngineException ex)
            {
                Logger.Error("Error in {cmd} : {error}", cmd, ex.ErrorDetails);
            }
        });

        Receive<MsgTimer>(msgTimer =>
        {
            try
            {
                //scriptHelper.Clear();
                msgTimer.Callback();
            }
            catch (ScriptEngineException ex)
            {
                Logger.Error("Error in {script} MsgTimer: {error}", Config.Script, ex.ErrorDetails);
            }
            finally
            {
                //foreach (var newNodeEvent in scriptHelper.NodeEvents)
                //{
                //    PostNodeEvent(newNodeEvent);
                //}
            }
        });
    }

    protected override void PostStop()
    {
        base.PostStop();
        foreach(var cancelTimer in scriptHelper.RecurringTimers)
        {
            cancelTimer.Cancel();
        }
        CancelTimers.Cancel();
    }
}