using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.JsScript;

public class JsScriptNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "script";
    public string Description { get; private set; } = "Script node";
    public Type NodeConfigType { get; private set; } = typeof(JsScriptNodeConfig);
    public Type NodeType { get; private set; } = typeof(JsScriptNode);
    public Type NodeConfigComponent { get; private set; } = null;

}