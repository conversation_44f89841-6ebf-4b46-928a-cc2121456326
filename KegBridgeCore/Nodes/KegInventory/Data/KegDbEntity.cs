using Opc.Ua;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Text;

namespace KegBridgeCore.Nodes.KegInventory.Data;

[Table("kegs")]
public class KegDbEntity
{
    private DateTime? _created_at = null;

    [Column("serial_number")]
    [Key]
    public string SerialNumber { get; set; }
    [Column("created_at")]
    public DateTime? CreatedAt
    {
        get
        {
            if( _created_at == null )
            {
                _created_at = DateTime.UtcNow;
            }
            return (DateTime)_created_at;
        }
        set
        {
            _created_at = value;
        }
    }
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
    [Column("keg_run_count")]
    public int KegRunCount { get; set; }

    public KegDbEntity UpdatedNow()
    {
        UpdatedAt = DateTime.UtcNow;
        return this;
    }
}