using Opc.Ua;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Text;

namespace KegBridgeCore.Nodes.KegInventory.Data;

[Table("keg_runs")]
public class KegRunDbEntity
{
    private DateTime? _created_at = null;

    [Column("id")]
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    [Column("created_at")]
    public DateTime? CreatedAt
    {
        get
        {
            if( _created_at == null )
            {
                _created_at = DateTime.UtcNow;
            }
            return (DateTime)_created_at;
        }
        set
        {
            _created_at = value;
        }
    }
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    [Column("keg_serial_number")]
    public string KegSerialNumber { get; set; }

    [Column("product")]
    public String Product{ get; set; }
    [Column("quality")]
    public String Quality { get; set; }

    public KegRunDbEntity UpdatedNow()
    {
        UpdatedAt = DateTime.UtcNow;
        return this;
    }

}