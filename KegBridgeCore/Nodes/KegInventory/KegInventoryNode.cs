using Akka.Actor;
using Akka.Event;
using Akka.IO;
using Dapper;
using Dapper.FastCrud;
using Dapper.Logging;
using KegBridgeCore.Data;
using KegBridgeCore.Nodes.Hmi;
using KegBridgeCore.Nodes.KegInventory.Data;
using Microsoft.AspNetCore.Connections;
using Microsoft.CodeAnalysis.Differencing;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using static KegBridgeCore.Nodes.Hmi.HmiNode;

namespace KegBridgeCore.Nodes.KegInventory;

public class KegInventoryNode : BaseNode
{
    private KegInventoryNodeConfig Config { get; set; }
    private IServiceScopeFactory serviceScopeFactory;

    public KegInventoryNode(IServiceScopeFactory _serviceScopeFactory, Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
        serviceScopeFactory = _serviceScopeFactory;
    }

    protected override bool Configure(object config)
    {
        Config = config as KegInventoryNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        Receive<NodeEvent>(ProcessNodeEvent);

        UnsubscribeAll();
        Subscribe(Config.Topic);
    }

    protected void ProcessNodeEvent(NodeEvent nodeEvent)
    {
        LogNodeEventIn(nodeEvent);

        if (nodeEvent.Data.ContainsKey("serial_number"))
        {
            AddKegRun(nodeEvent.Data);
        }
    }

    protected void AddKegRun(NodeEventData data)
    {
        Logger.Info("Add KegRun");

        using (IServiceScope serviceScope = serviceScopeFactory.CreateScope())
        {
            var dbConnFactory = serviceScope.ServiceProvider.GetService<IDbConnectionFactory>();

            using (var connection = dbConnFactory.CreateConnection())
            {
                data.TryGet<string>("serial_number", out var serialNumber);
              
                var keg = connection.Get<Data.KegDbEntity>( new Data.KegDbEntity { SerialNumber = serialNumber } );

                if( keg != null)
                {
                    Logger.Debug("Keg found");
                    keg.KegRunCount++;
                    connection.Update<Data.KegDbEntity>(keg.UpdatedNow());
                }
                else
                {
                    Logger.Debug("Keg not found");

                    keg = new Data.KegDbEntity
                    {
                        SerialNumber = serialNumber,
                        KegRunCount = 1
                    };

                    connection.Insert(keg.UpdatedNow());
                }

                Logger.Info("Keg {@keg}", keg);

                var kegRun = new Data.KegRunDbEntity
                {
                    KegSerialNumber = keg.SerialNumber
                };

                if( data.ContainsKey("product") )
                {
                    kegRun.Product = (string)data["product"];
                }
                if (data.ContainsKey("quality"))
                {
                    kegRun.Quality = (string)data["quality"];
                }

                connection.Insert(kegRun.UpdatedNow());
            }
        }
    }
}