using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;
using System;
using Opc.Ua;

namespace KegBridgeCore.Nodes.KegInventory;

public class KegInventoryNodeConfig : INodeTypeConfig
{
    [Required]
    public string Topic { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }
}