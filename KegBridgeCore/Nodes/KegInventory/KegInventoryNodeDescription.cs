using KegBridgeCore.Nodes.Hmi;
using KegBridgeCore.Services;
using System;
namespace KegBridgeCore.Nodes.KegInventory;

public class KegInventoryNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "keg_inventory";
    public string Description { get; private set; } = "Keg Inventory";
    public Type NodeConfigType { get; private set; } = typeof(KegInventoryNodeConfig);
    public Type NodeType { get; private set; } = typeof(KegInventoryNode);
    public Type NodeConfigComponent { get; private set; } = null ;

}