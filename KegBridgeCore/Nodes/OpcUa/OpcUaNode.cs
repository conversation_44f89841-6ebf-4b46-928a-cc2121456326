using Akka.Actor;
using Akka.Event;
using KegBridgeCore.Data;
using Newtonsoft.Json;
using Opc.Ua;
using Opc.Ua.Client;
using Opc.Ua.Client.ComplexTypes;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Dynamic;
using System.Threading;
using System.Threading.Tasks;

namespace KegBridgeCore.Nodes.OpcUa;

public class OpcUaNode : BaseNode
{
    private OpcUaNodeConfig Config { get; set; }

    private ApplicationConfiguration applicationConfiguration;
    private Session uaSession;
    private CancellationTokenSource cts;
    private ConfiguredEndpoint endpoint;
    private Subscription subscription;

    #region Messages
    public class MsgConnect
    {
    }
    public class MsgDisconnected
    {
        public KeepAliveEventArgs KeepAliveEvent { get; }
        public MsgDisconnected(KeepAliveEventArgs e)
        {
            KeepAliveEvent = e;
        }
    }
    #endregion

    public OpcUaNode(Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
        cts = new CancellationTokenSource();
    }

    protected override bool Configure(object config)
    {
        Config = config as OpcUaNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        applicationConfiguration = new ApplicationConfiguration();
        applicationConfiguration.ClientConfiguration = new ClientConfiguration();
        applicationConfiguration.CertificateValidator.CertificateValidation += (CertificateValidator validator, CertificateValidationEventArgs e) =>
        {
            var autoAccept = true;
            if (e.Error.StatusCode == Opc.Ua.StatusCodes.BadCertificateUntrusted)
            {
                e.Accept = autoAccept;
                if (autoAccept)
                {
                    Logger.Debug("Accepted Certificate: {0}", e.Certificate.Subject);
                }
                else
                {
                    Logger.Debug("Rejected Certificate: {0}", e.Certificate.Subject);
                }
            }
        };

        Receive<string>(m =>
        {
            BecomeLogged(Disconnected);
            Self.Tell(new MsgConnect());
        }, m => m == "disconnected");

        Self.Tell("disconnected");
    }
       

    protected void Disconnected()
    {
        ReceiveAsync<MsgConnect>(ConnectServer);
    }

    protected void Connected()
    {
        // store a reference to ourselve to use in the KeepAlive callback
        uaSession.Handle = Self;

        uaSession.DeleteSubscriptionsOnClose = true;
        uaSession.TransferSubscriptionsOnReconnect = false;

        // register keep alive handler
        uaSession.KeepAlive += (Opc.Ua.Client.ISession session, KeepAliveEventArgs e) =>
        {
            try
            {
                // check for events from discarded sessions.
                if (!uaSession.Equals(session))
                {
                    return;
                }

                // start reconnect sequence on communication error.
                if (ServiceResult.IsBad(e.Status))
                {
                    Logger.Debug("KeepAlive status {0}", e.Status);
                    ((IActorRef)(session.Handle)).Tell(new MsgDisconnected(e));
                    e.CancelKeepAlive = true;
                    return;
                }
            }
            catch (Exception exception)
            {
                Logger.Error("Error in OnKeepAlive. {0}", exception);
            }
        };

        // setup subscription
        // subscribe to our incoming event topics
        UnsubscribeAll();
        if (Config.WriteTopic != null)
        {
            Subscribe(Config.WriteTopic);
        }
        if (Config.ReadTopic != null)
        {
            Subscribe(Config.ReadTopic);
        }


        subscription = new Subscription(uaSession.DefaultSubscription)
        {
            DisplayName = "KegBridgeCoreSubscription",
            PublishingEnabled = true,
            PublishingInterval = Config.PublishingInterval,
            LifetimeCount = 0,
            MinLifetimeInterval = 120_000
        };
        uaSession.AddSubscription(subscription);
        subscription.Create();
        Logger.Info("Checking {0} monitored items", Config.MonitoredItems.Count);
        foreach (var monitoredItemConfig in Config.MonitoredItems)
        {
            Opc.Ua.Node node = null;
            if( ! monitoredItemConfig.Enabled )
            {
                continue;
            }

            try
            {
                Logger.Debug("creating monitored item {0}", monitoredItemConfig.NodeId);

                var nodeId = new NodeId(monitoredItemConfig.NodeId);
                if (uaSession.NodeCache.Exists(nodeId) )
                {
                    MonitoredItem monitoredItem = new MonitoredItem(subscription.DefaultItem);

                    if (monitoredItemConfig.Topic == null)
                    {
                        monitoredItem.DisplayName = Config.NotifyTopic;
                    }
                    else
                    {
                        monitoredItem.DisplayName = monitoredItemConfig.Topic;
                    }

                    monitoredItem.StartNodeId = nodeId;
                    monitoredItem.AttributeId = Attributes.Value;
                    monitoredItem.SamplingInterval = monitoredItemConfig.SamplingInterval;
                    monitoredItem.QueueSize = monitoredItemConfig.QueueSize;
                    monitoredItem.DiscardOldest = true;
                    monitoredItem.Notification += OnMonitoredItemNotification;
                    monitoredItem.Handle = Self;
                    subscription.AddItem(monitoredItem);
                } 
                else
                {
                    Logger.Warning("Subscribe failed, node {node_id} doesn't exist.", nodeId.ToString());
                }
            }
            catch(Opc.Ua.ServiceResultException ex)
            {

                Logger.Warning(ex, "Error subscribing to node {0}", node);
            }
        }

        Logger.Info("Subscribing to {0} nodes", subscription.MonitoredItemCount);
        subscription.ApplyChanges();

        ReceiveAsync<NodeEvent>(ProcessNodeEvent);
        Receive<MsgDisconnected>(m => {
            BecomeLogged(Disconnected);
            Logger.Debug("Disconnected {status}", m.KeepAliveEvent.Status);
            Context.System.Scheduler.ScheduleTellOnce(5000,Self,new MsgConnect(),Self);
        });
    }

    protected async Task ProcessNodeEvent(NodeEvent nodeEvent)
    {
        if (nodeEvent.Node == NodeName)
        {
            PostNodeEvent(nodeEvent);
        } 
        else if( nodeEvent.Topic == Config.WriteTopic )
        {
            await WriteNode(nodeEvent.Data);
        }
        else if (nodeEvent.Topic == Config.ReadTopic)
        {
            await ReadNode(nodeEvent.Data);
        }
    }

    protected async Task WriteNode(NodeEventData eventData)
    {
        //TODO implement!
        throw new NotImplementedException();
        // var data = eventData as IDictionary<string, object>;
        //
        // object o_node_id, o_data_value;
        // data.TryGetValue("node_id", out o_node_id);
        // string node_id = o_node_id as string;
        // data.TryGetValue("data_value", out o_data_value);
        //
        // if ( node_id != null && o_data_value != null )
        // {
        //     try
        //     {
        //         WriteValueCollection nodesToWrite = new WriteValueCollection();
        //
        //         string json = JsonConvert.SerializeObject(eventData);
        //
        //         var dataValue = JsonDecodeDataValue(uaSession.MessageContext, json);
        //
        //         WriteValue writeVal = new WriteValue();
        //         writeVal.NodeId = new NodeId(node_id);
        //         writeVal.AttributeId = Attributes.Value;
        //         writeVal.Value = dataValue;
        //         nodesToWrite.Add(writeVal);
        //
        //         WriteResponse writeResponse = await uaSession.WriteAsync(null,
        //             nodesToWrite, default);
        //
        //         dynamic nodeData = new ExpandoObject();
        //
        //         nodeData.node_id = node_id;
        //         nodeData.data_value = o_data_value;
        //         nodeData.status = writeResponse.Results[0].ToString();
        //
        //         PostNodeEvent(new NodeEvent(NodeName, Config.WriteTopic, nodeData));
        //     }
        //     catch( Exception ex)
        //     {
        //         Logger.Error(ex, "WriteNode error");
        //         dynamic resultData = new ExpandoObject();
        //         resultData.node_id = node_id;
        //         resultData.status = new Opc.Ua.StatusCode(Opc.Ua.StatusCodes.Bad).ToString();
        //         resultData.message = ex.Message;
        //         PostNodeEvent(new NodeEvent(NodeName, Config.WriteTopic, resultData));
        //     }
        // }
        // else
        // {
        //     Logger.Error("WriteNode required data: node_id,data_value");
        //     dynamic resultData = new ExpandoObject();
        //     resultData.node_id = node_id;
        //     resultData.status = new Opc.Ua.StatusCode(Opc.Ua.StatusCodes.Bad).ToString();
        //     resultData.message = "Required data: node_id,data_value";
        //     PostNodeEvent(new NodeEvent(NodeName, Config.WriteTopic, resultData));
        // }
    }

    protected async Task ReadNode(NodeEventData eventData)
    {
        //TODO implement!
        throw new NotImplementedException();
        // var data = eventData as IDictionary<string, object>;
        //
        // object o_node_id, o_topic;
        //
        //
        // data.TryGetValue("node_id", out o_node_id);
        // data.TryGetValue("topic", out o_topic);
        //
        // string node_id = o_node_id as string;
        // string topic= o_topic as string;
        //
        // if( node_id == null && topic != null)
        // {
        //     foreach (var monitored_item in Config.MonitoredItems)
        //     {
        //         if (monitored_item.Topic == topic)
        //         {
        //             node_id = monitored_item.NodeId;
        //             break;
        //         }
        //     }
        // }
        //
        // if ( node_id != null )
        // {
        //     try
        //     {
        //         var nodeId = new NodeId(node_id);
        //         var dataValue = await uaSession.ReadValueAsync(nodeId);
        //
        //         var nodeEvent = DataValueToNodeEvent((topic == null) ? Config.ReadTopic : topic, nodeId, dataValue);
        //
        //         PostNodeEvent(nodeEvent);
        //     }
        //     catch (Exception ex)
        //     {
        //         Logger.Error(ex, "ReadNode error");
        //         dynamic resultData = new ExpandoObject();
        //         resultData.node_id = node_id;
        //         resultData.status = new Opc.Ua.StatusCode(Opc.Ua.StatusCodes.Bad).ToString();
        //         resultData.message = ex.Message;
        //         PostNodeEvent(new NodeEvent(NodeName, (topic == null) ? Config.ReadTopic : topic, resultData));
        //     }
        // } 
        // else
        // {
        //     Logger.Error("ReadNode required node_id or topic");
        //     dynamic resultData = new ExpandoObject();
        //     resultData.node_id = node_id;
        //     resultData.status = new Opc.Ua.StatusCode(Opc.Ua.StatusCodes.Bad).ToString();
        //     resultData.message = "Required data: node_id,data_value";
        //     PostNodeEvent(new NodeEvent(NodeName, (topic == null) ? Config.ReadTopic : topic, resultData));
        //
        // }
    }

    protected async Task ConnectServer(MsgConnect connect)
    {
        if (uaSession != null)
        {
            Logger.Info("Recreate session to {endpoint}", endpoint);
            await uaSession.CloseAsync();
            uaSession.Dispose();
            uaSession = null;
        }

        if (uaSession == null) 
        {
            Logger.Info("Creating session to {server_url}", Config.ServerUrl);

            try
            {
                var selectedEndpoint = CoreClientUtils.SelectEndpoint(Config.ServerUrl, false, 15000);
                var endpointConfiguration = EndpointConfiguration.Create(applicationConfiguration);
                endpoint = new ConfiguredEndpoint(null, selectedEndpoint, endpointConfiguration);

                uaSession = await Session.Create(applicationConfiguration,
                    endpoint,
                    false,
                    "KegBridgeCore",
                    60000,
                    new UserIdentity(new AnonymousIdentityToken()),
                    null);

                if (uaSession != null && uaSession.Connected)
                {
                    Logger.Info("Created session");
                    await LoadTypeSystemAsync();
                    BecomeLogged(Connected);
                }
            }
            catch( Opc.Ua.ServiceResultException ex)
            {
                Logger.Error(ex, "ConnectServer error");
                Context.System.Scheduler.ScheduleTellOnce(5000, Self, new MsgConnect(), Self);
            }
        } 
    }
        
    private void OnMonitoredItemNotification(MonitoredItem monitoredItem, MonitoredItemNotificationEventArgs e)
    {
        try
        {
            // Log MonitoredItem Notification event
            MonitoredItemNotification notification = e.NotificationValue as MonitoredItemNotification;
            Logger.Debug("Notification: {0} \"{1}\" with Value = {2}.", notification.Message.SequenceNumber, monitoredItem.ResolvedNodeId, notification.Value);

            var nodeEvent = DataValueToNodeEvent(monitoredItem.DisplayName, monitoredItem.ResolvedNodeId, notification.Value);
            ((IActorRef)(monitoredItem.Handle)).Tell(nodeEvent);

            var nodeEvent2 = DataValueToNodeEventViaJson(monitoredItem.DisplayName, monitoredItem.ResolvedNodeId, notification.Value);
            ((IActorRef)(monitoredItem.Handle)).Tell(nodeEvent2);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "OnMonitoredItemNotification error: {0}", ex.Message);
        }
    }

    private NodeEvent DataValueToNodeEvent(string topic, Opc.Ua.NodeId nodeId, DataValue dataValue )
    {
        dynamic eventData = new ExpandoObject();

        // eventData.node_id = nodeId.ToString();
        // eventData.status = dataValue.StatusCode.ToString();
        //
        // string json = JsonEncodeDataValue(uaSession.MessageContext, dataValue);
        // dynamic extensionObject = JsonConvert.DeserializeObject<ExpandoObject>(json);
        // eventData.data_value = extensionObject.data_value;
        //
        // var node = uaSession.NodeCache.Find(nodeId) as Opc.Ua.VariableNode;
        //
        // if (node != null)
        // {
        //     eventData.display_text = uaSession.NodeCache.GetDisplayText(node);
        //     eventData.node_class = node.NodeClass.ToString();
        //     var dataTypeNode = uaSession.NodeCache.Find(node.DataType) as Opc.Ua.Node;
        //     if (dataTypeNode != null)
        //     {
        //         eventData.data_type = dataTypeNode.DisplayName.ToString();
        //     }
        //     eventData.value_rank = GetValueRankDisplayText(node.ValueRank);
        //     if (node.ValueRank != ValueRanks.Scalar)
        //     {
        //         eventData.array_dimensions = node.ArrayDimensions.ToArray();
        //     }
        // }

        eventData.data_value = OpcUaDataValueConverter.ConvertDataValueWithMetadata(dataValue);
        return NodeEvent.FromExpando(NodeName, eventData, topic, dataValue.ServerTimestamp);
    }
    
    private NodeEvent DataValueToNodeEventViaJson(string topic, Opc.Ua.NodeId nodeId, DataValue dataValue )
    {
        dynamic eventData = new ExpandoObject();

        eventData.node_id = nodeId.ToString();
        eventData.status = dataValue.StatusCode.ToString();
        
        string json = JsonEncodeDataValue(uaSession.MessageContext, dataValue);
        dynamic extensionObject = JsonConvert.DeserializeObject<ExpandoObject>(json);
        eventData.data_value = extensionObject.data_value;
        
        var node = uaSession.NodeCache.Find(nodeId) as Opc.Ua.VariableNode;
        
        if (node != null)
        {
            eventData.display_text = uaSession.NodeCache.GetDisplayText(node);
            eventData.node_class = node.NodeClass.ToString();
            var dataTypeNode = uaSession.NodeCache.Find(node.DataType) as Opc.Ua.Node;
            if (dataTypeNode != null)
            {
                eventData.data_type = dataTypeNode.DisplayName.ToString();
            }
            eventData.value_rank = GetValueRankDisplayText(node.ValueRank);
            if (node.ValueRank != ValueRanks.Scalar)
            {
                eventData.array_dimensions = node.ArrayDimensions.ToArray();
            }
        }

        return NodeEvent.FromExpando(NodeName, eventData, topic, dataValue.ServerTimestamp);
    }

    public static string GetValueRankDisplayText(int valueRank)
    {
        switch (valueRank)
        {
            case ValueRanks.Any: return "Any";
            case ValueRanks.Scalar: return "Scalar";
            case ValueRanks.ScalarOrOneDimension: return "ScalarOrOneDimension";
            case ValueRanks.OneOrMoreDimensions: return "OneOrMoreDimensions";
            case ValueRanks.OneDimension: return "OneDimension";
            case ValueRanks.TwoDimensions: return "TwoDimensions";
        }

        return valueRank.ToString();
    }

    public static string JsonEncodeDataValue(IServiceMessageContext messageContext, DataValue value)
    {
        string textbuffer;
        using (var jsonEncoder = new JsonEncoder(messageContext, true))
        {
            jsonEncoder.WriteDataValue("data_value", value);
            textbuffer = jsonEncoder.CloseAndReturnText();
        }
        return textbuffer;
    }

    public static DataValue JsonDecodeDataValue(IServiceMessageContext messageContext, string json)
    {
        DataValue dataValue = null;
        using (var jsonDecoder = new JsonDecoder(json, messageContext))
        {
            dataValue = jsonDecoder.ReadDataValue("data_value");
        }

        return dataValue;
    }

    protected override void PostStop()
    {
        if (uaSession != null)
        {
            if( subscription != null )
            {
                Logger.Info("Remove subscription");
                uaSession.RemoveSubscription(subscription);
            }

            Logger.Info("Disconnecting session");
            uaSession.Close();
            uaSession.Dispose();
            uaSession = null;

            Logger.Info("Session disconnected");
        }

        base.PostStop();
    }

    public async Task LoadTypeSystemAsync()
    {
        Logger.Info("Load the server type system.");

        Stopwatch stopWatch = new Stopwatch();
        stopWatch.Start();

        var complexTypeSystem = new ComplexTypeSystem(uaSession);
        await complexTypeSystem.Load().ConfigureAwait(false);

        stopWatch.Stop();

        Logger.Debug("Loaded {0} types took {1}ms.",
            complexTypeSystem.GetDefinedTypes().Length, stopWatch.ElapsedMilliseconds);

        if (false)
        {
            Logger.Debug("Custom types defined for this session:");
            foreach (var type in complexTypeSystem.GetDefinedTypes())
            {
                Logger.Debug($"{type.Namespace}.{type.Name}");
            }

            Logger.Debug($"Loaded {uaSession.DataTypeSystem.Count} dictionaries:");
            foreach (var dictionary in uaSession.DataTypeSystem)
            {
                Logger.Debug($" + {dictionary.Value.Name}");
                foreach (var type in dictionary.Value.DataTypes)
                {
                    Logger.Debug($" -- {type.Key}:{type.Value}");
                }
            }
        }
    }
}