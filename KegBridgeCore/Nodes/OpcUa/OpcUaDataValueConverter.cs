using System;
using Opc.Ua;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Xml;

namespace KegBridgeCore.Nodes.OpcUa
{
    /// <summary>
    /// Efficient converter for OPC UA DataValue objects to .NET types compatible with NodeEventData
    /// Avoids JSON serialization/deserialization for better performance
    /// </summary>
    public static class OpcUaDataValueConverter
    {
        /// <summary>
        /// Converts an OPC UA DataValue to a .NET object compatible with NodeEventData
        /// </summary>
        public static object? ConvertDataValue(DataValue dataValue)
        {
            if (dataValue?.Value == null)
                return null;

            // DataValue.Value can be different types, not just Variant
            return dataValue.Value switch
            {
                Variant variant => ConvertVariant(variant),
                ExtensionObject extensionObject => ConvertExtensionObject(extensionObject),
                _ => ConvertScalarValue(dataValue.Value)
            };
        }

        /// <summary>
        /// Converts an OPC UA Variant to a .NET object
        /// </summary>
        public static object? ConvertVariant(Variant variant)
        {
            if (variant.Value == null)
                return null;

            var value = variant.Value;
            var typeInfo = variant.TypeInfo;

            // Handle arrays
            if (typeInfo?.ValueRank > ValueRanks.Scalar)
            {
                return ConvertArray(value);
            }

            // Handle scalar values
            return ConvertScalarValue(value);
        }

        /// <summary>
        /// Converts OPC UA scalar values to .NET types
        /// </summary>
        private static object? ConvertScalarValue(object value)
        {
            return value switch
            {
                // Primitive types that are directly compatible
                bool b => b,
                byte b => b,
                sbyte sb => sb,
                short s => s,
                ushort us => us,
                int i => i,
                uint ui => ui,
                long l => l,
                ulong ul => ul,
                float f => f,
                double d => d,
                decimal dec => dec,
                string str => str,
                DateTime dt => dt,
                
                // OPC UA specific types that need conversion
                StatusCode statusCode => statusCode.Code,
                NodeId nodeId => nodeId.ToString(),
                ExpandedNodeId expandedNodeId => expandedNodeId.ToString(),
                QualifiedName qualifiedName => qualifiedName.ToString(),
                LocalizedText localizedText => localizedText.Text ?? localizedText.ToString(),
                Uuid uuid => uuid.ToString(),
                // ByteString handling - check if it's a byte array
                byte[] byteArray when value.GetType().Name.Contains("ByteString") => byteArray,
                XmlElement xmlElement => xmlElement.OuterXml,
                
                // Enums - convert to their underlying value or string representation
                Enum enumValue => enumValue.ToString(),
                
                // Extension objects - try to extract useful data
                ExtensionObject extensionObject => ConvertExtensionObject(extensionObject),
                
                // DataValue (nested)
                DataValue dataValue => ConvertDataValue(dataValue),
                
                // Variant (nested)
                Variant variant => ConvertVariant(variant),
                
                // Default case - try ToString() or return as-is
                _ => ConvertComplexObject(value)
            };
        }

        /// <summary>
        /// Converts OPC UA arrays to .NET arrays or lists
        /// </summary>
        private static object? ConvertArray(object arrayValue)
        {
            if (arrayValue is not IEnumerable enumerable)
                return arrayValue;

            var result = new List<object>();
            foreach (var item in enumerable)
            {
                result.Add(ConvertScalarValue(item));
            }

            // Convert to appropriate array type if all elements are the same type
            if (result.Count > 0)
            {
                var firstNonNull = result.FirstOrDefault(x => x != null);
                if (firstNonNull != null)
                {
                    var elementType = firstNonNull.GetType();
                    if (result.All(x => x == null || x.GetType() == elementType))
                    {
                        // All elements are the same type, convert to typed array
                        return ConvertToTypedArray(result, elementType);
                    }
                }
            }

            return result.ToArray();
        }

        /// <summary>
        /// Converts a list to a typed array
        /// </summary>
        private static Array ConvertToTypedArray(List<object?> list, Type elementType)
        {
            var array = Array.CreateInstance(elementType, list.Count);
            for (int i = 0; i < list.Count; i++)
            {
                array.SetValue(list[i], i);
            }
            return array;
        }

        /// <summary>
        /// Converts OPC UA ExtensionObject to a useful representation
        /// </summary>
        private static object? ConvertExtensionObject(ExtensionObject extensionObject)
        {
            if (extensionObject?.Body == null)
                return null;

            // If the body is already decoded, use it
            if (extensionObject.Body is IEncodeable encodeable)
            {
                return ConvertEncodeable(encodeable);
            }

            // If it's raw bytes, return as byte array
            if (extensionObject.Body is byte[] bytes)
            {
                return bytes;
            }

            // Fallback to string representation
            return extensionObject.ToString();
        }

        /// <summary>
        /// Converts OPC UA IEncodeable objects to dictionaries
        /// </summary>
        private static object? ConvertEncodeable(IEncodeable encodeable)
        {
            // For complex OPC UA types, we'll create a dictionary representation
            var result = new Dictionary<string, object?>();
            var type = encodeable.GetType();
            
            result["TypeName"] = type.Name;
            result["TypeId"] = encodeable.TypeId?.ToString();

            // Use reflection to get public properties
            var properties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(encodeable);
                    result[prop.Name] = ConvertScalarValue(value);
                }
                catch
                {
                    // Skip properties that can't be read
                    continue;
                }
            }

            return result;
        }

        /// <summary>
        /// Handles complex objects that don't fit other categories
        /// </summary>
        private static object? ConvertComplexObject(object value)
        {
            // If it's a simple type, return as-is
            var type = value.GetType();
            if (type.IsPrimitive || type == typeof(string) || type == typeof(decimal))
            {
                return value;
            }

            // If it implements IEnumerable (but isn't string), treat as array
            if (value is IEnumerable enumerable && value is not string)
            {
                return ConvertArray(value);
            }

            // For other complex objects, try to create a dictionary representation
            try
            {
                var result = new Dictionary<string, object?>();
                var properties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                
                foreach (var prop in properties)
                {
                    try
                    {
                        var propValue = prop.GetValue(value);
                        result[prop.Name] = ConvertScalarValue(propValue);
                    }
                    catch
                    {
                        // Skip properties that can't be read
                        continue;
                    }
                }

                return result.Count > 0 ? result : value.ToString();
            }
            catch
            {
                // Fallback to string representation
                return value.ToString();
            }
        }

        /// <summary>
        /// Creates a complete data structure from DataValue including metadata
        /// </summary>
        public static Dictionary<string, object?> ConvertDataValueWithMetadata(DataValue dataValue)
        {
            var result = new Dictionary<string, object?>();

            // Core value
            result["Value"] = ConvertDataValue(dataValue);
            
            // Status information
            result["StatusCode"] = dataValue.StatusCode.Code;
            result["StatusCodeName"] = dataValue.StatusCode.ToString();
            
            // Timestamps
            if (dataValue.SourceTimestamp != DateTime.MinValue)
                result["SourceTimestamp"] = dataValue.SourceTimestamp;
            
            if (dataValue.ServerTimestamp != DateTime.MinValue)
                result["ServerTimestamp"] = dataValue.ServerTimestamp;

            // Quality information
            if (dataValue.StatusCode.StructureChanged)
                result["StructureChanged"] = true;
            
            if (dataValue.StatusCode.SemanticsChanged)
                result["SemanticsChanged"] = true;

            return result;
        }
    }
}
