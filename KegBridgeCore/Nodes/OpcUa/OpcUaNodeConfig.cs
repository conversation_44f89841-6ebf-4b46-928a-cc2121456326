using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;

namespace KegBridgeCore.Nodes.OpcUa;

public class OpcUaNodeConfig : INodeTypeConfig
{
    [Required]
    public string ServerUrl { get; set; }
    public int PublishingInterval { get; set; } = 1000;
    public string ReadTopic { get; set; }
    public string NotifyTopic { get; set; }
    public string WriteTopic { get; set; }

    public class MonitoredItemConfig
    {
        public string NodeId { get; set; }
        public int SamplingInterval { get; set; } = 100;
        public uint QueueSize { get; set; } = 10;
        public string Topic { get; set; }
        public bool Enabled { get; set; } = true;
    }

    public List<MonitoredItemConfig> MonitoredItems = new List<MonitoredItemConfig>();

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }
}