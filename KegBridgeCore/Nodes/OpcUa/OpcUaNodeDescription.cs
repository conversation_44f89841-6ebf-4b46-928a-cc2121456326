using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.OpcUa;

public class OpcUaNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "opcua";
    public string Description { get; private set; } = "OpcUA node";
    public Type NodeConfigType { get; private set; } = typeof(OpcUaNodeConfig);
    public Type NodeType { get; private set; } = typeof(OpcUaNode);
    public Type NodeConfigComponent { get; private set; } = null;

}