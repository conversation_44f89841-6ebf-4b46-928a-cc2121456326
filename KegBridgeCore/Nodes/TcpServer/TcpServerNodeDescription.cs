using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.TcpServer;

public class TcpServerNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "tcp_server";
    public string Description { get; private set; } = "TCP server";
    public Type NodeConfigType { get; private set; } = typeof(TcpServerNodeConfig);
    public Type NodeType { get; private set; } = typeof(TcpServerNode);
    public Type NodeConfigComponent { get; private set; } = null;
    //public Type NodeConfigComponent { get; private set; } = typeof(DummyNodeConfigComponent);
}