using Akka.Actor;
using Akka.Event;
using Akka.IO;
using Akka.Logger.Serilog;
using Akka.Streams.Implementation.Fusing;
using DeepCopy;
using KegBridgeCore.Data;
using KegBridgeCore.Extensions;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using static Akka.IO.Inet.SO;

namespace KegBridgeCore.Nodes.TcpServer;

class TcpConnectionHandler : ReceiveActor
{
    private readonly ILoggingAdapter Logger;
    private readonly IActorRef Connection;
    private readonly EndPoint RemoteAddress;

    public TcpConnectionHandler(IActorRef connection, EndPoint remoteAddress) 
    {
        Connection = connection;
        RemoteAddress = remoteAddress;
        Logger = Context.GetLogger<SerilogLoggingAdapter>().ForContext("SourceContext",GetType().FullName + "-"+remoteAddress.ToString());

        Receive<Tcp.PeerClosed>(tcp_peer_closed => {
            Logger.Debug("Tcp.PeerClosed", tcp_peer_closed);
            Context.Stop(Self);
        });
        Receive<Tcp.Received>(tcp_received => {
            Logger.Debug("Tcp.Received {data} bytes", tcp_received.Data.Count);
            Context.Parent.Tell(tcp_received);
        });
    }
}

public class TcpServerNode : BaseNode
{
    private TcpServerNodeConfig Config { get; set; }
    private Dictionary<IActorRef,EndPoint> Connections { get; set; } = new ();
    private Dictionary<IActorRef,ByteString> ReceiveBuffers { get; set; } = new ();
    private ByteString StartByteString;
    private ByteString EndByteString;

    public TcpServerNode(Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
    }

    protected override bool Configure(object config)
    {
        Logger.Info("Configuring with config: {@config}", config);
        Config = config as TcpServerNodeConfig;
        return base.Configure(Config); 
    }

    protected override void Running()
    {
        Logger.Info(">>Running");

        Receive<Terminated>(term =>
        {
            Logger.Debug("Terminated");
            if (Connections.ContainsKey(term.ActorRef))
            {
                Logger.Debug("Connection {path} terminated", term.ActorRef.Path);
                Connections.Remove(term.ActorRef);
                ReceiveBuffers.Remove(term.ActorRef);
            }
        });
        Receive<Tcp.Bound>(tcp_bound  => {
            Logger.Debug("Tcp.Bound to {address}", tcp_bound.LocalAddress);
        });
        Receive<Tcp.Connected>(tcp_connected => {
            Logger.Debug("Tcp.Connected from {remote}", tcp_connected.RemoteAddress);

            var connection = Context.ActorOf(Props.Create(() => new TcpConnectionHandler(Sender, tcp_connected.RemoteAddress)),tcp_connected.RemoteAddress.ToString());

            Connections[connection] = tcp_connected.RemoteAddress;
            Context.Watch(connection);

            Sender.Tell(new Tcp.Register(connection));
        });
        Receive<Tcp.Received>(tcp_received => {
            Logger.Debug("Tcp.Received {data} bytes from {remote}", tcp_received.Data.Count, Connections[Sender]);
            if (Connections.ContainsKey(Sender))
            {
                var remoteAddress = Connections[Sender];
                if (!ReceiveBuffers.ContainsKey(Sender))
                {
                    ReceiveBuffers[Sender] = ByteString.Empty;
                }
                ReceiveBuffers[Sender] = ReceiveBuffers[Sender].Concat(tcp_received.Data);

                Logger.Debug("Tcp.Received buffer {data} bytes for {remote}", ReceiveBuffers[Sender].Count, Connections[Sender]);
                //Logger.Debug(ReceiveBuffers[Sender].ToHexRows());

                // extract frame between start and end marker
                while (ReceiveBuffers[Sender].TryExtractFrame(StartByteString, EndByteString, 0, includeDelimiters: true, out var frame, out var updatedReceiveBuffer))
                {
                    Logger.Debug("Tcp.Received extracted frame {frame} bytes for {remote}", 
                        frame.Count, 
                        Connections[Sender]);

                    var nodeEventData = NodeEventData.FromDictionary(new Dictionary<string, object?>
                    {
                        ["frame"] = frame.ToArray()
                    });
                    var nodeEvent = new Data.NodeEvent(NodeName, nodeEventData, "tcp");
                    PostNodeEvent(nodeEvent);
                    
                    ReceiveBuffers[Sender] = updatedReceiveBuffer;
                }

                Logger.Debug("Tcp.Received buffer keeps {data} bytes for {remote}", ReceiveBuffers[Sender].Count, Connections[Sender]);
            }
        });

        StartByteString = ByteString.FromBytes(Convert.FromHexString(Config.StartByteFrameMarker));
        EndByteString = ByteString.FromBytes(Convert.FromHexString(Config.EndByteFrameMarker));

        //var remoteEndpoint = IPEndPoint.Parse(Config.RemoteEndpoint);
        var localEndpoint = IPEndPoint.Parse(Config.LocalEndpoint);
        Context.System.Tcp().Tell(new Tcp.Bind(Self,localEndpoint));
    }

    protected override void PostStop()
    {
        base.PostStop();
    }

    public static int IndexOfSpan(byte[] buffer, byte[] pattern, int startIndex = 0)
    {
        if (buffer == null) throw new ArgumentNullException(nameof(buffer));
        if (pattern == null) throw new ArgumentNullException(nameof(pattern));
        if (startIndex < 0 || startIndex > buffer.Length) return -1;
        if (pattern.Length == 0) return startIndex;

        int rel = buffer.AsSpan(startIndex).IndexOf(pattern);
        return rel >= 0 ? startIndex + rel : -1;
    }
}