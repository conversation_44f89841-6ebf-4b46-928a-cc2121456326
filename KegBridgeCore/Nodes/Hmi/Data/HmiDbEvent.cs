using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Text;

namespace KegBridgeCore.Nodes.Hmi;

[Table("hmi_events")]
public class HmiDbEvent
{
    private string _digest = null;
    private DateTime? _created_at = null;

    [Column("id")]
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    [Column("created_at")]
    public DateTime? CreatedAt
    {
        get
        {
            if( _created_at == null )
            {
                _created_at = DateTime.UtcNow;
            }
            return (DateTime)_created_at;
        }
        set
        {
            _created_at = value;
        }
    }
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
    [Column("source")]
    public string Source { get; set; }
    [Column("digest")]
    public string Digest
    {
        get
        {
            if( _digest == null )
            {
                _digest = ComputeSHA256Hash(EventAt.ToString("o", System.Globalization.CultureInfo.InvariantCulture) + 
                                            Source +
                                            MsgText + 
                                            StateAfter.ToString() +
                                            MsgClass.ToString() + 
                                            MsgProc.ToString() +
                                            MsgNumber.ToString() ); 
            }
            return _digest;
        }
        set
        {
            _digest = value;
        }
    }
    [Column("event_at")]
    public DateTime EventAt{ get; set; }
    [Column("msg_proc")]
    public short MsgProc { get; set; }
    [Column("state_after")]
    public short StateAfter { get; set; }
    [Column("msg_class")]
    public short MsgClass { get; set; }
    [Column("msg_number")]
    public int MsgNumber { get; set; }
    [Column("var1")]
    public string Var1 { get; set; }
    [Column("var2")]
    public string Var2 { get; set; }
    [Column("var3")]
    public string Var3 { get; set; }
    [Column("var4")]
    public string Var4 { get; set; }
    [Column("var5")]
    public string Var5 { get; set; }
    [Column("var6")]
    public string Var6 { get; set; }
    [Column("var7")]
    public string Var7 { get; set; }
    [Column("var8")]
    public string Var8 { get; set; }
    [Column("msg_text")]
    public string MsgText { get; set; }

    [Column("event_on_at")]
    public DateTime? EventOnAt { get; set; }
    [Column("event_off_at")]
    public DateTime? EventOffAt { get; set; }
    [Column("other_event_id")]
    public int? OtherEventId { get; set; }

    public static string ComputeSHA256Hash(string text)
    {
        using (var sha256 = SHA256.Create())
        {
            return BitConverter.ToString(sha256.ComputeHash(Encoding.UTF8.GetBytes(text))).Replace("-", "");
        }
    }
        
}

// count unique message texts
// select count(*) as c ,msg_text from hmi_events group by msg_text order by msg_text ;

// count on off per message text
// select count(*) as c ,state_after,msg_text from hmi_events group by msg_text,state_after order by msg_text,state_after;

// count on off event that could not be matched
// select count(*) as c ,state_after,msg_text from hmi_events where (state_after=0 and event_on_at IS NULL) OR (state_after=1 and event_off_at IS NULL) group by msg_text,state_after order by msg_text,state_after;

// count on off per msg_proc
//select count(*),msg_proc,state_after from hmi_events group by msg_proc, state_after order by msg_proc, state_after;