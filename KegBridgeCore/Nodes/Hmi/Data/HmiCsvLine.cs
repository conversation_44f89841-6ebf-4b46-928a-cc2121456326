using CsvHelper.Configuration.Attributes;
using System;

namespace KegBridgeCore.Nodes.Hmi;
// Contents of the CSV is described here
//https://support.industry.siemens.com/cs/mdm/109747174?c=43358331019&lc=en-CZ

//0        ,1        ,2           ,3         ,4          ,5     ,6     ,7     ,8     ,9     ,10    ,11    ,12    ,13          ,14       ,15                           
//"Time_ms","MsgProc","StateAfter","MsgClass","MsgNumber","Var1","Var2","Var3","Var4","Var5","Var6","Var7","Var8","TimeString","MsgText","PLC"
//44048424479.7497,9,1,36,3458,"5",,,,,,"0401§A000201878701010200000004000201000004000201§Y0002000500020005","-2130706432","8/5/2020 10:11:15 AM","CM_DEPAL_KI020_M01 - Warning 05: Safe stop not released","HMI_Capper"
//44048424480.3701,9,0,36,3458,"5",,,,,,"0401§A000201868601010200000004000201000004000201§Y0002000500020005","-2130706432","8/5/2020 10:11:15 AM","CM_DEPAL_KI020_M01 - Warning 05: Safe stop not released","HMI_Capper"

public class HmiCsvLine
{
    private static readonly DateTime timeMsZeroDate = DateTime.Parse("1899-12-31T00:00:00");
    private double _time_ms;
    private DateTime _time;
    public double Time_ms
    {
        get
        {
            return _time_ms;
        }
        set
        {
            _time_ms = value;
            var date_time = _time_ms / 1000000;
            var days_since_1899 = Math.Floor(date_time);
            var time = date_time - days_since_1899;
            time = time * 24;
            var hours = Math.Floor(time);
            time = (time - hours) * 60;
            var mins = Math.Floor(time);
            time = (time - mins) * 60;
            var secs = Math.Floor(time);
            time = (time - secs) * 1000;
            var msecs = Math.Floor(time);
            //time = (time - msecs) * 100;
            //var ticks = (long)Math.Floor(time);

            // Siemens HMI have the same 'bug' as excel they treat 1900 as a leap year
            //  which means that all dates after 28/2/1900 are off by one day...
            // https://www.myonlinetraininghub.com/excel-date-and-time
            // https://stackoverflow.com/questions/727466/how-do-i-convert-an-excel-serial-date-number-to-a-net-datetime
            if (days_since_1899 > 59)
            {
                days_since_1899 -= 1;
            }

            // BIG FAT WARNING
            // THE HMI TIMEZONE and KEGBRIDGE TIMEZONE need to be aligned for this to work !!
            _time = timeMsZeroDate.AddDays(days_since_1899);
            _time = _time.AddHours(hours).AddMinutes(mins).AddSeconds(secs).AddMilliseconds(msecs);
            _time = DateTime.SpecifyKind(_time, DateTimeKind.Local);
        }
    }
    public DateTime TimeLocal { get { return _time; } }
    public DateTime TimeUTC { get { return _time.ToUniversalTime(); } }

    public short MsgProc { get; set; }
    public short StateAfter { get; set; }
    public short MsgClass { get; set; }
    public int MsgNumber { get; set; }
    public string Var1 { get; set; }
    public string Var2 { get; set; }
    public string Var3 { get; set; }
    public string Var4 { get; set; }
    public string Var5 { get; set; }
    public string Var6 { get; set; }
    public string Var7 { get; set; }
    public string Var8 { get; set; }
    public DateTime TimeString { get; set; }
    public string MsgText { get; set; }
    [Name("PLC")]
    public string Plc { get; set; }
}