using Akka.Actor;
using Akka.Event;
using CsvHelper;
using Dapper.FastCrud;
using Dapper.Logging;
using HtmlAgilityPack;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace KegBridgeCore.Nodes.Hmi;

public partial class HmiNode : BaseNode
{
    private HmiNodeConfig Config { get; set; }
    private HttpClientHandler httpClientHandler;
    private HttpClient httpClient;
    private ICancelable pollCancel;
    private IServiceScopeFactory serviceScopeFactory;
    #region Messages
    public class MsgGetAlarms
    {
    }
    public class MsgMatchAlarms
    {
    }
    public class MsgReadAlarms
    {
        public string Filename { get; private set; }
        public bool DeleteFile { get; private set; }
        public MsgReadAlarms(string filename, bool deleteFile = false)
        {
            Filename = filename;
            DeleteFile = deleteFile;
        }
    }
    public class MsgStartPoll
    {
        public int IntervalMs { get; set; }

        public MsgStartPoll(int intervalMs)
        {
            IntervalMs = intervalMs;
        }
    }
    public class MsgStopPoll
    {

    }
    #endregion

    public HmiNode(IServiceScopeFactory _serviceScopeFactory, Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
        serviceScopeFactory = _serviceScopeFactory;
        State.NodeContext.DownloadCount = 0;
        State.NodeContext.DownloadSize = 0;
        State.NodeContext.LoginCount = 0;
    }

    protected override bool Configure(object config)
    {
        if (base.Configure(config))
        {
            Config = config as HmiNodeConfig;
            if (Config != null)
            {
                Logger.Info("Config {@config}", Config);

                httpClientHandler = new HttpClientHandler();
                httpClientHandler.ServerCertificateCustomValidationCallback = (requestMsg, x509cert, x509chain, sslPolicy) =>
                {
                    return true;
                };
                httpClient = new HttpClient(httpClientHandler) { BaseAddress = new Uri(Config.Url) };

                return true;
            }
        }
        return false;
    }

    protected override void Running()
    {
        Logger.Debug(">>Running");

        Receive<MsgMatchAlarms>(MatchAlarms);
        Receive<MsgGetAlarms>(GetAlarms);
        Receive<MsgReadAlarms>(msg =>
        {
            ReadAlarmsCsv(msg);
        });
        Receive<MsgStartPoll>(m =>
        {
            Logger.Info("StartPoll {ms}ms", m.IntervalMs);

            if (pollCancel != null)
            {
                pollCancel.Cancel();
            }

            pollCancel = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                1000,
                m.IntervalMs,
                Self,
                new MsgGetAlarms(),
                Self);
        });
        Receive<MsgStopPoll>(m =>
        {
            pollCancel.Cancel();
            pollCancel = null;
        });
        // Initial read of the alarms
        if (Config.PollInterval > 0 && pollCancel == null )
        {
            Self.Tell(new MsgStartPoll(Config.PollInterval));
        }
    }

    protected void PendingDownload()
    {
        Receive<Status.Failure>(msg =>
        {
            var exception = msg.Cause;
            Logger.Error(msg.Cause, "Download failed {reason}", msg.Cause.Message);
            BecomeLogged(Running);
        });
        Receive<MsgReadAlarms>(msg =>
        {
            ReadAlarmsCsv(msg);
            BecomeLogged(Running);
        });
    }

    protected void GetAlarms(MsgGetAlarms msg)
    {
        BecomeLogged(PendingDownload);
        DownloadAlarmCsv(msg).ContinueWith( task =>
            {
                // if download succeeded then return the csv filename in a new message
                // to process
                return new MsgReadAlarms(task.Result, true);
                // exceptions will be catched by the Status.Failure message of PipeTo
            }, TaskContinuationOptions.ExecuteSynchronously)
            .PipeTo(Self);
    }

    protected void MatchAlarms(MsgMatchAlarms msg)
    {
        Logger.Info("Fix alarms");

        using (IServiceScope serviceScope = serviceScopeFactory.CreateScope())
        {
            var dbConnFactory = serviceScope.ServiceProvider.GetService<IDbConnectionFactory>();

            using (var connection = dbConnFactory.CreateConnection())
            {
                var latestEvents = connection.Find<HmiDbEvent>(sql => sql
                    .Where($"({nameof(HmiDbEvent.StateAfter):C} = 0) AND ({nameof(HmiDbEvent.OtherEventId):C} IS NULL)")
                    .OrderBy($"{nameof(HmiDbEvent.EventAt):C} DESC")
                );

                int matchedCount = 0, unmatchedCount = 0;
                foreach (var hmiEvent in latestEvents)
                {
                    if (hmiEvent.StateAfter == 0)
                    {
                        if (FindAndUpdateOnEvent(connection, hmiEvent))
                        {
                            matchedCount++;
                        }
                        else
                        {
                            unmatchedCount++;
                        }
                    }
                }
                Logger.Info("Matched {matched}, unmatched {unmatched}", matchedCount, unmatchedCount);

            }
        }
    }

    protected void ReadAlarmsCsv(MsgReadAlarms msg)
    {
        Logger.Info("Processing csv alarms from file {file}", msg.Filename);

        try
        {
            using (IServiceScope serviceScope = serviceScopeFactory.CreateScope())
            using (var reader = new StreamReader(msg.Filename))
            using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
            {
                var dbConnFactory = serviceScope.ServiceProvider.GetService<IDbConnectionFactory>();
                var alarms = csv.GetRecords<HmiCsvLine>();

                if (dbConnFactory != null)
                {
                    using (var connection = dbConnFactory.CreateConnection())
                    {
                        var latestEvents = connection.Find<HmiDbEvent>(sql => sql
                            .Where($"({nameof(HmiDbEvent.Source):C} = @Source)")
                            .OrderBy($"{nameof(HmiDbEvent.EventAt):C} DESC")
                            .Top(1)
                            .WithParameters(new
                            {
                                Source = alarms.First<HmiCsvLine>().Plc
                            })
                        );

                        DateTime? latestEventAt = null;
                        if (latestEvents.Count() > 0)
                        {
                            latestEventAt = latestEvents.First<HmiDbEvent>().EventAt;
                        }

                        int duplicateCount = 0, insertedCount = 0, skippedCount = 0;
                        int matchedCount = 0, unmatchedCount = 0;
                        foreach (var alarm in alarms)
                        {
                            if (latestEventAt != null)
                            {
                                TimeSpan delta = (TimeSpan)(latestEventAt - alarm.TimeUTC);
                                if (delta.TotalSeconds > 1)
                                {
                                    //Logger.Debug("Skipped latest {latest} {@alarm}", latestEventAt, alarm);
                                    skippedCount++;
                                    continue;
                                }
                            }

                            var hmiEvent = new HmiDbEvent();

                            hmiEvent.EventAt = alarm.TimeUTC;
                            hmiEvent.MsgText = alarm.MsgText;
                            hmiEvent.MsgProc = alarm.MsgProc;
                            hmiEvent.StateAfter = alarm.StateAfter;
                            hmiEvent.MsgClass = alarm.MsgClass;
                            hmiEvent.MsgNumber = alarm.MsgNumber;
                            hmiEvent.Source = alarm.Plc;
                            hmiEvent.Var1 = alarm.Var1;
                            hmiEvent.Var2 = alarm.Var2;
                            hmiEvent.Var3 = alarm.Var3;
                            hmiEvent.Var4 = alarm.Var4;
                            hmiEvent.Var5 = alarm.Var5;
                            hmiEvent.Var6 = alarm.Var6;
                            hmiEvent.Var7 = alarm.Var7;
                            hmiEvent.Var8 = alarm.Var8;

                            try
                            {
                                connection.Insert(hmiEvent);

                                latestEventAt = hmiEvent.EventAt;
                                insertedCount++;

                                // for alarms that are not present anymore search the event that
                                // started the alarm
                                if (Config.MatchOnOffEvents && hmiEvent.StateAfter == 0)
                                {
                                    if (FindAndUpdateOnEvent(connection, hmiEvent))
                                    {
                                        matchedCount++;
                                    }
                                    else
                                    {
                                        unmatchedCount++;
                                    }
                                }
                            }
                            catch (Npgsql.PostgresException ex) when (ex.ConstraintName == "hmi_events_digest")
                            {
                                // ignore duplicate messages
                                duplicateCount++;
                                //Logger.Debug("HMI event already added");
                            }
                        }

                        Logger.Info("Inserted {inserted} skipped {skipped} duplicate {duplicate} csv alarms from file {file}", insertedCount, skippedCount, duplicateCount, msg.Filename);
                        Logger.Info("Matched {matched}, unmatched {unmatched}", matchedCount, unmatchedCount);
                    }
                } 
                else
                {
                    Logger.Info("Download {alarm_count} alarms. No db connection available.", alarms.Count());
                }
            }
        }
        catch( System.IO.FileNotFoundException ex)
        {
            Logger.Error(ex,"File not found.");
        }
        catch( CsvHelper.TypeConversion.TypeConverterException ex)
        {
            Logger.Error(ex,"Error parsing CSV file");
        }
        finally
        {
            if (msg.DeleteFile)
            {
                try
                {
                    File.Delete(msg.Filename);
                    Logger.Info("Deleted file {filename}", msg.Filename);
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "Error deleting {filename}", msg.Filename);
                }
            }
        }
    }

    private bool FindAndUpdateOnEvent(DbConnection connection, HmiDbEvent hmiEvent)
    {
        // search the most recent on alarm for the same msgtext on the same plc
        // that is not taken yet
        var onEvents = connection.Find<HmiDbEvent>(sql => sql
            .Where($"({nameof(HmiDbEvent.Source):C} = @Source) AND ({nameof(HmiDbEvent.EventAt):C} < @EventAt) AND ({nameof(HmiDbEvent.OtherEventId):C} IS NULL) AND ({nameof(HmiDbEvent.StateAfter):C} = 1) AND ({nameof(HmiDbEvent.MsgText):C} = @MsgText)")
            .OrderBy($"{nameof(HmiDbEvent.EventAt):C} DESC")
            .Top(1)
            .WithParameters(new
            {
                Source = hmiEvent.Source,
                EventAt = hmiEvent.EventAt,
                MsgText = hmiEvent.MsgText
            })
        );

        if (onEvents.Count() == 1)
        {
            var onEvent = onEvents.First<HmiDbEvent>();

            Logger.Debug("Other event for {hmi_event_id} => {on_event_id}", hmiEvent.Id, onEvent.Id);

            if (onEvent != null)
            {
                hmiEvent.EventOffAt = hmiEvent.EventAt;
                hmiEvent.EventOnAt = onEvent.EventAt;
                hmiEvent.OtherEventId = onEvent.Id;
                hmiEvent.UpdatedAt = DateTime.UtcNow;

                onEvent.EventOnAt = onEvent.EventAt;
                onEvent.EventOffAt = hmiEvent.EventAt;
                onEvent.OtherEventId = hmiEvent.Id;
                onEvent.UpdatedAt = DateTime.UtcNow;

                connection.Update<HmiDbEvent>(hmiEvent);
                connection.Update<HmiDbEvent>(onEvent);
            }
            return true;
        }
        return false;
    }

    protected async Task<string> DownloadAlarmCsv(MsgGetAlarms msg)
    {            
        var response = await httpClient.GetAsync(Config.LogfilePath);

        response.EnsureSuccessStatusCode();

        if( response.RequestMessage.RequestUri.PathAndQuery != Config.LogfilePath)
        {
            Logger.Debug("Requested {requestPath} got {responsePath}", Config.LogfilePath, response.RequestMessage.RequestUri.PathAndQuery);
            Logger.Info("Authenticate to HMI Miniweb");

            var rootContent = await response.Content.ReadAsStringAsync();

            Logger.Debug("Authenticate login page {content}", rootContent);

            var rootHtmlDoc = new HtmlDocument();
            rootHtmlDoc.LoadHtml(rootContent);

            var loginForm = rootHtmlDoc.DocumentNode.SelectSingleNode("//form[@name='LoginForm']");
            if (loginForm != null)
            {
                var loginAction = loginForm.Attributes["action"].Value;
                var loginMethod = loginForm.Attributes["method"].Value;

                var loginParams = new Dictionary<string, string>();
                foreach (var input in loginForm.SelectNodes("//input"))
                {
                    if (input.Attributes["name"] != null)
                    {
                        loginParams.Add(input.Attributes["name"].Value, input.Attributes["value"]?.Value);
                    }
                }

                loginParams["Login"] = Config.Username;
                loginParams["Password"] = Config.Password;
                loginParams["Redirection"] = Config.LogfilePath;

                var loginResponse = await httpClient.PostAsync(loginAction, new FormUrlEncodedContent(loginParams));

                loginResponse.EnsureSuccessStatusCode();
                var loginContent = await loginResponse.Content.ReadAsStringAsync();

                Logger.Debug("Authenticate login post response {content}", loginContent);


                // Miniweb is not very clear in wether authentication succeeded.
                // it will always render the follwing page in response to the POST
                // wanting to redirect to the original request page via a meta http-equiv header
                // since in the early days of web it was not allowed to respond to a post with a 302 status

                // loginContent will be something like this with >>>>>URL<<<<<< replaced by the original request we made:
                //<HTML>
                //  <HEAD>
                //        <TITLE>Auth Form Response</TITLE>
                //           < META http-equiv='refresh' content='0;url= >>>>>URL<<<<<< '>
                //                < META http - equiv = 'content-type' content = 'text/html; charset=UTF-8' >
                //                     < META http - equiv = 'expires' content = '-1' >
                //                          < META http - equiv = 'pragma' content = 'no-cache' >
                //                               < META http - equiv = 'cache-control' content = 'no-cache' >
                //                                </ HEAD >
                //                                < BODY BGCOLOR = "#E9E9FC" >
                //                                     < A id = 'trap' href = ' >>>>>URL<<<<<< ' > Click Here </ A > if you are not automatically redirected.
                //        < BR >< BR >
                //        < A href = '/control.html' > Back to control page </ A >
                //             < SCRIPT >
                //                 < !--The trap usually triggers when the download box pops up. -- >
                //                 trap.focus();
                //                trap.onblur = function()
                //            {
                //                    if (trap.href.indexOf("?DownloadRecipe=") != -1)
                //                        window.location.href = "/ExportRecipes.html";
                //                    else if (trap.href.indexOf("/Rt/FwxPath/pdata.pwl") != -1)
                //                        window.location.href = "/control.html";
                //                }
                //        </ SCRIPT >
                //    </ BODY >
                //</ HTML >

                // so now we try to fetch the logile_path again
                // if auth succeeded it will work ; otherwise we get the login page again
                response = await httpClient.GetAsync(Config.LogfilePath);

                response.EnsureSuccessStatusCode();

            }
            else
            {
                Logger.Info("Unable to Authenticate. LoginForm not found on page.");
            }

        }

        if (response.RequestMessage.RequestUri.PathAndQuery == Config.LogfilePath)
        {
            Logger.Info("Authenticated. Download file");

            using (FileStream fs = File.Create(Path.GetTempFileName()))
            {
                await response.Content.CopyToAsync(fs);
                Logger.Info("Downloaded to {fname}", fs.Name);
                return fs.Name;
            }
                
        }
        else
        {
            Logger.Info("Authentication to HMI Miniweb failed.");

            var content = await response.Content.ReadAsStringAsync();
            Logger.Debug("Download page request {content}", content);

            throw new Exception($"Download of {Config.LogfilePath} failed.");
        }
    }
}