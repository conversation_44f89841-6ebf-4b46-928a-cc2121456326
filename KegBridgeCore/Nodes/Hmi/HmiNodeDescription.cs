using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.Hmi;

public class HmiNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "hmi";
    public string Description { get; private set; } = "Hmi node";
    public Type NodeConfigType { get; private set; } = typeof(HmiNodeConfig);
    public Type NodeType { get; private set; } = typeof(HmiNode);
    public Type NodeConfigComponent { get; private set; } = null;
}