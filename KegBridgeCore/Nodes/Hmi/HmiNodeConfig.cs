using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;

namespace KegBridgeCore.Nodes.Hmi;

public class HmiNodeConfig : INodeTypeConfig
{
    [Required]
    [MinLength(10)]
    public string Url { get; set; }
    [Required]
    public string LogfilePath { get; set; }
    public int PollInterval { get; set; } = 15000;
    public bool MatchOnOffEvents { get; set; } = true;
    public string Username { get; set; }
    public string Password { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }
}