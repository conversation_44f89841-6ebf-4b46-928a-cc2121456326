
using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;

using Akka.Actor;
using InfluxDB.Client;
using InfluxDB.Client.Writes;
using InfluxDB.Client.Api.Domain;
using Akka.Event;

namespace KegBridgeCore.Nodes.Influx;

public class InfluxNode : BaseNode
{
    private InfluxNodeConfig Config { get; set; }
    private InfluxDBClient influxClient { get; set; }
    private WriteApi influxWriteApi { get; set; }
    private IDictionary<string, int> MeasurementStats { get; set; } = new Dictionary<string, int>();

    public InfluxNode(Guid id, string name) : base(id,name)
    {
        State.NodeContext.WriteSuccess = 0;
        State.NodeContext.WriteError = 0;
        State.NodeContext.WriteRetry = 0;
        State.NodeContext.BatchWriteSuccess = 0;
        State.NodeContext.BatchWriteError = 0;
        State.NodeContext.BatchWriteRetry = 0;
        State.NodeContext.Measurements = MeasurementStats;
    }

    protected override bool Configure(object config)
    {
        Config = config as InfluxNodeConfig;
        if (Config != null)
        {
            Logger.Info("Config {@config}", Config);

            return true;
        }
        return false;
    }

    protected override void Running()
    {
        UnsubscribeAll();
        foreach (var topic in Config.Topics)
        {
            Subscribe(topic);
        }

        if (Config.Token != null)
        {

            var influxDBClientOptions = new InfluxDBClientOptions(Config.Url);
            influxDBClientOptions.Token = Config.Token;
            influxDBClientOptions.Bucket = Config.Bucket;
            influxDBClientOptions.Org = Config.Org;

            influxClient = new InfluxDBClient(influxDBClientOptions);
        }
        else if( Config.Database != null )
        {
            influxClient = new InfluxDBClient(Config.Url,"username","password",Config.Database,Config.Retention);
        }
        else
        {

        }

        var writeOptions = new WriteOptions();
        writeOptions.BatchSize = Config.BatchSize;
        writeOptions.FlushInterval = Config.FlushInterval;
            
        influxWriteApi = influxClient.GetWriteApi(writeOptions);

        IActorRef zelf = Self;

        influxWriteApi.EventHandler += (sender, eventArgs) =>
        {
            if( eventArgs is WriteSuccessEvent @success)
            {
                State.NodeContext.BatchWriteSuccess++;                   
                State.NodeContext.WriteSuccess += @success.LineProtocol.Count(c => (c == '\n')) + 1;
                Logger.Debug("Write success {@success}", success);
                return;
            }
            if (eventArgs is WriteErrorEvent @error)
            {
                State.NodeContext.BatchWriteError++;
                State.NodeContext.WriteError += @error.LineProtocol.Count(c => (c == '\n')) + 1;
                Logger.Error("Write error {@error}", error);

                //zelf.Tell(new MsgNodeError(error.Exception));

                return;
            }
            if (eventArgs is WriteRetriableErrorEvent @retry)
            {
                State.NodeContext.BatchWriteRetry++;
                State.NodeContext.WriteRetry += @retry.LineProtocol.Count(c => (c == '\n')) + 1;
                Logger.Warning("Write retry {@retry}", retry);
                return;
            }
        };

        Receive<Data.NodeEvent>(nodeEvent =>
        {
            LogNodeEventIn(nodeEvent);

            StoreNodeEvent(nodeEvent);
        });
    }

    private void StoreNodeEvent(Data.NodeEvent nodeEvent)
    {
        var data = nodeEvent.Data;

        if (data != null && data.ContainsKey("measurement") && data.ContainsKey("fields"))
        {
            string measurement = (string)data["measurement"];

            var _fields = (IDictionary<string, object>)data["fields"];
            bool hasAtLeastOneField = false;

            PointData pointData = PointData.Measurement(measurement);

            pointData = pointData.Timestamp(nodeEvent.Timestamp, WritePrecision.Ns);

            foreach (var field in _fields)
            {
                dynamic value = field.Value;
                    
                if (value is Microsoft.ClearScript.Undefined )
                    continue;

                if( value is String )
                {
                    var str_value = (string)value;
                    if( str_value.EndsWith("f") )
                    {
                        try
                        {
                            value = Convert.ToSingle(str_value.Replace("f", ""), CultureInfo.InvariantCulture);
                        }
                        catch(System.FormatException ex)
                        {
                            Logger.Warning(ex, "measurement {measurement} field {field} with value {value} ignored", measurement, field, str_value);
                            continue;
                        }
                    }
                }

                if (Config.ForceFloatFields != null && Config.ForceFloatFields.Contains(field.Key))
                {
                    value = Convert.ToSingle(value);
                }

                pointData = pointData.Field(field.Key, value);
                hasAtLeastOneField = true;
            }

            if (hasAtLeastOneField)
            {
                if (data.ContainsKey("tags"))
                {
                    var _tags = (IDictionary<string, object>)data["tags"];
                    foreach (var tag in _tags)
                    {
                        pointData = pointData.Tag(tag.Key, tag.Value.ToString());
                    }
                }

                if (!MeasurementStats.ContainsKey(measurement))
                {
                    MeasurementStats.Add(measurement, 0);
                }

                MeasurementStats[measurement]++;

                Logger.Debug("PointData {pointData}", pointData.ToLineProtocol());

                influxWriteApi.WritePoint(pointData);
            }
            else
            {
                Logger.Debug("NodeEvent contains no fields");
            }
        }
        else
        {
            Logger.Debug("NodeEvent without measurement or fields");
        }
    }

    protected override void PostStop()
    {
        Logger.Info("PostStop");
        if( influxWriteApi != null )
        {
            influxWriteApi.Dispose();
            Logger.Debug("influxWriteApi Disposed");

        }
        if ( influxClient != null )
        {
            influxClient.Dispose();
            Logger.Debug("influxClient Disposed");
        }
        base.PostStop();
    }

}