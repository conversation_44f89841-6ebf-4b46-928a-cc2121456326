using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using KegBridgeCore.Actors.Interfaces;

namespace KegBridgeCore.Nodes.Influx;

public class InfluxNodeConfig : INodeTypeConfig
{
    [Required]
    public string Url { get; set; }

    // for V2
    public string Token { get; set; }
    public string Bucket { get; set; }
    public string Org { get; set; }

    // for V1.8
    public string Database { get; set; }
    public string Retention { get; set; }

    public List<string> Topics { get; set; } = new List<string>();
    public List<string> ForceFloatFields { get; set; } = new List<string>();
    public int BatchSize { get; set; } = 1000;
    public int FlushInterval { get; set; } = 1000;
    public int RetryInterval { get; set; } = 1000;

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        return results;
    }
}