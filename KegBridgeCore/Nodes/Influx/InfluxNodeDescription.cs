using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.Influx;

public class InfluxNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "influx";
    public string Description { get; private set; } = "Influx node";
    public Type NodeConfigType { get; private set; } = typeof(InfluxNodeConfig);
    public Type NodeType { get; private set; } = typeof(InfluxNode);
    public Type NodeConfigComponent { get; private set; } = null;

}