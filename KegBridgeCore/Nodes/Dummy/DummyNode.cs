using Akka.Actor;
using Akka.Event;
using DeepCopy;
using KegBridgeCore.Data;
using System;
using System.Collections.Generic;
using System.Dynamic;

namespace KegBridgeCore.Nodes.Dummy;

public class DummyNode : BaseNode
{
    private record MsgProduceEvent();

    private DummyNodeConfig Config { get; set; }
    private ICancelable produceCancel;
    private static readonly Random random = new Random();

    public DummyNode(Guid nodeId, string nodeName) : base(nodeId, nodeName)
    {
    }

    protected override bool Configure(object config)
    {
        Logger.Info("Configuring DummyNode with config: {@config}", config);
        Config = config as DummyNodeConfig;
        return base.Configure(Config); 
    }

    protected override void Running()
    {
        Logger.Info(">>Running");
        if ( Config.ProduceEventsIntervalMs > 0  )
        {
            produceCancel = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                1000,
                Config.ProduceEventsIntervalMs,
                Self,
                new MsgProduceEvent(),
                Self);

        }
        Receive<MsgProduceEvent>(ProduceNodeEvent);
        Receive<NodeEvent>(ProcessNodeEvent);
    }

    protected void ProcessNodeEvent(NodeEvent nodeEvent)
    {
        LogNodeEventIn(nodeEvent);

        if( Config.ProcessEvents )
        {
            var newNodeEvent = nodeEvent.With(Config.Identifier, $"processed at {DateTime.UtcNow}");
            PostNodeEvent(newNodeEvent);
        }
    }

    private void ProduceNodeEvent(MsgProduceEvent msg)
    {
        dynamic data = new ExpandoObject();
        var _data = data as IDictionary<string, object>;

        _data[Config.Identifier]= $"produced {DateTime.UtcNow}";
        _data["random_value"] = random.NextDouble();

        var nodeEvent = new Data.NodeEvent(NodeName, null, data);
        PostNodeEvent(nodeEvent);
    }

    private void StopProduce()
    {
        if (produceCancel != null)
        {
            produceCancel.Cancel();
            produceCancel = null;
        }
    }

    protected override void PostStop()
    {
        StopProduce();
        base.PostStop();
    }

}