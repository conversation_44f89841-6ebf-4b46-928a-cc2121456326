using KegBridgeCore.Services;
using System;

namespace KegBridgeCore.Nodes.Dummy;

public class DummyNodeDescription : INodeDescription
{
    public string TypeName { get; private set; } = "dummy";
    public string Description { get; private set; } = "Dummy node";
    public Type NodeConfigType { get; private set; } = typeof(DummyNodeConfig);
    public Type NodeType { get; private set; } = typeof(DummyNode);
    public Type NodeConfigComponent { get; private set; } = null;
    //public Type NodeConfigComponent { get; private set; } = typeof(DummyNodeConfigComponent);
}