using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Dynamic;
using System.Linq;

namespace KegBridgeCore.Data;

/// <summary>
/// Strongly-typed immutable wrapper around the event payload.
/// Guarantees immutability of nested structures and exposes a read-only
/// dictionary interface for ergonomic consumption.
/// </summary>
public sealed record NodeEventData(ImmutableDictionary<string, object?> Items)
    : IReadOnlyDictionary<string, object?>
{
    public static readonly NodeEventData Empty = new(ImmutableDictionary<string, object?>.Empty);

    public static NodeEventData FromExpando(ExpandoObject expando)
    {
        var dict = (IDictionary<string, object?>)expando;
        var immutable = dict.ToImmutableDictionary(kv => kv.Key, kv => DeepFreeze(kv.Value));
        return new NodeEventData(immutable);
    }

    public static NodeEventData FromDictionary(IDictionary<string, object?> dict)
    {
        var immutable = dict.ToImmutableDictionary(kv => kv.Key, kv => DeepFreeze(kv.Value));
        return new NodeEventData(immutable);
    }

    public static NodeEventData FromPairs(IEnumerable<KeyValuePair<string, object?>> pairs)
    {
        var immutable = pairs.ToImmutableDictionary(kv => kv.Key, kv => DeepFreeze(kv.Value));
        return new NodeEventData(immutable);
    }

    public NodeEventData With(string key, object? value)
        => this with { Items = Items.SetItem(key, DeepFreeze(value)) };

    public NodeEventData WithRange(IEnumerable<KeyValuePair<string, object?>> items)
        => this with { Items = Items.SetItems(items.Select(kv => new KeyValuePair<string, object?>(kv.Key, DeepFreeze(kv.Value)))) };

    /// <summary>
    /// Return a copy without the specified key.
    /// </summary>
    public NodeEventData Without(string key)
        => this with { Items = Items.Remove(key) };
    public NodeEventData WithoutRange(IEnumerable<string> items)
        => this with { Items = Items.RemoveRange(items) };

    public bool TryGet<T>(string key, out T? value)
    {
        if (Items.TryGetValue(key, out var raw) && raw is T t)
        {
            value = t;
            return true;
        }
        value = default;
        return false;
    }

    /// <summary>
    /// Gets a value using dot notation path (e.g., "user.profile.name").
    /// Returns null if any part of the path doesn't exist.
    /// </summary>
    public object? GetPath(string path)
    {
        if (string.IsNullOrEmpty(path))
            return null;

        return TryGetPath(path, out var value) ? value : null;
    }

    /// <summary>
    /// Tries to get a value using dot notation path with type conversion.
    /// </summary>
    public bool TryGetPath<T>(string path, out T? value)
    {
        if (TryGetPath(path, out var raw) && raw is T t)
        {
            value = t;
            return true;
        }
        value = default;
        return false;
    }

    /// <summary>
    /// Tries to get a value using dot notation path.
    /// </summary>
    public bool TryGetPath(string path, out object? value)
    {
        value = null;

        if (string.IsNullOrEmpty(path))
            return false;

        var parts = path.Split('.');
        object? current = this;

        foreach (var part in parts)
        {
            if (string.IsNullOrEmpty(part))
                return false;

            current = NavigateToProperty(current, part);
            if (current == null)
                return false;
        }

        value = current;
        return true;
    }

    /// <summary>
    /// Sets a value using dot notation path, creating intermediate objects as needed.
    /// Returns a new NodeEventData instance.
    /// </summary>
    public NodeEventData SetPath(string path, object? value)
    {
        if (string.IsNullOrEmpty(path))
            throw new ArgumentException("Path cannot be null or empty", nameof(path));

        var parts = path.Split('.');
        if (parts.Length == 1)
        {
            return With(parts[0], value);
        }

        // For nested paths, we need to reconstruct the nested structure
        var newItems = Items.ToBuilder();
        SetNestedPath(newItems, parts, 0, value);
        return this with { Items = newItems.ToImmutable() };
    }

    /// <summary>
    /// Sets multiple values using dot notation paths in a single operation.
    /// More efficient than chaining multiple SetPath calls.
    /// Returns a new NodeEventData instance.
    /// </summary>
    public NodeEventData SetPaths(IEnumerable<KeyValuePair<string, object?>> pathValues)
    {
        if (pathValues == null)
            throw new ArgumentNullException(nameof(pathValues));

        var newItems = Items.ToBuilder();

        foreach (var pathValue in pathValues)
        {
            if (string.IsNullOrEmpty(pathValue.Key))
                throw new ArgumentException("Path cannot be null or empty", nameof(pathValues));

            var parts = pathValue.Key.Split('.');
            if (parts.Length == 1)
            {
                newItems[parts[0]] = DeepFreeze(pathValue.Value);
            }
            else
            {
                SetNestedPath(newItems, parts, 0, pathValue.Value);
            }
        }

        return this with { Items = newItems.ToImmutable() };
    }

    /// <summary>
    /// Sets multiple values using dot notation paths in a single operation.
    /// Convenience overload that accepts an anonymous object.
    /// Returns a new NodeEventData instance.
    /// </summary>
    public NodeEventData SetPaths(object pathValues)
    {
        if (pathValues == null)
            throw new ArgumentNullException(nameof(pathValues));

        var properties = pathValues.GetType().GetProperties();
        var pairs = properties.Select(prop => new KeyValuePair<string, object?>(
            prop.Name,
            prop.GetValue(pathValues)
        ));

        return SetPaths(pairs);
    }

    /// <summary>
    /// Deletes a value at the specified dot notation path.
    /// Returns a new NodeEventData instance.
    /// </summary>
    /// <param name="path">The dot notation path to delete (e.g., "user.profile.name")</param>
    /// <param name="allowNonLeafDeletion">If true, allows deletion of non-leaf nodes (nodes with children). If false, only leaf nodes can be deleted.</param>
    /// <returns>A new NodeEventData instance with the path removed</returns>
    /// <exception cref="ArgumentException">Thrown when path is null/empty or when trying to delete a non-leaf node without allowNonLeafDeletion</exception>
    /// <exception cref="InvalidOperationException">Thrown when the path doesn't exist</exception>
    public NodeEventData DeletePath(string path, bool allowNonLeafDeletion = false)
    {
        if (string.IsNullOrEmpty(path))
            throw new ArgumentException("Path cannot be null or empty", nameof(path));

        var parts = path.Split('.');

        // Check if path exists
        if (!TryGetPath(path, out var existingValue))
            throw new InvalidOperationException($"Path '{path}' does not exist");

        // Check if it's a leaf node (unless non-leaf deletion is allowed)
        if (!allowNonLeafDeletion && IsNonLeafNode(existingValue))
            throw new ArgumentException($"Path '{path}' is not a leaf node. Use allowNonLeafDeletion=true to delete non-leaf nodes.", nameof(path));

        if (parts.Length == 1)
        {
            // Simple case: delete from root level
            return Without(parts[0]);
        }

        // Complex case: delete from nested structure
        var newItems = Items.ToBuilder();
        DeleteNestedPath(newItems, parts, 0);
        return this with { Items = newItems.ToImmutable() };
    }

    /// <summary>
    /// Deletes multiple paths in a single operation.
    /// More efficient than chaining multiple DeletePath calls.
    /// Returns a new NodeEventData instance.
    /// </summary>
    /// <param name="paths">The paths to delete</param>
    /// <param name="allowNonLeafDeletion">If true, allows deletion of non-leaf nodes</param>
    /// <returns>A new NodeEventData instance with the paths removed</returns>
    public NodeEventData DeletePaths(IEnumerable<string> paths, bool allowNonLeafDeletion = false)
    {
        if (paths == null)
            throw new ArgumentNullException(nameof(paths));

        var pathList = paths.ToList();
        if (pathList.Count == 0)
            return this;

        // Validate all paths first
        foreach (var path in pathList)
        {
            if (string.IsNullOrEmpty(path))
                throw new ArgumentException("Path cannot be null or empty", nameof(paths));

            if (!TryGetPath(path, out var existingValue))
                throw new InvalidOperationException($"Path '{path}' does not exist");

            if (!allowNonLeafDeletion && IsNonLeafNode(existingValue))
                throw new ArgumentException($"Path '{path}' is not a leaf node. Use allowNonLeafDeletion=true to delete non-leaf nodes.", nameof(paths));
        }

        // Sort paths by depth (deepest first) to avoid deleting parent before child
        var sortedPaths = pathList.OrderByDescending(p => p.Split('.').Length).ToList();

        var newItems = Items.ToBuilder();
        foreach (var path in sortedPaths)
        {
            var parts = path.Split('.');
            if (parts.Length == 1)
            {
                newItems.Remove(parts[0]);
            }
            else
            {
                DeleteNestedPath(newItems, parts, 0);
            }
        }

        return this with { Items = newItems.ToImmutable() };
    }

    private static bool IsNonLeafNode(object? value)
    {
        return value switch
        {
            null => false,
            ImmutableDictionary<string, object?> dict => dict.Count > 0,
            IDictionary<string, object?> dict => dict.Count > 0,
            IDictionary dict => dict.Count > 0,
            NodeEventData data => data.Count > 0,
            _ => false
        };
    }

    private static void DeleteNestedPath(ImmutableDictionary<string, object?>.Builder items, string[] parts, int index)
    {
        var key = parts[index];

        if (index == parts.Length - 1)
        {
            // This is the final part - delete it
            if (items.ContainsKey(key))
            {
                items.Remove(key);
            }
            return;
        }

        // Navigate deeper
        if (!items.TryGetValue(key, out var current) || current is not ImmutableDictionary<string, object?> existingDict)
        {
            // Path doesn't exist at this level
            return;
        }

        var nestedBuilder = existingDict.ToBuilder();
        DeleteNestedPath(nestedBuilder, parts, index + 1);

        // If the nested dictionary becomes empty after deletion, we might want to remove it too
        var updatedDict = nestedBuilder.ToImmutable();
        if (updatedDict.Count == 0)
        {
            items.Remove(key);
        }
        else
        {
            items[key] = updatedDict;
        }
    }

    private static void SetNestedPath(ImmutableDictionary<string, object?>.Builder items, string[] parts, int index, object? value)
    {
        var key = parts[index];

        if (index == parts.Length - 1)
        {
            // Last part - set the value
            items[key] = DeepFreeze(value);
            return;
        }

        // Get or create intermediate dictionary
        if (!items.TryGetValue(key, out var existing) || existing is not ImmutableDictionary<string, object?> existingDict)
        {
            existingDict = ImmutableDictionary<string, object?>.Empty;
        }

        var nestedBuilder = existingDict.ToBuilder();
        SetNestedPath(nestedBuilder, parts, index + 1, value);
        items[key] = nestedBuilder.ToImmutable();
    }

    private static object? NavigateToProperty(object? current, string propertyName)
    {
        return current switch
        {
            null => null,
            NodeEventData data => data.Items.TryGetValue(propertyName, out var value) ? value : null,
            ImmutableDictionary<string, object?> dict => dict.TryGetValue(propertyName, out var value) ? value : null,
            IDictionary<string, object?> dict => dict.TryGetValue(propertyName, out var value) ? value : null,
            IDictionary dict => dict.Contains(propertyName) ? dict[propertyName] : null,
            _ => GetPropertyViaReflection(current, propertyName)
        };
    }

    private static object? GetPropertyViaReflection(object obj, string propertyName)
    {
        var type = obj.GetType();
        var property = type.GetProperty(propertyName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
        if (property != null && property.CanRead)
        {
            return property.GetValue(obj);
        }

        var field = type.GetField(propertyName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
        if (field != null)
        {
            return field.GetValue(obj);
        }

        return null;
    }

    // --- IReadOnlyDictionary implementation ---
    public int Count => Items.Count;
    public IEnumerable<string> Keys => Items.Keys;
    public IEnumerable<object?> Values => Items.Values;
    public object? this[string key] => Items[key];
    public bool ContainsKey(string key) => Items.ContainsKey(key);
    public bool TryGetValue(string key, out object? value) => Items.TryGetValue(key, out value);
    public IEnumerator<KeyValuePair<string, object?>> GetEnumerator() => Items.GetEnumerator();
    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    private static object? DeepFreeze(object? value)
    {
        if (value is null) return null;
        if (value is string) return value;

        switch (value)
        {
            case ExpandoObject eo:
                return ((IDictionary<string, object?>)eo)
                    .ToImmutableDictionary(kv => kv.Key, kv => DeepFreeze(kv.Value));

            case IDictionary<string, object?> dictObj:
                return dictObj.ToImmutableDictionary(kv => kv.Key, kv => DeepFreeze(kv.Value));

            case IDictionary dictNonGeneric:
                return dictNonGeneric.Cast<DictionaryEntry>()
                    .ToImmutableDictionary(de => Convert.ToString(de.Key)!, de => DeepFreeze(de.Value));

            case IEnumerable enumerable:
                var builder = ImmutableArray.CreateBuilder<object?>();
                foreach (var item in enumerable)
                    builder.Add(DeepFreeze(item));
                return builder.ToImmutable();

            default:
                return value;
        }
    }
}

/// <summary>
/// Immutable event message passed between NodeActors.
/// Contains:
/// - Id: internal stable unique id for the event (always auto-generated)
/// - CreatedAt: when this NodeEvent object was constructed
/// - Timestamp: domain timestamp (can be provided externally)
/// - Node: the name of the node that generated this message (required)
/// - Label: optional label associated with the message
/// - Data: immutable NodeEventData payload
/// </summary>
public sealed record NodeEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTimeOffset CreatedAt { get; }
    public DateTimeOffset Timestamp { get; }
    public string Node { get; }
    public string?  Topic { get; }
    public NodeEventData Data { get; }

    public NodeEvent(string node, NodeEventData data, string? topic = null, DateTimeOffset? timestamp = null)
    {
        if (string.IsNullOrWhiteSpace(node))
            throw new ArgumentException("Node must be a non-empty string", nameof(node));

        Node = node;
        Topic = topic;
        Timestamp = timestamp ?? DateTimeOffset.UtcNow;
        CreatedAt = DateTimeOffset.UtcNow; 
        Data = data ?? NodeEventData.Empty;
    }

    public static NodeEvent FromExpando(string node, ExpandoObject expando, string? topic = null, DateTimeOffset? timestamp = null)
        => new(node, NodeEventData.FromExpando(expando), topic, timestamp);

    public static NodeEvent FromDictionary(string node, IDictionary<string, object?> dict, string? topic = null, DateTimeOffset? timestamp = null)
        => new(node, NodeEventData.FromDictionary(dict), topic, timestamp);

    public NodeEvent With(string key, object? value)
        => new(Node, Data.With(key, value), Topic, Timestamp );

    public NodeEvent WithRange(IEnumerable<KeyValuePair<string, object?>> items)
        => new(Node, Data.WithRange(items), Topic, Timestamp);

    public NodeEvent Without(string key)
        => new(Node, Data.Without(key), Topic, Timestamp);

    public NodeEvent WithoutRange(IEnumerable<string> keys)
        => new(Node, Data.WithoutRange(keys), Topic, Timestamp);

    public NodeEvent WithTopic(string? topic)
        => new(Node, Data, topic, Timestamp);
    public NodeEvent WithTimestamp(DateTimeOffset timestamp)
        => new(Node, Data, Topic, timestamp);

    public NodeEvent WithNode(string node)
    {
        if (string.IsNullOrWhiteSpace(node))
            throw new ArgumentException("Node must be a non-empty string", nameof(node));
        return new(node, Data, Topic, Timestamp);
    }

    public bool TryGet<T>(string key, out T? value) => Data.TryGet(key, out value);

    /// <summary>
    /// Gets a value using dot notation path (e.g., "user.profile.name").
    /// Returns null if any part of the path doesn't exist.
    /// </summary>
    public object? GetPath(string path) => Data.GetPath(path);

    /// <summary>
    /// Tries to get a value using dot notation path with type conversion.
    /// </summary>
    public bool TryGetPath<T>(string path, out T? value) => Data.TryGetPath(path, out value);

    /// <summary>
    /// Tries to get a value using dot notation path.
    /// </summary>
    public bool TryGetPath(string path, out object? value) => Data.TryGetPath(path, out value);

    /// <summary>
    /// Sets a value using dot notation path, creating intermediate objects as needed.
    /// Returns a new NodeEvent instance.
    /// </summary>
    public NodeEvent SetPath(string path, object? value)
        => new(Node, Data.SetPath(path, value), Topic, Timestamp);

    /// <summary>
    /// Sets multiple values using dot notation paths in a single operation.
    /// More efficient than chaining multiple SetPath calls.
    /// Returns a new NodeEvent instance.
    /// </summary>
    public NodeEvent SetPaths(IEnumerable<KeyValuePair<string, object?>> pathValues)
        => new(Node, Data.SetPaths(pathValues), Topic, Timestamp);

    /// <summary>
    /// Sets multiple values using dot notation paths in a single operation.
    /// Convenience overload that accepts an anonymous object.
    /// Returns a new NodeEvent instance.
    /// </summary>
    public NodeEvent SetPaths(object pathValues)
        => new(Node, Data.SetPaths(pathValues), Topic, Timestamp);

    /// <summary>
    /// Deletes a value at the specified dot notation path.
    /// Returns a new NodeEvent instance.
    /// </summary>
    /// <param name="path">The dot notation path to delete (e.g., "user.profile.name")</param>
    /// <param name="allowNonLeafDeletion">If true, allows deletion of non-leaf nodes (nodes with children). If false, only leaf nodes can be deleted.</param>
    /// <returns>A new NodeEvent instance with the path removed</returns>
    public NodeEvent DeletePath(string path, bool allowNonLeafDeletion = false)
        => new(Node, Data.DeletePath(path, allowNonLeafDeletion), Topic, Timestamp);

    /// <summary>
    /// Deletes multiple paths in a single operation.
    /// More efficient than chaining multiple DeletePath calls.
    /// Returns a new NodeEvent instance.
    /// </summary>
    /// <param name="paths">The paths to delete</param>
    /// <param name="allowNonLeafDeletion">If true, allows deletion of non-leaf nodes</param>
    /// <returns>A new NodeEvent instance with the paths removed</returns>
    public NodeEvent DeletePaths(IEnumerable<string> paths, bool allowNonLeafDeletion = false)
        => new(Node, Data.DeletePaths(paths, allowNonLeafDeletion), Topic, Timestamp);
}
