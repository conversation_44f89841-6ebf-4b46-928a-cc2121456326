{"AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:2870"}}}, "Serilog": {"Using": {"0": "Serilog.Sinks.Console"}, "MinimumLevel": {"Default": "Information"}, "LevelSwitches": {"$consoleLevel": "Information"}, "WriteTo": {"console": {"Name": "<PERSON><PERSON><PERSON>", "Args": {"levelSwitch": "$consoleLevel", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3} {SourceContext}] {Message:lj}{NewLine}{Exception}"}}}}}