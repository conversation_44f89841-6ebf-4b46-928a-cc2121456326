using Akka.Hosting;
using Akka.Logger.Serilog;
using Dapper.Logging;
using Dapper.Logging.Configuration;
using KegBridgeCore.Actors;
using KegBridgeCore.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Npgsql;
using Serilog;
using System;
using KegBridgeCore.Hubs;
using NCalc;

namespace KegBridgeCore;

public class Startup(IConfiguration configuration)
{
    private IConfiguration Configuration { get; } = configuration;

    public void ConfigureServices(IServiceCollection services)
    {
        services.Configure<KegBridgeCoreSettings>(Configuration.GetSection("KegBridgeCore"));
        services.AddSerilog();

        services.AddRouting(options =>
        {
            options.LowercaseUrls = true;
        });

        services.AddControllersWithViews()
            .ConfigureApiBehaviorOptions(options =>
            {
                options.SuppressModelStateInvalidFilter = true;
                options.SuppressMapClientErrors = true;
            })
            .AddNewtonsoftJson();
        services.AddHttpContextAccessor();
        services.AddTransient<IRazorPartialToStringRenderer, RazorPartialToStringRenderer>();
        services.AddSignalR();
        services.AddSwaggerGen();

        services.AddSingleton<INodeRepository,NodeRepository>();

        services.AddDbConnectionFactory(
            sp => new NpgsqlConnection(Configuration.GetConnectionString("DatabaseContext")),
            options => options
                .WithLogLevel(LogLevel.Debug)
                .WithSensitiveDataLogging() //to show values of the query parameters
                .WithConnectionProjector(c => new { c.DataSource, c.Database })
            , ServiceLifetime.Scoped);

        services.AddAkka("kbcore-actors", (builder,sp) =>
        {
            builder
                .ConfigureLoggers(setup =>
                {
                    setup.LogLevel = Akka.Event.LogLevel.DebugLevel;
                    //setup.LogConfigOnStart = true;
                    setup.ClearLoggers();
                    setup.AddLogger<SerilogLogger>();
                    setup.WithDefaultLogMessageFormatter<SerilogLogMessageFormatter>();
                })
                //.WithActors((system, registry, resolver) =>
                //{
                //    var eventRouterProps = resolver.Props<EventRouter>();
                //    var eventRouterActor = system.ActorOf(eventRouterProps, "event-router");
                //    registry.Register<EventRouter>(eventRouterActor);
                //})
                //.WithActors((system, registry, resolver) =>
                // {
                //     var nodeManagerProps = resolver.Props<NodeManager>();
                //     var nodeManagerActor = system.ActorOf(nodeManagerProps, "nodes");
                //     registry.Register<NodeManager>(nodeManagerActor);
                // })
                .WithActors((system, registry, resolver) =>
                {
                    var flowManagerProps = resolver.Props<FlowManager>();
                    var flowManagerActor = system.ActorOf(flowManagerProps, "flows");
                    registry.Register<FlowManager>(flowManagerActor);
                })
                .WithActors((system, registry, resolver) =>
                {
                    var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
                    Log.Logger.Information("Create config manager for {environment}", environment);
                    var configManagerProps = resolver.Props<ConfigManager>("flows/");
                    var configManagerActor = system.ActorOf(configManagerProps, "config");
                    registry.Register<ConfigManager>(configManagerActor);
                });
        });
    }

    // This method gets called by the runtime. 
    // Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHostApplicationLifetime lifetime, ILoggerFactory loggerFactory)
    {
        app.UseSerilogRequestLogging();

        app.UseWebSockets(new WebSocketOptions
        {
            KeepAliveInterval = TimeSpan.FromMinutes(2)
        });

        if (!env.IsDevelopment())
        {
            app.UseExceptionHandler("/Error");
        }

        app.UseStaticFiles();
        app.UseRouting();

        app.UseSwagger();
        app.UseSwaggerUI();

        app.UseAntiforgery();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapControllerRoute(name: "default", pattern: "{controller=Home}/{action=Index}/{id?}");
            endpoints.MapHub<AppHub>("/appHub");
        });

        if (env.IsDevelopment())
        {
            app.UseSpa(spa =>
                spa.UseProxyToSpaDevelopmentServer("http://localhost:5173/"));
        }
    }
}