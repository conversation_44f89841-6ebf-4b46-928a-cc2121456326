{
  "AllowedHosts": "*",
  "KegBridge": {
    "ConfigFile": "keg_bridge-belhaven.json"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore": "Warning",
        "System": "Warning",
        "Serilog.AspNetCore.RequestLoggingMiddleware": "Error",
        "Dapper.Logging.IDbConnectionFactory":  "Information",
        "KegBridgeCore.Actors.EventRouter": "Information",
        "KegBridgeCore.Actors.NodeManager": "Information",
        "KegBridgeCore.Nodes.Plc.PlcNode": "Information",
        "KegBridgeCore.Nodes.Hmi.HmiNode": "Information",
        "KegBridgeCore.Nodes.Robot.RobotNode": "Information",
        "KegBridgeCore.Nodes.Plc.Communicators.SiemensS7": "Information",
        "KegBridgeCore.Nodes.Decoder.DecoderNode": "Information",
        "KegBridgeCore.Nodes.JsScript.JsScriptNode": "Information",
        "KegBridgeCore.Nodes.JsScript.JsScriptNode.Scripts": "Information",
        "KegBridgeCore.Nodes.Influx.InfluxNode": "Information"
      }
    },
    "LevelSwitches": {
      "$consoleLevel": "Information"
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "levelSwitch": "$consoleLevel",
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3} {SourceContext}] {Message:lj}{NewLine}{Exception}"
        }
      },
      //{
      //  "Name": "Seq",
      //  "Args": { "serverUrl": "http://localhost:5341" }
      //}
      {
        "Name": "File",
        "Args": {
          "path": "./logs/kegbridge.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 14,
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3} {SourceContext}] {Message:lj}{NewLine}{Exception}"
        }
      }    
    ]
  },
  "ConnectionStrings": {
    "DatabaseContext": "Host=127.0.0.1;Database=kb_belhaven;Username=kegbridge;Password=******;timezone=UTC;encoding=UTF8"
  }
}
