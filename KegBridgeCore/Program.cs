using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.IO;
using Destructurama;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Dynamic;
using Serilog.Exceptions;
using Serilog.Debugging;
using SimpleMigrations.DatabaseProvider;
using SimpleMigrations;
using System.Reflection;
using Dapper.FastCrud;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Microsoft.AspNetCore.Hosting.Server;
using Dapper.Logging;
using KegBridgeCore.Data;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace KegBridgeCore;

public class Program
{
    public static int Main(string[] args)
    {
        //DisableConsoleQuickEdit.Go();

        OrmConfiguration.DefaultDialect = SqlDialect.PostgreSql;

        JsonConvert.DefaultSettings = () => new JsonSerializerSettings
        {
            Formatting = Formatting.Indented,
            ContractResolver = new DefaultContractResolver { NamingStrategy = new SnakeCaseNamingStrategy() }
        };

        SelfLog.Enable(Console.Error);

        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";

        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile(path: "appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{environment}.json", optional: true)
            .Build();

        var baseLogger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Destructure.ToMaximumDepth(100)
            .Destructure.UsingAttributes()
            .Destructure.JsonNetTypes()
            // https://stackoverflow.com/questions/48958444/serilog-and-expandoobject 
            .Destructure.ByTransforming<ExpandoObject>(e => new Dictionary<string, object>(e))
            .Destructure.ByTransforming<NodeEventData>(e => new Dictionary<string, object>(e))
            .Destructure.ByTransforming<IReadOnlyDictionary<string, object?>>(e => new Dictionary<string, object>(e))
            .Destructure.ByTransforming<ImmutableDictionary<string, object?>>(e => new Dictionary<string, object>(e))
            .Enrich.WithThreadId()
            .Enrich.FromLogContext()
            .Enrich.WithExceptionDetails()
            .Enrich.WithProperty("application", "kegbridgecore")
            .Enrich.WithProperty("startup", DateTime.Now.ToString("yyyyMMddHHmm"))
            .CreateLogger();

        Serilog.Log.Logger = baseLogger;
        
        var logger = baseLogger.ForContext("SourceContext", "KegBridgeCore.Main");

        // dynamic e = new ExpandoObject();
        // e.baz = "a";
        // e.test2 = 1;
        // e.obj = new { a = 1, b = 2 };
        // logger.Debug("exp data {e} {@e}", e, e);
        //
        // var ned = NodeEventData.FromExpando(e);
        // logger.Debug("ned data {ned} {@ned}", ned, ned);
        //
        // var ne = new NodeEvent("test", ned, "topix");
        // logger.Debug("ne {ne} {@ne}", ne, ne);
        // return(0);
        
        try
        {
            logger.Information("Starting Webhost for {environment}", environment);
            logger.Debug("Starting Webhost for {environment}", environment);

            var webHost = CreateHostBuilder(args).Build();

            using (var serviceScope = webHost.Services.CreateScope())
            {
                var services = serviceScope.ServiceProvider;

                var dbConnFactory = services.GetRequiredService<IDbConnectionFactory>();
                using (var connection = dbConnFactory.CreateConnection())
                {
                    try
                    {
                        var pgProvider = new PostgresqlDatabaseProvider(connection);
                        var migrator = new SimpleMigrator(Assembly.GetEntryAssembly(),
                            pgProvider);
                        //new MigrationLogger(logger.ForContext("Migration", true)));
                        migrator.Load();
                        migrator.MigrateToLatest();
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, "Error migrating database");
                    }
                }
            }

            webHost.Start();

            // log kestrel endpoints
            var server = webHost.Services.GetService<IServer>();
            var addressFeature = server.Features.Get<IServerAddressesFeature>();

            foreach (var address in addressFeature.Addresses)
            {
                logger.Information("Webhost listening on {address}", address);
            }

            webHost.WaitForShutdown();

            logger.Information("Exit");

            return 0;
        }
        catch (Exception ex)
        {
            logger.Fatal(ex, "Host terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
            })
            .UseSerilog();

}