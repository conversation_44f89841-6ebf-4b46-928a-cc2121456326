{
  "Serilog": {
    "Using": {
      "1": "Serilog.Sinks.File",
      "2": "Serilog.Sinks.Seq"
    },
    "LevelSwitches": {
      "$consoleLevel": "Information"
    },
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore": "Warning",
        "System": "Warning",
        "Serilog.AspNetCore.RequestLoggingMiddleware": "Information",
        "Dapper.Logging.IDbConnectionFactory": "Information",
        "KegBridgeCore.Actors.EventRouter": "Information",
        "KegBridgeCore.Actors.NodeManager": "Information",
        "KegBridgeCore.Nodes.Decoder.DecoderNode": "Information",
        "KegBridgeCore.Nodes.Hmi.HmiNode": "Information",
        "KegBridgeCore.Nodes.Influx.InfluxNode": "Information",
        "KegBridgeCore.Nodes.JsScript.JsScriptNode": "Information",
        "KegBridgeCore.Nodes.JsScript.JsScriptNode.Scripts": "Information",
        "KegBridgeCore.Nodes.Modbus.ModbusNode": "Information",
        "KegBridgeCore.Nodes.OpcUa.OpcUaNode": "Information",
        "KegBridgeCore.Nodes.Plc.PlcNode": "Information",
        "KegBridgeCore.Nodes.Plc.Communicators.SiemensS7": "Information",
        "KegBridgeCore.Nodes.Robot.HmiNode": "Information"
      }
    },
    "WriteTo": {
      // "file": {
      //   "Name": "File",
      //   "Args": {
      //     "path": "./logs/kegbridge.log",
      //     "rollingInterval": "Day",
      //     "retainedFileCountLimit": 14,
      //     "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3} {SourceContext}] {Message:lj}{NewLine}{Exception}"
      //   }
      // },

      "seq": {
       "Name": "Seq",
       "Args": { "serverUrl": "http://localhost:5341" }
      }

      //,"loki": {
      //  "Name": "GrafanaLoki",
      //  "Args": {
      //    "uri": "http://localhost:3100",
      //    "propertiesAsLabels": [
      //      "application"
      //    ]
      //  }
      //}
    }
  }
  //"ConnectionStrings": {
  //  "DatabaseContext": "Host=127.0.0.1;Database=kb_belhaven;Username=kegbridge;Password=******;timezone=UTC;encoding=UTF8"
  //}
}