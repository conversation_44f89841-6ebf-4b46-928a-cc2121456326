using System;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using KegBridgeCore.Utilities;
using System.Collections;
using System.Collections.Generic;

namespace KegBridgeCore.Logging
{
    /// <summary>
    /// Serilog destructuring policy that converts byte arrays to hex: prefixed format
    /// </summary>
    public class ByteArrayDestructuringPolicy : IDestructuringPolicy
    {
        public bool TryDestructure(object value, ILogEventPropertyValueFactory propertyValueFactory, out LogEventPropertyValue result)
        {
            result = null!;

            // Handle byte arrays directly
            if (value is byte[] bytes)
            {
                result = new ScalarValue(ByteArrayConverter.BytesToHexSignature(bytes));
                return true;
            }

            // Handle ImmutableArray<object> that contains bytes (from NodeEventData)
            if (value is System.Collections.Immutable.ImmutableArray<object> immutableArray)
            {
                // Check if this is a byte array stored as ImmutableArray<object>
                if (immutableArray.Length > 0 && immutableArray.All(item => item is byte || (item is int intVal && intVal >= 0 && intVal <= 255)))
                {
                    try
                    {
                        var convertedBytes = immutableArray.Select(obj => Convert.ToByte(obj)).ToArray();
                        result = new ScalarValue(ByteArrayConverter.BytesToHexSignature(convertedBytes));
                        return true;
                    }
                    catch
                    {
                        // If conversion fails, fall through to normal processing
                    }
                }
            }

            // Handle NodeEventData specifically
            if (value is KegBridgeCore.Data.NodeEventData nodeEventData)
            {
                result = DestructureNodeEventData(nodeEventData, propertyValueFactory);
                return true;
            }

            // Handle objects that might contain byte arrays
            if (value != null && !IsSimpleType(value.GetType()))
            {
                result = DestructureObject(value, propertyValueFactory);
                return true;
            }

            return false;
        }

        private LogEventPropertyValue DestructureNodeEventData(KegBridgeCore.Data.NodeEventData nodeEventData, ILogEventPropertyValueFactory propertyValueFactory)
        {
            var properties = new List<LogEventProperty>();
            var dictionary = nodeEventData.ToDictionary();

            foreach (var kvp in dictionary)
            {
                LogEventPropertyValue logValue;

                if (kvp.Value is byte[] bytes)
                {
                    logValue = new ScalarValue(ByteArrayConverter.BytesToHexSignature(bytes));
                }
                else if (kvp.Value is System.Collections.Immutable.ImmutableArray<object> immutableArray)
                {
                    // Check if this is a byte array stored as ImmutableArray<object>
                    if (immutableArray.Length > 0 && immutableArray.All(item => item is byte || (item is int intVal && intVal >= 0 && intVal <= 255)))
                    {
                        try
                        {
                            var convertedBytes = immutableArray.Select(obj => Convert.ToByte(obj)).ToArray();
                            logValue = new ScalarValue(ByteArrayConverter.BytesToHexSignature(convertedBytes));
                        }
                        catch
                        {
                            logValue = propertyValueFactory.CreatePropertyValue(kvp.Value, destructureObjects: true);
                        }
                    }
                    else
                    {
                        logValue = propertyValueFactory.CreatePropertyValue(kvp.Value, destructureObjects: true);
                    }
                }
                else if (kvp.Value != null && !IsSimpleType(kvp.Value.GetType()))
                {
                    logValue = DestructureObject(kvp.Value, propertyValueFactory);
                }
                else
                {
                    logValue = propertyValueFactory.CreatePropertyValue(kvp.Value, destructureObjects: true);
                }

                properties.Add(new LogEventProperty(kvp.Key.Replace('.', '_'), logValue));
            }

            return new StructureValue(properties, "NodeEventData");
        }

        private LogEventPropertyValue DestructureObject(object value, ILogEventPropertyValueFactory propertyValueFactory)
        {
            var type = value.GetType();

            // Handle collections
            if (value is IEnumerable enumerable && !(value is string))
            {
                var elements = new List<LogEventPropertyValue>();
                foreach (var item in enumerable)
                {
                    if (item is byte[] bytes)
                    {
                        elements.Add(new ScalarValue(ByteArrayConverter.BytesToHexSignature(bytes)));
                    }
                    else if (item is System.Collections.Immutable.ImmutableArray<object> immutableArray)
                    {
                        // Check if this is a byte array stored as ImmutableArray<object>
                        if (immutableArray.Length > 0 && immutableArray.All(obj => obj is byte || (obj is int intVal && intVal >= 0 && intVal <= 255)))
                        {
                            try
                            {
                                var convertedBytes = immutableArray.Select(obj => Convert.ToByte(obj)).ToArray();
                                elements.Add(new ScalarValue(ByteArrayConverter.BytesToHexSignature(convertedBytes)));
                            }
                            catch
                            {
                                elements.Add(propertyValueFactory.CreatePropertyValue(item, destructureObjects: true));
                            }
                        }
                        else
                        {
                            elements.Add(propertyValueFactory.CreatePropertyValue(item, destructureObjects: true));
                        }
                    }
                    else if (item != null && !IsSimpleType(item.GetType()))
                    {
                        elements.Add(DestructureObject(item, propertyValueFactory));
                    }
                    else
                    {
                        elements.Add(propertyValueFactory.CreatePropertyValue(item, destructureObjects: true));
                    }
                }
                return new SequenceValue(elements);
            }

            // Handle regular objects
            var properties = new List<LogEventProperty>();
            var typeProperties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            foreach (var prop in typeProperties)
            {
                try
                {
                    var propValue = prop.GetValue(value);
                    LogEventPropertyValue logValue;

                    if (propValue is byte[] propBytes)
                    {
                        logValue = new ScalarValue(ByteArrayConverter.BytesToHexSignature(propBytes));
                    }
                    else if (propValue is System.Collections.Immutable.ImmutableArray<object> immutableArray)
                    {
                        // Check if this is a byte array stored as ImmutableArray<object>
                        if (immutableArray.Length > 0 && immutableArray.All(obj => obj is byte || (obj is int intVal && intVal >= 0 && intVal <= 255)))
                        {
                            try
                            {
                                var convertedBytes = immutableArray.Select(obj => Convert.ToByte(obj)).ToArray();
                                logValue = new ScalarValue(ByteArrayConverter.BytesToHexSignature(convertedBytes));
                            }
                            catch
                            {
                                logValue = propertyValueFactory.CreatePropertyValue(propValue, destructureObjects: true);
                            }
                        }
                        else
                        {
                            logValue = propertyValueFactory.CreatePropertyValue(propValue, destructureObjects: true);
                        }
                    }
                    else if (propValue != null && !IsSimpleType(propValue.GetType()))
                    {
                        logValue = DestructureObject(propValue, propertyValueFactory);
                    }
                    else
                    {
                        logValue = propertyValueFactory.CreatePropertyValue(propValue, destructureObjects: true);
                    }

                    properties.Add(new LogEventProperty(prop.Name, logValue));
                }
                catch
                {
                    // Skip properties that can't be read
                    continue;
                }
            }

            return new StructureValue(properties, type.Name);
        }

        private static bool IsSimpleType(Type type)
        {
            return type.IsPrimitive ||
                   type.IsEnum ||
                   type == typeof(string) ||
                   type == typeof(decimal) ||
                   type == typeof(DateTime) ||
                   type == typeof(DateTimeOffset) ||
                   type == typeof(TimeSpan) ||
                   type == typeof(Guid) ||
                   (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>));
        }
    }

    /// <summary>
    /// Extension methods for configuring byte array destructuring
    /// </summary>
    public static class ByteArrayDestructuringExtensions
    {
        /// <summary>
        /// Adds byte array destructuring policy to the logger configuration
        /// </summary>
        public static LoggerConfiguration WithByteArrayDestructuring(this LoggerConfiguration loggerConfiguration)
        {
            return loggerConfiguration.Destructure.With<ByteArrayDestructuringPolicy>();
        }
    }
}
