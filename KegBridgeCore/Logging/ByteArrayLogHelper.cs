using System;
using Serilog;
using Serilog.Events;
using KegBridgeCore.Utilities;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace KegBridgeCore.Logging
{
    /// <summary>
    /// Helper methods for logging objects with byte arrays converted to hex format
    /// </summary>
    public static class ByteArrayLogHelper
    {
        /// <summary>
        /// Converts an object to a logging-friendly format with byte arrays as hex strings
        /// </summary>
        public static object ToLogFormat(object? obj)
        {
            if (obj == null) return null!;

            return obj switch
            {
                byte[] bytes => ByteArrayConverter.BytesToHexSignature(bytes),
                System.Collections.Immutable.ImmutableArray<object> immutableArray => ConvertImmutableArray(immutableArray),
                Data.NodeEventData nodeEventData => ConvertNodeEventData(nodeEventData),
                string str => str,
                _ when IsSimpleType(obj.GetType()) => obj,
                IEnumerable enumerable => ConvertEnumerable(enumerable),
                _ => ConvertObject(obj)
            };
        }

        /// <summary>
        /// Converts an object to a logging-friendly format with spaced hex format
        /// </summary>
        public static object ToLogFormatSpaced(object? obj)
        {
            if (obj == null) return null!;

            return obj switch
            {
                byte[] bytes => ByteArrayConverter.BytesToHexSignature(bytes).Replace("hex:", "hex:").Insert(4, " ").Replace("", " ").Trim(),
                string str => str,
                _ when IsSimpleType(obj.GetType()) => obj,
                IEnumerable enumerable => ConvertEnumerableSpaced(enumerable),
                _ => ConvertObjectSpaced(obj)
            };
        }

        /// <summary>
        /// Creates an anonymous object with byte arrays converted to hex format for structured logging
        /// </summary>
        public static object CreateLogObject(params (string name, object? value)[] properties)
        {
            var logObject = new Dictionary<string, object>();
            
            foreach (var (name, value) in properties)
            {
                logObject[name] = ToLogFormat(value);
            }

            return logObject;
        }

        /// <summary>
        /// Converts a NodeEvent to a logging-friendly format
        /// </summary>
        public static object ToLogFormat(Data.NodeEvent nodeEvent)
        {
            return new
            {
                Node = nodeEvent.Node,
                Topic = nodeEvent.Topic,
                Timestamp = nodeEvent.Timestamp,
                Id = nodeEvent.Id,
                Data = ToLogFormat(nodeEvent.Data.ToDictionary())
            };
        }

        /// <summary>
        /// Extension method for ILogger to log with automatic byte array conversion
        /// </summary>
        public static void LogObject(this ILogger logger, LogEventLevel level, string messageTemplate, object obj)
        {
            logger.Write(level, messageTemplate, ToLogFormat(obj));
        }

        /// <summary>
        /// Extension method for ILogger to log NodeEvent with automatic byte array conversion
        /// </summary>
        public static void LogNodeEvent(this ILogger logger, LogEventLevel level, string messageTemplate, Data.NodeEvent nodeEvent)
        {
            logger.Write(level, messageTemplate, ToLogFormat(nodeEvent));
        }

        private static object ConvertImmutableArray(System.Collections.Immutable.ImmutableArray<object> immutableArray)
        {
            // Check if this is a byte array stored as ImmutableArray<object>
            if (immutableArray.Length > 0 && immutableArray.All(item => item is byte || (item is int intVal && intVal >= 0 && intVal <= 255)))
            {
                try
                {
                    var convertedBytes = immutableArray.Select(obj => Convert.ToByte(obj)).ToArray();
                    return ByteArrayConverter.BytesToHexSignature(convertedBytes);
                }
                catch
                {
                    // If conversion fails, convert as regular enumerable
                    return ConvertEnumerable(immutableArray);
                }
            }

            // Not a byte array, convert as regular enumerable
            return ConvertEnumerable(immutableArray);
        }

        private static object ConvertNodeEventData(Data.NodeEventData nodeEventData)
        {
            var result = new Dictionary<string, object?>();
            var dictionary = nodeEventData.ToDictionary();

            foreach (var kvp in dictionary)
            {
                result[kvp.Key] = ToLogFormat(kvp.Value);
            }

            return result;
        }

        private static object ConvertEnumerable(IEnumerable enumerable)
        {
            var result = new List<object?>();
            foreach (var item in enumerable)
            {
                result.Add(ToLogFormat(item));
            }
            return result;
        }

        private static object ConvertEnumerableSpaced(IEnumerable enumerable)
        {
            var result = new List<object?>();
            foreach (var item in enumerable)
            {
                result.Add(ToLogFormatSpaced(item));
            }
            return result;
        }

        private static object ConvertObject(object obj)
        {
            var type = obj.GetType();
            var result = new Dictionary<string, object?>();

            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(obj);
                    result[prop.Name] = ToLogFormat(value);
                }
                catch
                {
                    // Skip properties that can't be read
                    result[prop.Name] = "<error reading property>";
                }
            }

            return result;
        }

        private static object ConvertObjectSpaced(object obj)
        {
            var type = obj.GetType();
            var result = new Dictionary<string, object?>();

            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(obj);
                    result[prop.Name] = ToLogFormatSpaced(value);
                }
                catch
                {
                    // Skip properties that can't be read
                    result[prop.Name] = "<error reading property>";
                }
            }

            return result;
        }

        private static bool IsSimpleType(Type type)
        {
            return type.IsPrimitive ||
                   type.IsEnum ||
                   type == typeof(string) ||
                   type == typeof(decimal) ||
                   type == typeof(DateTime) ||
                   type == typeof(DateTimeOffset) ||
                   type == typeof(TimeSpan) ||
                   type == typeof(Guid) ||
                   (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>));
        }
    }

    /// <summary>
    /// Fluent interface for building log objects with byte array conversion
    /// </summary>
    public class LogObjectBuilder
    {
        private readonly Dictionary<string, object?> _properties = new();

        public LogObjectBuilder Add(string name, object? value)
        {
            _properties[name] = ByteArrayLogHelper.ToLogFormat(value);
            return this;
        }

        public LogObjectBuilder AddRaw(string name, object? value)
        {
            _properties[name] = value;
            return this;
        }

        public LogObjectBuilder AddByteArray(string name, byte[]? bytes, bool useSpaces = false)
        {
            if (bytes == null)
            {
                _properties[name] = null;
            }
            else
            {
                var hex = ByteArrayConverter.BytesToHexSignature(bytes);
                _properties[name] = useSpaces ? hex.Replace("hex:", "hex:").Insert(4, " ") : hex;
            }
            return this;
        }

        public object Build() => _properties;

        public static LogObjectBuilder Create() => new();
    }
}
