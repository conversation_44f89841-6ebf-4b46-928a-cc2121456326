# Byte Array Logging with Serilog

This document explains how to configure Serilog to automatically convert byte arrays to hex: prefixed format in JSON logs.

## Quick Start

### 1. Basic Configuration (Recommended)

```csharp
using Serilog;
using KegBridgeCore.Logging;

// Configure logger with automatic byte array conversion
var logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .WithByteArrayHexConversion()  // Enable automatic conversion
    .WriteTo.Console()
    .WriteTo.File("logs/app.log")
    .CreateLogger();

// Use normally - byte arrays will be automatically converted
var data = new { 
    message = "Hello", 
    payload = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F } 
};

logger.Information("Received data: {@Data}", data);
// Output: Received data: {"message": "Hello", "payload": "hex:48656C6C6F"}
```

### 2. Production Configuration

```csharp
public static ILogger CreateLogger()
{
    return new LoggerConfiguration()
        .MinimumLevel.Information()
        .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
        .WithByteArrayHexConversion()
        .Enrich.FromLogContext()
        .Enrich.WithMachineName()
        .WriteTo.Console(outputTemplate: 
            "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] {Message:lj} {Properties:j}{NewLine}")
        .WriteTo.File(
            path: "logs/kegbridge-.log",
            rollingInterval: RollingInterval.Day,
            retainedFileCountLimit: 30)
        .CreateLogger();
}
```

## Available Approaches

### 1. Enricher Approach (Automatic)
- **Best for**: General use, automatic conversion
- **Pros**: Works automatically, no code changes needed
- **Cons**: Processes all log events

```csharp
var logger = new LoggerConfiguration()
    .WithByteArrayHexConversion()
    .WriteTo.Console()
    .CreateLogger();

// All byte arrays automatically converted
logger.Information("Data: {@Data}", someObjectWithByteArrays);
```

### 2. Destructuring Policy (Deep Objects)
- **Best for**: Complex nested objects with byte arrays
- **Pros**: Deep object traversal, handles collections
- **Cons**: More processing overhead

```csharp
var logger = new LoggerConfiguration()
    .WithByteArrayDestructuring()
    .WriteTo.Console()
    .CreateLogger();

// Handles deeply nested byte arrays
logger.Information("Complex object: {@Object}", complexNestedObject);
```

### 3. Helper Methods (Manual Control)
- **Best for**: Selective conversion, performance-critical scenarios
- **Pros**: Full control, minimal overhead
- **Cons**: Manual conversion required

```csharp
// Manual conversion
var logData = ByteArrayLogHelper.ToLogFormat(myObject);
logger.Information("Data: {@Data}", logData);

// Fluent builder
var logObject = LogObjectBuilder.Create()
    .Add("operation", "processing")
    .AddByteArray("payload", myByteArray)
    .Build();
logger.Information("Result: {@Result}", logObject);
```

## Output Examples

### Input Object
```csharp
var sensorData = new {
    DeviceId = "SENSOR_001",
    BinaryData = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }, // "Hello"
    Readings = new[] {
        new { Value = 23.5, Raw = new byte[] { 0x12, 0x34 } }
    }
};
```

### JSON Output
```json
{
  "DeviceId": "SENSOR_001",
  "BinaryData": "hex:48656C6C6F",
  "Readings": [
    {
      "Value": 23.5,
      "Raw": "hex:1234"
    }
  ]
}
```

## Extension Methods

### Logging with Automatic Conversion
```csharp
// Extension methods for convenience
logger.InformationWithByteArrays("Processing {DeviceId} with {Payload}", 
    "DEVICE_001", myByteArray);

logger.LogObject(LogEventLevel.Information, "Data received: {@Data}", myObject);

logger.LogNodeEvent(LogEventLevel.Information, "NodeEvent: {@Event}", nodeEvent);
```

### Fluent Builder
```csharp
var logData = LogObjectBuilder.Create()
    .Add("timestamp", DateTime.UtcNow)
    .AddByteArray("header", headerBytes)
    .AddByteArray("checksum", checksumBytes, useSpaces: true)  // hex: format with spaces
    .AddRaw("metadata", metadataObject)  // No conversion
    .Build();

logger.Information("Processing complete: {@Result}", logData);
```

## Integration with KegBridge

### NodeEvent Logging
```csharp
// Automatic conversion of NodeEvent data
logger.Information("NodeEvent received: {@NodeEvent}", nodeEvent);

// Manual conversion for specific control
var logFriendlyEvent = ByteArrayLogHelper.ToLogFormat(nodeEvent);
logger.Information("Processing: {@Event}", logFriendlyEvent);
```

### EventsController Integration
```csharp
// In your controller
[HttpPost("{node}/{topic}")]
public async Task<IActionResult> PostEvent(string node, string topic, 
    [FromBody] Dictionary<string, object> payload,
    [FromQuery] bool convertByteArrays = false)
{
    var nodeEvent = NodeEvent.FromDictionary(node, payload, topic);
    
    // Log with automatic byte array conversion
    _logger.Information("Event received: {@NodeEvent}", nodeEvent);
    
    return Ok();
}
```

## Performance Considerations

1. **Enricher**: Processes all log events - use for general logging
2. **Destructuring Policy**: More overhead for complex objects - use selectively
3. **Helper Methods**: Minimal overhead - use for high-frequency logging

## Configuration Tips

1. **Development**: Use both enricher and destructuring for maximum coverage
2. **Production**: Use enricher only for better performance
3. **High-frequency logs**: Use helper methods for manual control
4. **Debugging**: Enable debug level with automatic conversion

```csharp
// Development configuration
var devLogger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .WithByteArrayHexConversion()
    .WithByteArrayDestructuring()
    .WriteTo.Console()
    .CreateLogger();

// Production configuration  
var prodLogger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .WithByteArrayHexConversion()
    .WriteTo.File("logs/app.log")
    .CreateLogger();
```
