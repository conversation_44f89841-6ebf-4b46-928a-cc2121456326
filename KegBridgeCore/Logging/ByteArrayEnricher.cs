using System.Collections.Generic;
using System.Linq;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using KegBridgeCore.Utilities;

namespace KegBridgeCore.Logging
{
    /// <summary>
    /// Serilog enricher that converts byte arrays to hex: prefixed format in JSON output
    /// </summary>
    public class ByteArrayEnricher : ILogEventEnricher
    {
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            // Process all properties in the log event
            var propertiesToUpdate = new List<(string key, LogEventPropertyValue value)>();

            foreach (var property in logEvent.Properties)
            {
                var convertedValue = ConvertByteArraysToHex(property.Value);
                if (convertedValue != property.Value)
                {
                    propertiesToUpdate.Add((property.Key, convertedValue));
                }
            }

            // Update properties with converted values
            foreach (var (key, value) in propertiesToUpdate)
            {
                logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty(key, value, destructureObjects: true));
            }
        }

        private LogEventPropertyValue ConvertByteArraysToHex(LogEventPropertyValue value)
        {
            return value switch
            {
                ScalarValue scalar when scalar.Value is byte[] bytes =>
                    new ScalarValue(ByteArrayConverter.BytesToHexSignature(bytes)),

                ScalarValue scalar when scalar.Value is System.Collections.Immutable.ImmutableArray<object> immutableArray =>
                    ConvertImmutableArrayToHex(immutableArray),

                StructureValue structure => new StructureValue(
                    structure.Properties.Select(p => new LogEventProperty(
                        p.Name,
                        ConvertByteArraysToHex(p.Value)
                    )),
                    structure.TypeTag
                ),

                SequenceValue sequence => new SequenceValue(
                    sequence.Elements.Select(ConvertByteArraysToHex)
                ),

                DictionaryValue dictionary => new DictionaryValue(
                    dictionary.Elements.Select(kvp => new KeyValuePair<ScalarValue, LogEventPropertyValue>(
                        kvp.Key,
                        ConvertByteArraysToHex(kvp.Value)
                    ))
                ),

                _ => value
            };
        }

        private LogEventPropertyValue ConvertImmutableArrayToHex(System.Collections.Immutable.ImmutableArray<object> immutableArray)
        {
            // Check if this is a byte array stored as ImmutableArray<object>
            if (immutableArray.Length > 0 && immutableArray.All(item => item is byte || (item is int intVal && intVal >= 0 && intVal <= 255)))
            {
                try
                {
                    var convertedBytes = immutableArray.Select(obj => Convert.ToByte(obj)).ToArray();
                    return new ScalarValue(ByteArrayConverter.BytesToHexSignature(convertedBytes));
                }
                catch
                {
                    // If conversion fails, return as-is
                    return new ScalarValue(immutableArray);
                }
            }

            // Not a byte array, return as-is
            return new ScalarValue(immutableArray);
        }
    }

    /// <summary>
    /// Extension methods for configuring byte array logging
    /// </summary>
    public static class ByteArrayLoggingExtensions
    {
        /// <summary>
        /// Adds byte array to hex conversion enricher to the logger configuration
        /// </summary>
        public static LoggerConfiguration WithByteArrayHexConversion(this LoggerConfiguration loggerConfiguration)
        {
            return loggerConfiguration.Enrich.With<ByteArrayEnricher>();
        }

        /// <summary>
        /// Logs an object with automatic byte array conversion to hex format
        /// </summary>
        public static void LogWithByteArrays(this ILogger logger, LogEventLevel level, string messageTemplate, params object[] propertyValues)
        {
            // Convert any byte arrays in the property values to hex format
            var convertedValues = propertyValues.Select(ConvertByteArrayToHex).ToArray();
            logger.Write(level, messageTemplate, convertedValues);
        }

        /// <summary>
        /// Logs an object with automatic byte array conversion to hex format (Information level)
        /// </summary>
        public static void InformationWithByteArrays(this ILogger logger, string messageTemplate, params object[] propertyValues)
        {
            logger.LogWithByteArrays(LogEventLevel.Information, messageTemplate, propertyValues);
        }

        /// <summary>
        /// Logs an object with automatic byte array conversion to hex format (Debug level)
        /// </summary>
        public static void DebugWithByteArrays(this ILogger logger, string messageTemplate, params object[] propertyValues)
        {
            logger.LogWithByteArrays(LogEventLevel.Debug, messageTemplate, propertyValues);
        }

        /// <summary>
        /// Logs an object with automatic byte array conversion to hex format (Warning level)
        /// </summary>
        public static void WarningWithByteArrays(this ILogger logger, string messageTemplate, params object[] propertyValues)
        {
            logger.LogWithByteArrays(LogEventLevel.Warning, messageTemplate, propertyValues);
        }

        /// <summary>
        /// Logs an object with automatic byte array conversion to hex format (Error level)
        /// </summary>
        public static void ErrorWithByteArrays(this ILogger logger, string messageTemplate, params object[] propertyValues)
        {
            logger.LogWithByteArrays(LogEventLevel.Error, messageTemplate, propertyValues);
        }

        private static object ConvertByteArrayToHex(object value)
        {
            return value switch
            {
                byte[] bytes => ByteArrayConverter.BytesToHexSignature(bytes),
                _ => value
            };
        }
    }
}
