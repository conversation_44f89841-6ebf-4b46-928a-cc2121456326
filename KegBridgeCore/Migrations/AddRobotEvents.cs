using SimpleMigrations;

namespace KegBridgeCore.Migrations;

[Migration(2, "Add robot alarms table")]
class AddRobotAlarms: Migration
{
    protected override void Up()
    {
        Execute(@"CREATE TABLE robot_alarms (
                id SERIAL NOT NULL PRIMARY KEY,
                source TEXT NOT NULL,
                event_at  TIMESTAMP WITH TIME ZONE,
                msg_code TEXT,
                msg_text TEXT NOT NULL,
                msg_severity TEXT,
                msg_data TEXT,
                msg_bits TEXT,
                msg_act TEXT,
                digest TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE
            )");
        Execute(@"CREATE UNIQUE INDEX robot_alarms_digest ON robot_alarms(source,digest)");
        Execute(@"CREATE INDEX robot_alarms_event_at ON robot_alarms(event_at)");
    }

    protected override void Down()
    {
        Execute(@"DROP TABLE robot_alarms");
    }
}