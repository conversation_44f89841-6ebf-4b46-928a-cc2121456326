using SimpleMigrations;

namespace KegBridgeCore.Migrations;

[Migration(3, "Add updated_at to hmi events table")]
class AddUpdatedAtToHmiEvents: Migration
{
    protected override void Up()
    {
        Execute(@"ALTER TABLE hmi_events ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE");
    }

    protected override void Down()
    {
        Execute(@"ALTER TABLE hmi_events DROP COLUMN updated_at");
    }
}