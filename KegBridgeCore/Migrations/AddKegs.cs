using SimpleMigrations;

namespace Keg<PERSON>ridgeCore.Migrations;

[Migration(4, "Add Kegs")]
class AddKegs: Migration
{
    protected override void Up()
    {
        Execute(@"CREATE TABLE kegs (
                serial_number TEXT NOT NULL PRIMARY KEY,
                keg_run_count INTEGER,
                created_at TIMESTAMP WITH TIME ZONE,
                updated_at TIMESTAMP WITH TIME ZONE
            )");
        Execute(@"CREATE TABLE keg_runs (
                id SERIAL NOT NULL PRIMARY KEY,
                keg_serial_number TEXT REFERENCES kegs,
                created_at TIMESTAMP WITH TIME ZONE,
                updated_at TIMESTAMP WITH TIME ZONE,
                product TEXT,
                quality TEXT,
                data JSONB
            )");
    }

    protected override void Down()
    {
        Execute(@"DROP TABLE kegs");
        Execute(@"DROP TABLE keg_runs");
    }
}