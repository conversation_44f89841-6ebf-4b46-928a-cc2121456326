using SimpleMigrations;

namespace Keg<PERSON>ridgeCore.Migrations;

[Migration(1, "Add hmi events table")]
class AddHmiEvents: Migration
{
    protected override void Up()
    {
        Execute(@"CREATE TABLE hmi_events (
                id SERIAL NOT NULL PRIMARY KEY,
                source TEXT NOT NULL,
                event_at  TIMESTAMP WITH TIME ZONE,
                state_after SMALLINT,
                msg_text TEXT NOT NULL,
                msg_proc SMALLINT,
                msg_class SMALLINT,
                msg_number INTEGER,
                var1 TEXT,
                var2 TEXT,
                var3 TEXT,
                var4 TEXT,
                var5 TEXT,
                var6 TEXT,
                var7 TEXT,
                var8 TEXT,
                event_on_at TIMESTAMP WITH TIME ZONE,
                event_off_at TIMESTAMP WITH TIME ZONE,
                other_event_id INTEGER,
                digest TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE
            )");
        Execute(@"CREATE UNIQUE INDEX hmi_events_digest ON hmi_events(digest)");
        Execute(@"CREATE INDEX hmi_events_event_at ON hmi_events(event_at)");
    }

    protected override void Down()
    {
        Execute(@"DROP TABLE hmi_events");
    }
}