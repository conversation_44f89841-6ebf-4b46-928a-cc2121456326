{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "weatherforecast", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "KegBridgeCore": {"commandName": "Project", "workingDirectory": "D:\\Develop\\KegBridge\\KegBridgeCore", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "INFLUX_TEST": "Hall<PERSON>"}}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:50598", "sslPort": 0}}}