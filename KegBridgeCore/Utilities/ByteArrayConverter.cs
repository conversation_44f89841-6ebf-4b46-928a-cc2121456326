using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace KegBridgeCore.Utilities
{
    /// <summary>
    /// Utility class for converting special string signatures to byte arrays in JSON payloads.
    /// This allows representing binary data in JSON that gets properly converted to byte[] in NodeEventData.
    /// </summary>
    public static class ByteArrayConverter
    {
        // Regex patterns for different byte array formats
        private static readonly Regex Base64Pattern = new(@"^base64:([A-Za-z0-9+/=]+)$", RegexOptions.Compiled);
        private static readonly Regex HexPattern = new(@"^hex:([0-9A-Fa-f\s\-]+)$", RegexOptions.Compiled);
        private static readonly Regex BytesPattern = new(@"^bytes:\[([0-9,\s]+)\]$", RegexOptions.Compiled);
        private static readonly Regex Utf8Pattern = new(@"^utf8:(.+)$", RegexOptions.Compiled | RegexOptions.Singleline);
        private static readonly Regex AsciiPattern = new(@"^ascii:(.+)$", RegexOptions.Compiled | RegexOptions.Singleline);

        /// <summary>
        /// Supported byte array string signatures:
        /// - base64:SGVsbG8gV29ybGQ= (Base64 encoded data)
        /// - hex:48656C6C6F20576F726C64 (Hexadecimal string, spaces and hyphens are ignored)
        /// - hex:48-65-6C-6C-6F-20-57-6F-72-6C-64 (Hex with hyphens for readability)
        /// - hex:48 65 6C 6C 6F 20 57 6F 72 6C 64 (Hex with spaces for readability)
        /// - bytes:[72,101,108,108,111,32,87,111,114,108,100] (Comma-separated byte values)
        /// - utf8:Hello World (UTF-8 encoded string)
        /// - ascii:Hello World (ASCII encoded string)
        /// </summary>
        public static bool TryConvertString(string input, out byte[] result)
        {
            result = null;

            if (string.IsNullOrEmpty(input))
                return false;

            try
            {
                // Try Base64 format: base64:SGVsbG8gV29ybGQ=
                var base64Match = Base64Pattern.Match(input);
                if (base64Match.Success)
                {
                    result = Convert.FromBase64String(base64Match.Groups[1].Value);
                    return true;
                }

                // Try Hex format: hex:48656C6C6F20576F726C64 or hex:48-65-6C-6C-6F or hex:48 65 6C 6C 6F
                var hexMatch = HexPattern.Match(input);
                if (hexMatch.Success)
                {
                    var hexString = hexMatch.Groups[1].Value;

                    // Remove spaces and hyphens to allow for readable formatting
                    hexString = hexString.Replace(" ", "").Replace("-", "");

                    if (hexString.Length % 2 != 0)
                        return false; // Invalid hex string length

                    result = new byte[hexString.Length / 2];
                    for (int i = 0; i < result.Length; i++)
                    {
                        result[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
                    }
                    return true;
                }

                // Try Bytes array format: bytes:[72,101,108,108,111]
                var bytesMatch = BytesPattern.Match(input);
                if (bytesMatch.Success)
                {
                    var bytesString = bytesMatch.Groups[1].Value;
                    var byteStrings = bytesString.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    result = new byte[byteStrings.Length];
                    
                    for (int i = 0; i < byteStrings.Length; i++)
                    {
                        if (!byte.TryParse(byteStrings[i].Trim(), out result[i]))
                            return false;
                    }
                    return true;
                }

                // Try UTF-8 format: utf8:Hello World
                var utf8Match = Utf8Pattern.Match(input);
                if (utf8Match.Success)
                {
                    result = Encoding.UTF8.GetBytes(utf8Match.Groups[1].Value);
                    return true;
                }

                // Try ASCII format: ascii:Hello World
                var asciiMatch = AsciiPattern.Match(input);
                if (asciiMatch.Success)
                {
                    result = Encoding.ASCII.GetBytes(asciiMatch.Groups[1].Value);
                    return true;
                }

                return false;
            }
            catch
            {
                result = null;
                return false;
            }
        }

        /// <summary>
        /// Recursively processes an object and converts any string values that match
        /// byte array signatures to actual byte[] objects.
        /// </summary>
        public static object ProcessObject(object obj)
        {
            return obj switch
            {
                null => null,
                string str when TryConvertString(str, out var bytes) => bytes,
                IDictionary<string, object> dict => ProcessDictionary(dict),
                Array array => ProcessArray(array),
                IList list => ProcessList(list),
                string str => str,
                _ => obj
            };
        }

        /// <summary>
        /// Processes a dictionary and converts any byte array strings in values.
        /// </summary>
        public static Dictionary<string, object> ProcessDictionary(IDictionary<string, object> dictionary)
        {
            var result = new Dictionary<string, object>();
            foreach (var kvp in dictionary)
            {
                result[kvp.Key] = ProcessObject(kvp.Value);
            }
            return result;
        }

        /// <summary>
        /// Processes a list and converts any byte array strings in elements.
        /// </summary>
        public static List<object> ProcessList(IList list)
        {
            var result = new List<object>();
            foreach (var item in list)
            {
                result.Add(ProcessObject(item));
            }
            return result;
        }

        /// <summary>
        /// Processes an array and converts any byte array strings in elements.
        /// </summary>
        public static Array ProcessArray(Array array)
        {
            var result = new object[array.Length];
            for (int i = 0; i < array.Length; i++)
            {
                result[i] = ProcessObject(array.GetValue(i));
            }
            return result;
        }

        /// <summary>
        /// Converts a byte array back to a string signature for JSON serialization.
        /// </summary>
        public static string BytesToBase64Signature(byte[] bytes)
        {
            if (bytes == null) return null;
            return $"base64:{Convert.ToBase64String(bytes)}";
        }

        /// <summary>
        /// Converts a byte array back to a hex string signature for JSON serialization.
        /// </summary>
        public static string BytesToHexSignature(byte[] bytes)
        {
            if (bytes == null) return null;
            return $"hex:{Convert.ToHexString(bytes)}";
        }

        /// <summary>
        /// Converts a byte array back to a bytes array signature for JSON serialization.
        /// </summary>
        public static string BytesToBytesSignature(byte[] bytes)
        {
            if (bytes == null) return null;
            return $"bytes:[{string.Join(",", bytes)}]";
        }

        /// <summary>
        /// Gets information about supported byte array formats.
        /// </summary>
        public static Dictionary<string, string> GetSupportedFormats()
        {
            return new Dictionary<string, string>
            {
                ["base64"] = "base64:SGVsbG8gV29ybGQ= (Base64 encoded data)",
                ["hex"] = "hex:48656C6C6F20576F726C64 (Hexadecimal string)",
                ["hex_spaced"] = "hex:48 65 6C 6C 6F 20 57 6F 72 6C 64 (Hex with spaces)",
                ["hex_hyphenated"] = "hex:48-65-6C-6C-6F-20-57-6F-72-6C-64 (Hex with hyphens)",
                ["bytes"] = "bytes:[72,101,108,108,111,32,87,111,114,108,100] (Comma-separated byte values)",
                ["utf8"] = "utf8:Hello World (UTF-8 encoded string)",
                ["ascii"] = "ascii:Hello World (ASCII encoded string)"
            };
        }

        /// <summary>
        /// Validates if a string matches any of the supported byte array signatures.
        /// </summary>
        public static bool IsValidByteArraySignature(string input)
        {
            return TryConvertString(input, out _);
        }

        /// <summary>
        /// Gets the format type of a byte array signature string.
        /// </summary>
        public static string GetSignatureFormat(string input)
        {
            if (string.IsNullOrEmpty(input)) return null;

            if (Base64Pattern.IsMatch(input)) return "base64";
            if (HexPattern.IsMatch(input)) return "hex";
            if (BytesPattern.IsMatch(input)) return "bytes";
            if (Utf8Pattern.IsMatch(input)) return "utf8";
            if (AsciiPattern.IsMatch(input)) return "ascii";

            return null;
        }
    }
}
