{"nodes": [{"id": 1, "name": "plc_keg", "type": "plc", "enabled": true, "config": {"ip": "***********", "rack": 0, "slot": 2, "poll_interval": 200, "dbs": [{"topic": "plc.db98", "db": 98, "length": 50, "poll": true}]}}, {"id": 3, "name": "decoder", "type": "decoder", "enabled": true, "config": {"schemas": {"flir": {"floats": {"$array": 5, "$refs": "be.real"}}, "ircheck": {"bools": {"$array": 16, "$refs": "s7.bit"}, "tw2000_help": "s7.word", "kegtype": {"$array": 16, "$refs": "s7.bit"}, "words": {"$array": 6, "$refs": "s7.word"}, "ints": {"$array": 16, "$refs": "s7.int"}}}, "decode": [{"in_topic": "plc.db98", "schema": "<PERSON><PERSON><PERSON>", "out_topic": "plc.irdata"}, {"in_topic": "flir.data", "schema": "flir", "out_topic": "flir.temperatures"}]}}, {"id": 4, "type": "script", "name": "keg_processor", "config": {"topics": ["plc.irdata", "flir.temperatures"], "script": "Moortgat/IRCheckVl2"}}, {"id": 5, "type": "influx", "name": "influx_vl", "config": {"url": "http://localhost:8086", "database": "moortgat_vl2", "retention": "autogen", "topics": ["influx.i<PERSON><PERSON>"], "batch_size": 1000, "flush_interval": 1000}}, {"id": 6, "type": "modbus", "name": "flir", "config": {"ip": "************:502", "start_register": 1017, "length": 10, "topic": "flir.data", "poll_interval": 200}}]}