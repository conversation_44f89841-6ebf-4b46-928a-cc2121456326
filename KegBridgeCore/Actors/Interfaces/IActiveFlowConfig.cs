using System;
using System.Collections.Generic;

namespace KegBridgeCore.Actors.Interfaces;

public interface IActiveFlowConfig
{
    public Guid Id { get; }
    public string Name { get; }
    public bool Enabled { get; }
    public IList<IActiveNodeConfig> Nodes { get; }
    public IList<IActiveWireConfig> Wires { get; }
}

public interface IActiveNodeConfig
{
    public Guid Id { get; }
    public string Name { get; }
    public bool Enabled { get; }
    public int X { get; }
    public int Y { get; }
}

public interface IActiveWireConfig
{
    public Guid Id { get; }
    public string From{ get; }
    public Guid FromId { get; }
    public string To { get; }
    public Guid ToId { get; }
    public string Filter { get; }
}