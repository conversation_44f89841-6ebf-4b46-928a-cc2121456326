using System;
using System.Collections.Generic;

namespace KegBridgeCore.Actors.Interfaces;

public interface IActiveNodeInstance : INodeInstance
{
    // inherits Id Type Name Description Config Enabled
    public List<DateTime> CreatedAt { get; }
    public List<DateTime> TerminatedAt { get; }
    public class Failure
    {
        public DateTime FailureAt { get; set; }
        public Exception FailureException { get; set; }
    }
    public List<Failure> Failures { get; }
}