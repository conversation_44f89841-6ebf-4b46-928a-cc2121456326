using System.Collections.Generic;

namespace KegBridgeCore.Actors.Interfaces;

public interface IFlowConfig
{
    public bool Enabled { get; }
    public IList<INodeConfig> Nodes { get; }
    public IList<IWireConfig> Wires { get; }
}

public interface INodeConfig
{
    public string Type { get; }
    public string Name { get; }
    public bool Enabled { get; }
    public object Config { get; }
    public int X { get; }
    public int Y { get; }
}
public interface IWireConfig
{
    public string From { get; }
    public string To { get; }
    public string Filter { get; }
}