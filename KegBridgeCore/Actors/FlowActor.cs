using Akka.Actor;
using Akka.Event;
using Akka.Logger.Serilog;
using KegBridgeCore.Actors.Interfaces;
using KegBridgeCore.Actors.Messages;
using KegBridgeCore.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using KegBridgeCore.Data;
using KegBridgeCore.Nodes;
using static KegBridgeCore.Actors.ConfigManager;

namespace KegBridgeCore.Actors;

public class FlowActor : ReceiveActor
{
    private readonly ILoggingAdapter _contextLogger = Context.GetLogger<SerilogLoggingAdapter>();

    public class ActiveFlowConfig : IActiveFlowConfig
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public bool Enabled { get; set; }

        public List<ActiveNodeConfig> _Nodes { get; set; } = new();
        public List<ActiveWireConfig> _Wires { get; set; } = new();

        public IList<IActiveNodeConfig> Nodes => _Nodes.Cast<IActiveNodeConfig>().ToList();
        public IList<IActiveWireConfig> Wires => _Wires.Cast<IActiveWireConfig>().ToList();
    }

    public class ActiveNodeConfig : IActiveNodeConfig
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public bool Enabled { get; set; }
        public object Config { get; set; }
        public string JsonConfig { get; set; }
        public int X { get; set; } = 0; // Default position
        public int Y { get; set; } = 0; // Default position
    }

    public class ActiveWireConfig : IActiveWireConfig
    {
        public Guid Id { get; set; }
        public string From { get; set; }
        public Guid FromId { get; set; }
        public string To { get; set; }
        public Guid ToId { get; set; }
        public string Filter { get; set; }
    }

    private Dictionary<Guid, IActorRef> NodeActors { get; set; } = new();
    private Dictionary<string, ActiveNodeConfig> NodesByName { get; set; } = new ();
    private Dictionary<Guid, ActiveNodeConfig> NodesById { get; set; } = new ();
        
    private class WireDestination
    {
        public IActorRef To { get; set; }
        public string Name { get; set; }
        public string Filter { get; set; }
    }
    private Dictionary<IActorRef,List<WireDestination>> Wires { get; set; } = new ();
        
    private ActiveFlowConfig ActiveConfig { get; set; } = new ();
    private INodeRepository NodeRepository { get; }
    private record MsgStartActiveNodes();

    public FlowActor(INodeRepository nodeRepository)
    {
        NodeRepository = nodeRepository;

        Receive<Terminated>(msg => HandleTerminated(msg));
        Receive<MsgNewFlow>(msg => HandleNewFlow(msg));
        Receive<MsgGetActiveFlow>(msg => HandleGetActiveFlow(msg));
        Receive<MsgGetNodeConfig>(msg => HandleGetNodeConfig(msg));
        Receive<MsgStartActiveNodes>(msg => StartActiveNodes());
        Receive<Data.NodeEvent>(msg => HandleNodeEvent(msg));
    }

    private void HandleNewFlow(MsgNewFlow msg)
    {
        _contextLogger.Info("Creating new flow {id} {name}", msg.Id, msg.Name);

        ActiveConfig.Id = msg.Id;
        ActiveConfig.Name = msg.Name;
        ActiveConfig.Enabled = msg.Config.Enabled;

        // setup nodes

        foreach (var nodeConfig in msg.Config.Nodes)
        {
            _contextLogger.Debug("Node: {nodeType} {nodeName} {config}", nodeConfig.Type, nodeConfig.Name, nodeConfig.Config);

            if (nodeConfig.Name != null && NodeRepository.ContainsType(nodeConfig.Type))
            {
                object typedConfig = null;
                string jsonConfig = String.Empty;

                var nodeConfigType = NodeRepository.ConfigType(nodeConfig.Type);
                if (nodeConfigType != null)
                {
                    if (nodeConfig.Config == null)
                    {
                        _contextLogger.Error("Node {nodeName} in flow {flowName} has no configuration defined.",
                            nodeConfig.Name, ActiveConfig.Name);
                        typedConfig = JsonConvert.DeserializeObject("{}", nodeConfigType);
                    }
                    else
                    {
                        jsonConfig = JsonConvert.SerializeObject(nodeConfig.Config);
                        typedConfig = JsonConvert.DeserializeObject(jsonConfig, nodeConfigType);
                    }
                }

                var activeNodeConfig = new ActiveNodeConfig
                {
                    Id = Guid.NewGuid(), // Assuming each node gets a new unique ID in the flow in this run
                    Name = nodeConfig.Name,
                    Type = nodeConfig.Type,
                    Enabled = nodeConfig.Enabled,
                    Config = typedConfig,
                    JsonConfig = jsonConfig,
                    X = nodeConfig.X,
                    Y = nodeConfig.Y 
                };


                ActiveConfig._Nodes.Add(activeNodeConfig);
                NodesByName[activeNodeConfig.Name] = activeNodeConfig;
                NodesById[activeNodeConfig.Id] = activeNodeConfig;
                CreateNodeActor(activeNodeConfig);
            }
            else
            {
                _contextLogger.Error("Node type {nodeType} not found or invalid name {nodeName}", nodeConfig.Type, nodeConfig.Name);
            }
        }

        // setup routing

        Wires.Clear();
        ActiveConfig._Wires.Clear();
        foreach (var wireConfig in msg.Config.Wires)
        {
            _contextLogger.Debug("Wire: {from} {to} {filter}", wireConfig.From, wireConfig.To, wireConfig.Filter);

            if (!NodesByName.ContainsKey(wireConfig.From) || !NodesByName.ContainsKey(wireConfig.To))
            {
                _contextLogger.Error("Wire configuration error: From node {from} or To node {to} not found in flow {flowName}",
                    wireConfig.From, wireConfig.To, ActiveConfig.Name);
                continue;
            }
                
            var fromNode = NodesByName[wireConfig.From];
            var toNode = NodesByName[wireConfig.To];

            var fromActor = NodeActors[fromNode.Id];
            var toActor = NodeActors[toNode.Id];

            if( ! Wires.TryGetValue(fromActor, out var destinations) )
            {
                destinations = new List<WireDestination>();
                Wires[fromActor] = destinations;
            }
            destinations.Add(new WireDestination
            {
                To = toActor,
                Name = toNode.Name,
                Filter = wireConfig.Filter
            });

            ActiveConfig._Wires.Add(new ActiveWireConfig
            {
                Id = Guid.NewGuid(), // Assuming each node gets a new unique ID
                From = wireConfig.From,
                FromId = fromNode.Id,
                To = wireConfig.To,
                ToId = toNode.Id,
                Filter = wireConfig.Filter
            });
        }

        if (ActiveConfig.Enabled)
        {
            _contextLogger.Info("Flow {flowName} is enabled, starting active nodes", ActiveConfig.Name);
            Self.Tell(new MsgStartActiveNodes());
        } 
        else
        {
            _contextLogger.Info("Flow {flowName} is disabled.", ActiveConfig.Name);
        }
    }

    private void StartActiveNodes()
    {
        // now start all enabled active nodes
        foreach (var activeNodeConfig in ActiveConfig._Nodes)
        {
            if (activeNodeConfig.Enabled)
            {
                _contextLogger.Info("Enabling node {nodeName} with ID {nodeId}", activeNodeConfig.Name, activeNodeConfig.Id);
                NodeActors[activeNodeConfig.Id].Tell(new MsgNodeStart(activeNodeConfig.Id));
            }
            else
            {
                _contextLogger.Info("Node {nodeName} is disabled", activeNodeConfig.Name);
            }
        }
    }

    private void CreateNodeActor(ActiveNodeConfig activeNodeConfig)
    {
        var logger = _contextLogger.ForContext("Method", "CreateNodeActor");

        if (!NodeActors.ContainsKey(activeNodeConfig.Id))
        {
            IActorRef actorRef = NodeRepository.CreateNode(Context,
                activeNodeConfig.Type,
                activeNodeConfig.Id,
                activeNodeConfig.Name);

            if (actorRef != null)
            {
                _contextLogger.Info("Node actor created for {nodeName} with ID {nodeId}", activeNodeConfig.Name, activeNodeConfig.Id);
                Context.Watch(actorRef);
                NodeActors[activeNodeConfig.Id] = actorRef;
            }
            else
            {
                _contextLogger.Error("Failed to create node actor for {nodeName}", activeNodeConfig.Name);
            }
        }
        else
        {
            _contextLogger.Error("Node {nodeName} already created", activeNodeConfig.Name);
        }
    }

    private void HandleNodeEvent(Data.NodeEvent nodeEvent)
    {
        _contextLogger.Debug("HandleNodeEvent {@nodeEvent}", nodeEvent);
        
        var senderNode = SenderNode(nodeEvent);
        
        if (senderNode == null)
        {
            _contextLogger.Error("Sender {SenderActor} not found for event {NodeEventId} {NodeEventNodeName}", Sender, nodeEvent.Id, nodeEvent.Node);
            return;
        }

        _contextLogger.Debug("Routing event {NodeEventId} from {NodeName}", nodeEvent.Id, senderNode.Name);
        if( Wires.TryGetValue(NodeActors[senderNode.Id], out var destinations) )
        {
            foreach( var destination in destinations)
            {
                _contextLogger.Debug("Sending event {NodeEventId} to {NodeName}", nodeEvent.Id, destination.Name);
                destination.To.Forward(nodeEvent);
            }
        } 
        else
        {
            _contextLogger.Warning("No wires found for sender {sender}", Sender);
        }
    }

    private void HandleGetActiveFlow(MsgGetActiveFlow msg)
    {
        _contextLogger.Info("HandleGetFlow called for {id}", msg.Id);
        if (msg.Id == ActiveConfig.Id)
        {
            Sender.Tell(ActiveConfig);
        }
        else
        {
            _contextLogger.Error("Received id {id} mismatch", msg.Id);
            Sender.Tell(null);
        }
    }

    private ActiveNodeConfig SenderNode(NodeEvent nodeEvent)
    {
        Guid? nodeId = NodeActors.FirstOrDefault(x => x.Value.Equals(Context.Sender)).Key;
        if (NodesById.ContainsKey((Guid)nodeId))
        {
            return NodesById[(Guid)nodeId];
        }
        else
        {
            if (Context.Sender.Equals(Context.Parent))
            {
                if(NodesByName.ContainsKey(nodeEvent.Node))
                {
                    return NodesByName[nodeEvent.Node];
                }
            }
        }
        _contextLogger.Error("Unknown node for event id {NodeEventId} node {NodeName} from sender {actorRef} ", nodeEvent.Id, nodeEvent.Node, Context.Sender);
        return null;
    }

    private void HandleGetNodeConfig(MsgGetNodeConfig msg)
    {
        _contextLogger.Info("HandleGetNodeConfig");

        Guid? nodeId = NodeActors.FirstOrDefault(x => x.Value.Equals(Context.Sender)).Key;

        if (nodeId != null && NodesById.ContainsKey((Guid)nodeId))
        {
            Sender.Tell(
                new MsgNodeConfig(
                    (Guid)nodeId,
                    NodesById[(Guid)nodeId].Enabled,
                    NodesById[(Guid)nodeId].Config
                )
            );
        }
        else
        {
            _contextLogger.Error("Unknown node config request for {actorRef}", Sender);
        }
    }

    private void HandleTerminated(Terminated terminated)
    {
        _contextLogger.Warning("Node actor terminated {actorRef}", terminated.ActorRef);
            
        var nodeId = NodeActors.FirstOrDefault(x => x.Value.Equals(terminated.ActorRef)).Key;
        if( nodeId != Guid.Empty )
        {
            _contextLogger.Warning("Node actor {name} terminated with ID {nodeId}", nodeId);
        }
    }
    
    protected override SupervisorStrategy SupervisorStrategy()
    {
        return new OneForOneStrategy(
            maxNrOfRetries: 5,
            withinTimeMilliseconds: 60000,
            loggingEnabled: false,
            localOnlyDecider: exception =>
            {
                Guid nodeId = NodeActors.FirstOrDefault(x => x.Value.Equals(Context.Sender)).Key;

                if( nodeId != Guid.Empty && NodesById.ContainsKey((Guid)nodeId) )
                {
                    var activeNode = NodesById[(Guid)nodeId];

                    if (exception is NodeRestartException)
                    {
                        _contextLogger
                            .ForContext("NodeName", activeNode.Name)
                            .ForContext("NodeId", activeNode.Id)
                            .Info("Configuration changed");
                    }
                    else
                    {
                        // activeNode.Failures.Insert(0, new IActiveNodeInstance.Failure()
                        // {
                        //     FailureAt = DateTime.UtcNow,
                        //     FailureException = exception
                        // });
                        
                        _contextLogger
                            .ForContext("NodeName", activeNode.Name)
                            .ForContext("NodeId", activeNode.Id)
                            .Error(exception, "Node unhandled exception");
                    }
                }
                return Directive.Restart;
            });
    }
}