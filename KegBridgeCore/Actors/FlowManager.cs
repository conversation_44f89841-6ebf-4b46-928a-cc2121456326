using Akka.Actor;
using Akka.DependencyInjection;
using Akka.Event;
using Akka.Logger.Serilog;
using KegBridgeCore.Actors.Interfaces;
using KegBridgeCore.Actors.Messages;
using Microsoft.DotNet.Scaffolding.Shared.Project;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Akka.Streams.Attributes;

namespace KegBridgeCore.Actors;

public class FlowManager : ReceiveActor
{
    private readonly ILoggingAdapter _contextLogger = Context.GetLogger<SerilogLoggingAdapter>();

    private record struct Flow(Guid Id, string Name, IActorRef ActorRef) : IActiveFlow;

    private Dictionary<Guid, Flow> _flows = new Dictionary<Guid, Flow>();

    public FlowManager()
    {
        _contextLogger.Info("FlowManager actor started");
        Receive<MsgNewFlow>(msg => HandleNewFlow(msg));
            
        ReceiveAsync<MsgGetActiveFlows>(async msg =>
        {
            _contextLogger.Info("HandleGetFlows");
            await Task.CompletedTask;
            Sender.Tell(new List<IActiveFlow>(_flows.Values.Cast<IActiveFlow>()));
        });
        Receive<MsgGetActiveFlow>(msg => HandleGetFlow(msg));
        Receive<Data.NodeEvent>(msg =>
        {
            _contextLogger.Info("HandleNodeEvent");
            foreach (var flow in _flows.Values)
            {
                flow.ActorRef.Tell(msg);
            }
        });
    }

    private void HandleNewFlow(MsgNewFlow msg)
    {
        _contextLogger.Info("HandleNewFlow for {id} {name}", msg.Id, msg.Name);
        // Logic to handle the creation of a new flow
        // This could involve creating a new Flow actor, initializing it with the provided configuration, etc.
        var flowConfig = msg.Config;
        // Example: Create a new Flow actor and pass the configuration

        var nodeProps = DependencyResolver.For(Context.System).Props(typeof(FlowActor));
        var flowActor = Context.ActorOf(nodeProps, msg.Id.ToString());

        _flows[msg.Id] = new Flow
        {
            ActorRef = flowActor,
            Id = msg.Id,
            Name = msg.Name
        };

        // Optionally, you can send the configuration to the newly created Flow actor
        flowActor.Tell(msg);
    }

    private void HandleGetFlow(MsgGetActiveFlow msg)
    {
        _contextLogger.Info("HandleGetFlow called for {id}", msg.Id);

        if (_flows.TryGetValue(msg.Id, out var flow))
        {
            flow.ActorRef.Forward(msg);
        }
        else
        {
            _contextLogger.Warning("Flow with id {id} not found", msg.Id);
            Sender.Tell(null); // or throw an exception, or handle it as needed
        }
    }
}