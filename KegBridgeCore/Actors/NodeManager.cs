using Akka.Actor;
using Akka.Event;
using Akka.Hosting;
using Akka.Logger.Serilog;
using KegBridgeCore.Actors.Interfaces;
using KegBridgeCore.Actors.Messages;
using KegBridgeCore.Extensions;
using KegBridgeCore.Nodes;
using KegBridgeCore.Services;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace KegBridgeCore.Actors;

public class NodeManager : ReceiveActor
{
    #region State
    private class ActiveNode : IActiveNodeInstance
    {
        public Guid Id { get; private set; }
        public string Type { get; internal set; }
        public string Name { get; internal set; }
        public string Description { get; internal set; }
        public object Config { get; internal set; }
        public bool Enabled { get; internal set; }

        public List<DateTime> CreatedAt { get; } = [];
        public List<DateTime> TerminatedAt { get; } = [];
        public List<IActiveNodeInstance.Failure> Failures { get; } = new List<IActiveNodeInstance.Failure>();
        public void Created()
        {
            CreatedAt.Insert(0, DateTime.UtcNow);
        }
        public void Terminated()
        {
            TerminatedAt.Insert(0, DateTime.UtcNow);
        }
        public ActiveNode(Guid id, string type, string name, string description, object config, bool enabled)
        {
            Id = id;
            Type = type;
            Name = name;
            Description = description;
            Config = config;
            Enabled = enabled;
        }
    }
    #endregion

    private Dictionary<Guid, ActiveNode> activeNodes = [];
    private Dictionary<Guid, IActorRef>   activeNodesActorRefs = [];
        
    private readonly ILoggingAdapter contextLogger = Context.GetLogger<SerilogLoggingAdapter>();
    private readonly INodeRepository nodeRepository;
    private readonly IActorRef eventRouter;

    public NodeManager(INodeRepository _nodeRepository /*, IRequiredActor<EventRouter> _eventRouter*/)
    {
        nodeRepository = _nodeRepository;
        //eventRouter = _eventRouter.ActorRef;
        Become(Running);
    }

    #region Behaviors
    private void Running()
    {
        Receive<Terminated>(NodeTerminated);

        // incoming from ConfigManager/Web to create/update a node
        Receive<MsgNodeInstance>(UpdateOrCreateNode);

        // incoming from the nodes itself to retrieve it's config
        Receive<MsgGetNodeConfig>(GetNodeConfig);

        // coming from everyhwere
        Receive<MsgGetActiveNodes>(GetActiveNodes);
        Receive<MsgGetActiveNode>(GetActiveNode);

        Receive<MsgNodeStart>(StartNode);
        Receive<MsgNodeStop>(StopNode);
        Receive<MsgNodeRestart>(RestartNode);
        Receive<MsgGetNodeState>(GetNodeState);

        // base message routing hookup
        //Receive<Data.NodeEvent>(e => { eventRouter.Forward(e); });
        //Receive<EventRouter.MsgSubscribe>(e => { eventRouter.Forward(e); });
        //Receive<EventRouter.MsgUnsubscribe>(e => { eventRouter.Forward(e); });
    }

    #endregion

    #region MessageHandlers
    private void NodeTerminated(Terminated terminated)
    {
        var logger = contextLogger.ForContext("Method", "NodeTerminated");

        Guid? nodeId = activeNodesActorRefs.FirstOrDefault(x => x.Value == terminated.ActorRef).Key;

        if( nodeId != null)
        {
            var activeNode = activeNodes[(Guid)nodeId];

            logger.Warning("Node {nodeId} {nodeName} terminated", activeNode.Id, activeNode.Name);

            activeNode.Terminated();
            activeNodesActorRefs.Remove(activeNode.Id);
            activeNodes.Remove(activeNode.Id);
        }
    }

    private void UpdateOrCreateNode(MsgNodeInstance msgNodeSpec)
    {
        var logger = contextLogger.ForContext("Method", "UpdateOrCreateNode");

        //var results = new List<ValidationResult>();
            
        //bool isValid = Validator.TryValidateObject(
        //    nodeConfig.Node,
        //    new ValidationContext(nodeConfig.Node, null, null),
        //    results,
        //    false);

        //dynamic nodeTypeConfig = null;

        //if (isValid)
        //{
        //    if (!nodeRepository.ContainsType(nodeConfig.Node.Type))
        //    {
        //        results.Add(new ValidationResult("Invalid Type", new string[] { "Type" }));
        //        isValid = false;
        //    }
        //    else
        //    {
        //        nodeTypeConfig = nodeConfig.Node.Config.ToObject(nodeRepository.ConfigType(nodeConfig.Node.Type));

        //        isValid = Validator.TryValidateObject(
        //                            nodeTypeConfig,
        //                            new ValidationContext(nodeTypeConfig, null, null),
        //                            results,
        //                            false);

        //    }
        //}
            
        //if( ! isValid )
        //{
        //    logger.Error("Node {node_name}:{node_type} configuration error : {node_errors}", nodeConfig.Node.Name, nodeConfig.Node.Type, results);
        //    return;
        //}

        // does node exist ?
        if (!activeNodes.ContainsKey(msgNodeSpec.Id))
        {
            // new node double check type (config mgr also does this to check the config)
            if (nodeRepository.ContainsType(msgNodeSpec.Type))
            {
                activeNodes[msgNodeSpec.Id] = new ActiveNode(
                    msgNodeSpec.Id,
                    msgNodeSpec.Type,
                    msgNodeSpec.Name,
                    msgNodeSpec.Description,
                    msgNodeSpec.Config,
                    msgNodeSpec.Enabled);

                CreateNodeActor(activeNodes[msgNodeSpec.Id]);

                if (msgNodeSpec.Enabled)
                {
                    Context.System.Scheduler.ScheduleTellOnce(1000, Self, new MsgNodeStart(msgNodeSpec.Id), Self);
                } 
            }
            else
            {
                logger.Error("Unknown node type {nodeType} for {nodeName}", msgNodeSpec.Type, msgNodeSpec.Name);
            }
        }
        else
        {
            // update to existing node
            var activeNode = activeNodes[msgNodeSpec.Id];

            if (activeNode.Name == msgNodeSpec.Name && activeNode.Type == msgNodeSpec.Type)
            {
                var serializedActiveConfig  = JObject.Parse(JsonConvert.SerializeObject(activeNode.Config));
                var serializedNewConfig = JObject.Parse(JsonConvert.SerializeObject(msgNodeSpec.Config));

                if ( serializedActiveConfig != serializedNewConfig )
                {
                    logger.Info("Node {nodeName} config changed.", msgNodeSpec.Name);
                    activeNode.Config = serializedNewConfig.ToObject(nodeRepository.ConfigType(activeNode.Type));
                    activeNode.Enabled = msgNodeSpec.Enabled;

                    if (activeNode.Enabled)
                    {
                        activeNodesActorRefs[msgNodeSpec.Id].Tell(new MsgNodeRestart(msgNodeSpec.Id));
                    }
                }
                else
                {
                    logger.Info("Node {nodeName} config unchanged", msgNodeSpec.Name);
                }
                activeNode.Description = msgNodeSpec.Description;
                activeNode.Enabled = msgNodeSpec.Enabled;
            }
            else
            {
                logger.Warning("TODO Node {activeName}=>{nodeName} {activeType}=>{nodeType} ", activeNode.Name,msgNodeSpec.Name, activeNode.Type, msgNodeSpec.Type);
            }
        }
    }

    private void CreateNodeActor(ActiveNode activeNode)
    {
        var logger = contextLogger.ForContext("Method", "CreateNodeActor");

        if( !activeNodesActorRefs.ContainsKey(activeNode.Id) )
        {
            IActorRef actorRef = nodeRepository.CreateNode(Context, activeNode.Type, activeNode.Id, activeNode.Name);

            Context.Watch(actorRef);

            activeNodesActorRefs[activeNode.Id] = actorRef;
            activeNode.Created();
        }
        else
        {
            logger.Error("Node {nodeName} already created", activeNode.Name);
        }
    }

    private void StartNode(MsgNodeStart nodeStart)
    {
        var logger = contextLogger.ForContext("Method","StartNode");
        logger.Info("{@MsgStartNode}", nodeStart);

        if (activeNodesActorRefs.TryGetValue(nodeStart.Id, out IActorRef nodeActorRef))
        {
            nodeActorRef.Forward(nodeStart);
        }
        else
        {
            logger.Error("Node {nodeId} doesn't exists", nodeStart.Id);
        }
    }
    private void StopNode(MsgNodeStop nodeStop)
    {
        var logger = contextLogger.ForContext("Method", "StopNode");
        logger.Info("{@MsgStopNode}", nodeStop);

        if (activeNodesActorRefs.TryGetValue(nodeStop.Id, out IActorRef nodeActorRef))
        {
            nodeActorRef.Forward(nodeStop);
        }
        else
        {
            logger.Error("Node {nodeId} doesn't exists", nodeStop.Id);
        }
    }
    private void RestartNode(MsgNodeRestart nodeRestart)
    {
        var logger = contextLogger.ForContext("Method", "RestartNode");
        logger.Info("{@MsgRestartNode}", nodeRestart);

        if (activeNodesActorRefs.TryGetValue(nodeRestart.Id, out IActorRef nodeActorRef))
        {
            nodeActorRef.Forward(nodeRestart);
        }
        else
        {
            logger.Error("Node {nodeId} doesn't exists", nodeRestart.Id);
        }
    }
    private void GetNodeState(MsgGetNodeState nodeState)
    {
        var logger = contextLogger.ForContext("Method", "GetNodeState");
        //logger.Debug("{@MsgGetNodeState}", nodeState);

        if (activeNodesActorRefs.TryGetValue(nodeState.Id, out IActorRef nodeActorRef))
        {
            nodeActorRef.Forward(nodeState);
        }
        else
        {
            logger.Error("Node {nodeId} doesn't exists", nodeState.Id);
            Sender.Tell(null);
        }
    }

    // comes from the nodes when they are (re)started to get their config
    private void GetNodeConfig(MsgGetNodeConfig msgGetNodeConfig)
    {
        var logger = contextLogger.ForContext("Method", "GetNodeConfig");

        Guid? nodeId = activeNodesActorRefs.FirstOrDefault(x => x.Value == Context.Sender).Key;

        if (nodeId!=null && activeNodes.ContainsKey((Guid)nodeId))
        {
            Sender.Tell(
                new MsgNodeConfig(
                    (Guid)nodeId,
                    activeNodes[(Guid)nodeId].Enabled,
                    activeNodes[(Guid)nodeId].Config
                )
            );
        }
        else
        {
            logger.Error("Unknown node request config {actorRef}", Sender);
        }
    }

    private void GetActiveNodes(MsgGetActiveNodes msgGetActiveNodes)
    {
        Sender.Tell(activeNodes.Values as IEnumerable<IActiveNodeInstance>);
    }

    private void GetActiveNode(MsgGetActiveNode msgGetActiveNode)
    {
        var logger = contextLogger.ForContext("Method", "GetActiveNode");
        //logger.Debug("{@MsgGetNodeState}", nodeState);

        if (activeNodes.TryGetValue(msgGetActiveNode.Id, out ActiveNode activeNode))
        {
            Sender.Tell(activeNode as IActiveNodeInstance);
        }
        else
        {
            logger.Error("Node {nodeId} doesn't exists", msgGetActiveNode.Id);
            Sender.Tell(null);
        }
    }

    #endregion

    protected override SupervisorStrategy SupervisorStrategy()
    {
        return new OneForOneStrategy(
            maxNrOfRetries: 5,
            withinTimeMilliseconds: 60000,
            loggingEnabled: false,
            localOnlyDecider: exception =>
            {
                Guid? nodeId = activeNodesActorRefs.FirstOrDefault(x => x.Value == Context.Sender).Key;

                if( nodeId != null && activeNodes.ContainsKey((Guid)nodeId) )
                {
                    var activeNode = activeNodes[(Guid)nodeId];

                    if (exception is NodeRestartException)
                    {
                        contextLogger
                            .ForContext("NodeName", activeNode.Name)
                            .ForContext("NodeId", activeNode.Id)
                            .Info("Configuration changed");
                    }
                    else
                    {
                        activeNode.Failures.Insert(0, new IActiveNodeInstance.Failure()
                        {
                            FailureAt = DateTime.UtcNow,
                            FailureException = exception
                        });
                        contextLogger
                            .ForContext("NodeName", activeNode.Name)
                            .ForContext("NodeId", activeNode.Id)
                            .Error(exception, "Node unhandled exception");
                    }
                }
                return Directive.Restart;
            });
    }
}