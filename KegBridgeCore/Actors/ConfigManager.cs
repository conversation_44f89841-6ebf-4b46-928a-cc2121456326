using Akka.Actor;
using Akka.Event;
using Akka.Hosting;
using Akka.Logger.Serilog;
using Akka.Streams.Dsl;
using KegBridgeCore.Actors.Interfaces;
using KegBridgeCore.Actors.Messages;
using KegBridgeCore.Services;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;
using static KegBridgeCore.Actors.ConfigManager;

namespace KegBridgeCore.Actors;

public class ConfigManager : ReceiveActor
{
    public class FlowConfig : IFlowConfig
    {
        public bool Enabled { get; set; } = true;
        public List<NodeConfig> _Nodes { get; set; } = new();
        public List<WireConfig> _Wires { get; set; } = new();

        public IList<INodeConfig> Nodes => _Nodes.Cast<INodeConfig>().ToList();
        public IList<IWireConfig> Wires => _Wires.Cast<IWireConfig>().ToList();
    }

    public class NodeConfig : INodeConfig
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public bool Enabled { get; set; } = true;
        public object Config { get; set; }
        public int X { get; set; } = 0;
        public int Y { get; set; } = 0;
    }

    public class WireConfig : IWireConfig
    {
        public string From { get; set; }
        public string To { get; set; }
        public string Filter { get; set; }
    }

    #region Messages
    internal class MsgReadFlows
    {
        public MsgReadFlows()
        {
        }
    }
    #endregion

    #region State
    #endregion

    private readonly ILoggingAdapter _contextLogger = Context.GetLogger<SerilogLoggingAdapter>();
    private readonly string _flowFolder;

    private readonly YamlDotNet.Serialization.IDeserializer _deserializer;
    private readonly YamlDotNet.Serialization.ISerializer _serializer;

    private INodeRepository _nodeRepository;
    private FileSystemWatcher _configFileWatcher;
    private readonly IActorRef _flowManager;
    //private bool ReloadConfig;
    private Dictionary<string, Guid> _configuredFlows = new ();

    public ConfigManager(string folder, IRequiredActor<FlowManager> flowManager, INodeRepository nodeRepository)
    {

        _flowFolder = folder;

        if (Path.IsPathRooted(folder))
        {
            _flowFolder = Path.GetFullPath(folder);
        }
        else
        {
            _flowFolder = Path.Combine(Directory.GetCurrentDirectory(), folder);
        }
        if (!Directory.Exists(_flowFolder))
        {
            Directory.CreateDirectory(_flowFolder);
        }

        _deserializer = new DeserializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .Build();

        _serializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .Build();

        _nodeRepository = nodeRepository;

        _flowManager = flowManager.ActorRef;


        Receive<MsgReadFlows>(ReadFlows);

        Self.Tell(new MsgReadFlows());
    }

    private IEnumerable<string> GetAllFlowIds() =>
        Directory.GetFiles(_flowFolder, "*.yaml")
            .Select(Path.GetFileNameWithoutExtension);

    private string ExpandEnvVariables(string input)
    {
        return Regex.Replace(input, @"\$\{(\w+)\}", match =>
        {
            var varName = match.Groups[1].Value;
            return Environment.GetEnvironmentVariable(varName) ?? match.Value;
        });
    }

    // Read all flows from the configured folder on startup
    //
    private void ReadFlows(MsgReadFlows msgReadConfig)
    {
        var logger = _contextLogger.ForContext("Method", "ReadFlows");

        logger.Info("Reading flow configurations from {flowFolder}", _flowFolder);

        foreach (var flowName in GetAllFlowIds())
        {
            var flowConfig = ReadFlow(flowName);

            if (flowConfig == null)
            {
                logger.Error("Failed to read flow configuration for {flowName}", flowName);
                continue;
            }

            var flowId = Guid.NewGuid(); // Generate a new flow ID
            _configuredFlows[flowName] = flowId;

            // Launch the new flow
            _flowManager.Tell(new MsgNewFlow(flowId, flowName, flowConfig));
        }

        // once all files are read, we can start the watcher
        StartConfigFileWatcher();
    }

    private void StartConfigFileWatcher()
    {
        _configFileWatcher = new FileSystemWatcher
        {
            Path = _flowFolder,
            IncludeSubdirectories = true,
            NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName,
            Filter = "*.*"
        };

        //IActorRef MySelf = Self;
        //IScheduler MyScheduler = Context.System.Scheduler;

        FileSystemEventHandler s = (s, e) =>
        {
            WatcherChangeTypes wct = e.ChangeType;
            _contextLogger.Debug("File {file} changed with type {changeType}", e.FullPath, wct.ToString());

            //if (e.Name == configFilename && !ReloadConfig)
            //{
            //    contextLogger.Info("Configuration changed.");
            //    MyScheduler.ScheduleTellOnce(200, MySelf, new MsgConfigReload(), MySelf);
            //    ReloadConfig = true;
            //}
        };

        RenamedEventHandler sr = (s, e) =>
        {
            WatcherChangeTypes wct = e.ChangeType;
            _contextLogger.Debug("File {file} renamed to {newfile} changed with type {changeType}", e.OldFullPath, e.FullPath, wct.ToString());
                
            //if (e.Name == configFilename && !ReloadConfig)
            //{
            //    contextLogger.Info("Configuration changed.");
            //    MyScheduler.ScheduleTellOnce(200, MySelf, new MsgConfigReload(), MySelf);
            //    ReloadConfig = true;
            //}
        };

        _configFileWatcher.Changed += s;
        _configFileWatcher.Renamed += sr;
        _configFileWatcher.Created += s;
        _configFileWatcher.Deleted += s;

        _configFileWatcher.EnableRaisingEvents = true;
    }

    private FlowConfig ReadFlow(string flowName)
    {
        var logger = _contextLogger.ForContext("Method", "ReadFlow");
        try
        {
            var flowPath = Path.Combine(_flowFolder, $"{flowName}.yaml");
            var flowYaml = ExpandEnvVariables(File.ReadAllText(flowPath));

            var flowConfig = _deserializer.Deserialize<FlowConfig>(flowYaml);
            if (flowConfig == null)
            {
                logger.Error("Failed to deserialize flow configuration from {path}", flowPath);
                return null;
            }
            logger.Debug("Loaded flow {flowName} with {nodeCount} nodes and {wireCount} wires",
                flowName, flowConfig.Nodes.Count, flowConfig.Wires.Count);
                
            return flowConfig;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error loading flow {flowName}", flowName);
        }

        return null;
    }
}