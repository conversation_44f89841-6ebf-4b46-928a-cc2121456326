using KegBridgeCore.Actors.Interfaces;
using System;

namespace KegBridgeCore.Actors.Messages;

public class MsgNodeInstance : INodeInstance
{
    public Guid Id { get; private set; }
    public string Type { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public object Config { get; private set; }
    public bool Enabled { get; private set; }

    public MsgNodeInstance(Guid id, string type, string name, string description, object config, bool enabled)
    {
        Id = id;
        Type = type;
        Name = name;
        Description = description;
        Config = config;
        Enabled = enabled;
    }
}