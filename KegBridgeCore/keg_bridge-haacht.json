{
  "nodes": [
    {
      "id": 1,
      "name": "plc_keg",
      "type": "plc",
      "enabled": true,
      "config": {
        "ip": "*************",
        //"ip": "*************",
        "rack": 0,
        "slot": 1,
        "poll_interval": 100,
        //"poll_interval": 1000,
        "dbs": [
          {
            "topic": "plc.general_io",
            "db": 10310,
            "length": 124,
            "poll": true
          },
          {
            "topic": "plc.transport",
            "db": 10305,
            "length": 1760,
            "poll": true
          },
          {
            "topic": "plc.filler_process",
            "db": 10301,
            "length": 1760,
            "poll": true
          },
          {
            "topic": "plc.filler_cip",
            "db": 10302,
            "length": 704,
            "poll": true
          },
          {
            "topic": "plc.filler_purge",
            "db": 10303,
            "length": 704,
            "poll": true
          },
          {
            "topic": "plc.tanks",
            "db": 10304,
            "length": 704,
            "poll": true
          },
          {
            "topic": "plc.safety",
            "db": 10320,
            "length": 124,
            "poll": false
          }
        ]
      }
    }, // PLC KEG
    {
      "id": 2,
      "name": "plc_flash",
      "type": "plc",
      "enabled": true,
      "config": {
        "ip": "*************",
        "rack": 0,
        "slot": 1,
        "poll_interval": 100,
        "dbs": [
          {
            "topic": "flash.general_io",
            "db": 27,
            "length": 678,
            "poll": true
          }
        ]
      }
    }, // PLC FLASH
    {
      "id": 3,
      "name": "decoder",
      "type": "decoder",
      "enabled": true,
      "config": {
        "schemas": {
          "udt_kb_ce": {
            "counter": "s7.dint",
            "seq": {
              "msg_id": "s7.int",
              "msg_step": "s7.int",
              "msg_stat": "s7.int",
              "active_step": "s7.int",
              "val": "s7.real"
            },
            "ce_io": {
              "di_do": "s7.dword",
              "values": {
                "$array": 5,
                "$refs": "s7.real"
              }
            },
            "ce_status": {
                "Action": "s7.int",
                "Status": "s7.int"
            }
          },
          "flash": {
            "analog_in": {
              "$array": 51,
              "$refs": "s7.real"    
            },
            "valve_status": {
              "$array": 131,
              "$refs": "s7.word"    
            },
            "analog_out": {
              "$array": 31,
              "$refs": "s7.real"    
            },
            "analog_out_status": {
              "$array": 31,
              "$refs": "s7.word"    
            },
            "digital_out": "le.dword", 
            "actual_step": {
              "$array": 11,
              "$refs": "s7.int" 
            }  
          },
          "transport": {
            "ce": {
              "$array": 40,
              "$refs": "udt_kb_ce"
            }
          },
          "general": {
            "watchdog_in": "s7.int",
            "watchdog_out": "s7.int",
            "modes": {
              "retour": "s7.int",
              "ow": "s7.int"
            },
            "kegtypes": {
              "infeed": "s7.int",
              "pw": "s7.int",
              "soaking": "s7.int",
              "mw": "s7.int",
              "sterilization": "s7.int",
              "f": "s7.int",
              "outfeed": "s7.int",
              "_0": "s7.int",
              "_1": "s7.int",
              "_2": "s7.int",
              "ow": "s7.int"
            },
            "bits": {
              "$array": 2,
              "$refs": "le.dword"
            },
            "reals": {
              "$array": 16,
              "$refs": "s7.real"
            },
            "ints": {
              "$array": 11,
              "$refs": "s7.int"
            }
          },
          "safety": {
            "ce": {
              "$array": 62,
              "$refs": "s7.word"
            }
          },
          "tanks": {
            "ce": {
              "$array": 16,
              "$refs": "udt_kb_ce"
            }
          },
          "filler_process": {
            "ce": {
              "$array": 40,
              "$refs": "udt_kb_ce"
            }
          },
          "filler_cip": {
            "ce": {
              "$array": 16,
              "$refs": "udt_kb_ce"
            }
          },
          "filler_purge": {
            "ce": {
              "$array": 16,
              "$refs": "udt_kb_ce"
            }
          }

        },
        "decode": [
          {
            "in_topic": "plc.general_io",
            "schema": "general",
            "out_topic": "db.general_io"
          },
          {
            "in_topic": "plc.transport",
            "schema": "transport",
            "out_topic": "db.transport"
          },
          {
            "in_topic": "plc.filler_process",
            "schema": "filler_process",
            "out_topic": "db.filler_process"
          },
          {
            "in_topic": "plc.filler_purge",
            "schema": "filler_purge",
            "out_topic": "db.filler_purge"
          },
          {
            "in_topic": "plc.filler_cip",
            "schema": "filler_cip",
            "out_topic": "db.filler_cip"
          },
          {
            "in_topic": "plc.tanks",
            "schema": "tanks",
            "out_topic": "db.tanks"
          },
          {
            "in_topic": "plc.safety",
            "schema": "safety",
            "out_topic": "db.safety"
          },
          {
            "in_topic": "flash.general_io",
            "schema": "flash",
            "out_topic": "db.flash"
          }
        ]
      }
    }, // DECODER
    {
      "id": 4,
      "type": "script",
      "name": "keg_processor",
      "config": {
        "remoteDebug": true,
        "topics": [
          "db.general_io",
          "db.transport",
          "db.filler_process",
          "db.filler_purge",
          "db.filler_cip",
          "db.tanks",
          "db.safety"
        ],
        "script": "Haacht/KegProcessor"
      }
    }, // SCRIPT KEG
    {
      "id": 5,
      "type": "influx",
      "name": "influx_keg",
      "config": {
        "url": "http://localhost:8086",
        "database": "haacht_vl",
        "retention": "autogen",
        "topics": [
          "influx.keg"
        ],
        "batch_size": 1000,
        "flush_interval": 1000
      }
    }, // INFLUX KEG
    {
      "id": 6,
      "type": "script",
      "name": "flash_processor",
      "config": {
        "topics": [
          "db.flash"
        ],
        "script": "Haacht/FlashProcessor"
      }
    }, // SCRIPT FLASH
    {
      "id": 7,
      "type": "influx",
      "name": "influx_flash",
      "config": {
        "url": "http://localhost:8086",
        "database": "haacht_flash",
        "retention": "autogen",
        "topics": [
          "influx.flash"
        ],
        "batch_size": 1000,
        "flush_interval": 1000
      }
    },  // INFLUX FLASH
    {
      "id": 8,
      "type": "hmi",
      "name": "hmi_100",
      "config": {
        "url": "http://***************",
        "logfile_path": "/StorageCardSD/Logs/Alarm_logCSV0.csv",
        "username": "Lambrechts",
        "password": "2870",
        "poll_interval": 300000
      }
    },
    {
      "type": "robot",
      "name": "depal_111",
      "config": {
        "url": "http://***************",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "pw_113",
      "config": {
        "url": "http://***************",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "mw_115",
      "config": {
        "url": "http://192.168.111.115",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "filler_117",
      "config": {
        "url": "http://192.168.111.117",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "capper_119",
      "config": {
        "url": "http://192.168.111.119",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "pal_121",
      "config": {
        "url": "http://192.168.111.121",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    },
    {
      "type": "robot",
      "name": "ow_123",
      "config": {
        "url": "http://192.168.111.123",
        "errall_path": "/MD/ERRALL.LS",
        "poll_interval": 60000
      }
    }
  ]
}