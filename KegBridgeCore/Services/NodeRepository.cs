using Akka.Actor;
using Akka.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Loader;

namespace KegBridgeCore.Services;

public interface INodeDescription
{
    public string TypeName { get; }
    public string Description { get; }
    public Type NodeType { get; }
    public Type NodeConfigType { get; }
    public Type NodeConfigComponent { get; }
}

public interface INodeRepository
{
    IEnumerable<INodeDescription> AvailableTypes();
    bool ContainsType(string type);
    IActorRef CreateNode(IActorContext _context, string type, Guid id, string name);
    public Type ConfigType(string type);
    public Type NodeType(string type);
    public Type ConfigComponent(string type);
}

public class NodeRepositoryItem : INodeDescription
{
    public string TypeName { get; private set; }
    public string Description { get; private set; }
    public Type NodeConfigType { get; }
    public Type NodeType { get; }
    public Type NodeConfigComponent { get; }

    public NodeRepositoryItem(INodeDescription _nodeDescription )
    {
        TypeName = _nodeDescription.TypeName;
        Description = _nodeDescription.Description;
        NodeConfigType = _nodeDescription.NodeConfigType;
        NodeType = _nodeDescription.NodeType;
        NodeConfigComponent = _nodeDescription.NodeConfigComponent;
    }
}

public class NodeRepository : INodeRepository
{
    private ILogger<NodeRepository> logger;
    private IDictionary<string, INodeDescription> availableNodeTypes = new Dictionary<string, INodeDescription> ();

    public NodeRepository(ILogger<NodeRepository> _logger)
    {
        logger = _logger;
            
        logger.LogInformation("Create NodeRepository");

        var assembliesFromAssemblyLoadContext = AssemblyLoadContext.Default.Assemblies;

        var nodeDescriptionTypes = assembliesFromAssemblyLoadContext
            .SelectMany(x => x.GetTypes())
            .Where(type => !type.IsAbstract && typeof(INodeDescription).IsAssignableFrom(type) && type != typeof(NodeRepositoryItem))
            .ToArray();

        foreach( var nodeDescriptionType in nodeDescriptionTypes)
        {
            var nodeDescriptionObject = Activator.CreateInstance(nodeDescriptionType) as INodeDescription;
            var nodeTypeName = nodeDescriptionObject.TypeName;
            logger.LogInformation("Registering node {nodeTypeName}", nodeTypeName);
            availableNodeTypes.Add(nodeTypeName, new NodeRepositoryItem(nodeDescriptionObject));
        }
    }

    public IEnumerable<INodeDescription> AvailableTypes()
    {
        return availableNodeTypes.Values;
    }

    public bool ContainsType(string type)
    {
        return availableNodeTypes.ContainsKey(type);
    }

    public IActorRef CreateNode(IActorContext context, string type, Guid id, string name)
    {
        var nodeType = NodeType(type);
        if ( nodeType != null )
        {
            var nodeProps = DependencyResolver.For(context.System).Props(nodeType, id, name);
            return context.ActorOf(nodeProps, name);
        } 
        else
        {
            return null;
        }
    }

    public Type NodeType(string type)
    {
        INodeDescription repoItem;
        if (availableNodeTypes.TryGetValue(type, out repoItem))
        {
            return repoItem.NodeType;
        }
        return null;
    }

    public Type ConfigType(string type)
    {
        INodeDescription repoItem;
        if( availableNodeTypes.TryGetValue(type,out repoItem))
        {
            return repoItem.NodeConfigType;
        }
        return null;
    }

    public Type ConfigComponent(string type)
    {
        INodeDescription repoItem;
        if (availableNodeTypes.TryGetValue(type, out repoItem))
        {
            return repoItem.NodeConfigComponent;
        }
        return null;
    }
}