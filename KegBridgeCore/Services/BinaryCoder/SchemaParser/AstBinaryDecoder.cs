using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;

namespace KegBridgeCore.Services.BinaryCoder.SchemaParser;

/// <summary>
/// Binary decoder that works directly with AST nodes instead of dictionary conversion
/// </summary>
public static class AstSchemas
{
    public static Dictionary<string, IDecoderAction> DecodeCatalog = new();
    
    /// <summary>
    /// Initialize schemas from parsed AST
    /// </summary>
    public static void Initialize(SchemaFile schemaFile)
    {
        DecodeCatalog.Clear();
        
        foreach (var structDecl in schemaFile.Structs)
        {
            DecodeCatalog[structDecl.Name] = new AstDecodeStruct(structDecl);
        }
    }
    
    /// <summary>
    /// Decode binary data using a schema
    /// </summary>
    public static dynamic Decode(string schemaName, byte[] data)
    {
        using var bs = new BinaryStreamReader(data);
        return DecodeCatalog[schemaName].Execute(bs);
    }
}

/// <summary>
/// Decoder for struct declarations using AST nodes
/// </summary>
public class AstDecodeStruct : IDecoderAction
{
    private readonly string _schemaName;
    private readonly List<(string name, IDecoderAction action)> _decoderSequence;

    public AstDecodeStruct(StructDeclaration structDecl)
    {
        _schemaName = structDecl.Name;
        _decoderSequence = new List<(string, IDecoderAction)>();
        BuildSequence(structDecl.Members);
    }

    private void BuildSequence(List<IMember> members)
    {
        foreach (var member in members)
        {
            switch (member)
            {
                case FieldDeclaration field:
                    var fieldDecoder = CreateFieldDecoder(field);
                    if (fieldDecoder != null)
                    {
                        _decoderSequence.Add((field.Name, fieldDecoder));
                    }
                    break;
                    
                case NestedStructDeclaration nestedStruct:
                    var nestedDecoder = new AstDecodeStruct(nestedStruct);
                    _decoderSequence.Add((nestedStruct.Name, nestedDecoder));
                    break;
            }
        }
    }

    private IDecoderAction? CreateFieldDecoder(FieldDeclaration field)
    {
        var fieldType = field.Type;
        
        // Handle arrays
        if (fieldType.ArraySpec != null)
        {
            var elementDecoder = CreateTypeDecoder(fieldType);
            if (elementDecoder != null)
            {
                return new DecodeArray(fieldType.ArraySpec.Size, elementDecoder);
            }
        }
        
        // Handle simple types and custom types
        return CreateTypeDecoder(fieldType);
    }

    private IDecoderAction? CreateTypeDecoder(FieldType fieldType)
    {
        var baseType = GetEffectiveType(fieldType);
        
        // Try simple types first
        var simpleDecoder = CreateSimpleTypeDecoder(baseType);
        if (simpleDecoder != null)
        {
            return simpleDecoder;
        }
        
        // Try custom types (other structs)
        if (AstSchemas.DecodeCatalog.TryGetValue(baseType, out var customDecoder))
        {
            return customDecoder;
        }
        
        throw new Exception($"Unknown type '{baseType}' in field type '{fieldType.GetFullType()}'");
    }

    private string GetEffectiveType(FieldType fieldType)
    {
        var baseType = fieldType.BaseType;
        
        // Apply endianness modifiers
        foreach (var modifier in fieldType.Modifiers)
        {
            if (modifier is EndiannessModifier endian)
            {
                // Replace or add endianness prefix
                if (baseType.StartsWith("s7."))
                {
                    baseType = endian.Endianness + "." + baseType.Substring(3);
                }
                else if (baseType.StartsWith("be.") || baseType.StartsWith("le."))
                {
                    baseType = endian.Endianness + "." + baseType.Substring(3);
                }
                else
                {
                    baseType = endian.Endianness + "." + baseType;
                }
            }
            // Note: Alignment modifiers could be handled here in the future
        }
        
        return baseType;
    }

    private IDecoderAction? CreateSimpleTypeDecoder(string typeName)
    {
        return typeName.ToLower() switch
        {
            "s7.byte" => new DecodeS7Byte(),
            "s7.int" => new DecodeS7Int(),
            "s7.dint" => new DecodeS7DInt(),
            "s7.word" => new DecodeS7Word(),
            "s7.dword" => new DecodeS7DWord(),
            "s7.real" => new DecodeS7Real(),
            "s7.string" => new DecodeS7String(),
            "s7.bool" => new DecodeS7Bool(),
            "s7.bit" => new DecodeS7Bool(),
            "s7.ulong" => new DecodeS7ULong(),
            "le.dword" => new DecodeLeDWord(),
            "le.real" => new DecodeLeReal(),
            "be.real" => new DecodeBeReal(),
            _ => null
        };
    }

    public object Execute(BinaryStreamReader bs)
    {
        dynamic result = new ExpandoObject();
        var resultDict = result as IDictionary<string, object>;

        // Add schema metadata
        if (!string.IsNullOrEmpty(_schemaName))
        {
            resultDict["__schema"] = _schemaName;
        }

        // Execute all field decoders
        foreach (var (name, action) in _decoderSequence)
        {
            var value = action.Execute(bs);
            if (!name.StartsWith("_")) // Skip internal fields
            {
                resultDict[name] = value;
            }
        }

        return result;
    }
}

/// <summary>
/// Additional decoder for S7 ULong type
/// </summary>
public class DecodeS7ULong : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadU8be();
    }
}

/// <summary>
/// Additional decoder for Big Endian Real type
/// </summary>
public class DecodeBeReal : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadF4be();
    }
}

/// <summary>
/// Extension methods for integrating AST-based decoder with existing system
/// </summary>
public static class AstBinaryDecoderExtensions
{
    /// <summary>
    /// Initialize schemas from struct-style schema text using AST decoder
    /// </summary>
    public static void InitializeFromSchemaText(string schemaText)
    {
        var schemaFile = BinarySchemaParser.Parse(schemaText);
        AstSchemas.Initialize(schemaFile);
    }
    
    /// <summary>
    /// Add schemas from struct-style schema text to existing AST schemas
    /// </summary>
    public static void AddSchemasFromText(string schemaText)
    {
        var schemaFile = BinarySchemaParser.Parse(schemaText);
        
        // Add new schemas to existing catalog
        foreach (var structDecl in schemaFile.Structs)
        {
            AstSchemas.DecodeCatalog[structDecl.Name] = new AstDecodeStruct(structDecl);
        }
    }
    
    /// <summary>
    /// Decode binary data using AST-based decoder
    /// </summary>
    public static dynamic DecodeWithAst(string schemaName, byte[] data)
    {
        return AstSchemas.Decode(schemaName, data);
    }
}
