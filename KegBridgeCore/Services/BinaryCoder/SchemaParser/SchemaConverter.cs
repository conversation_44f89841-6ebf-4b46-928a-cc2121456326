using System;
using System.Collections.Generic;
using System.Linq;

namespace KegBridgeCore.Services.BinaryCoder.SchemaParser;

/// <summary>
/// Converts parsed schema AST to the format expected by the existing binary decoder
/// </summary>
public static class SchemaConverter
{
    /// <summary>
    /// Convert a parsed schema file to the dictionary format used by the binary decoder
    /// </summary>
    public static Dictionary<string, object> ConvertToSchemas(SchemaFile schemaFile)
    {
        var schemas = new Dictionary<string, object>();
        
        foreach (var structDecl in schemaFile.Structs)
        {
            schemas[structDecl.Name] = ConvertStruct(structDecl);
        }
        
        return schemas;
    }
    
    /// <summary>
    /// Convert a struct declaration to the nested dictionary format
    /// </summary>
    private static Dictionary<string, object> ConvertStruct(StructDeclaration structDecl)
    {
        var result = new Dictionary<string, object>();
        
        foreach (var member in structDecl.Members)
        {
            switch (member)
            {
                case FieldDeclaration field:
                    result[field.Name] = ConvertFieldType(field.Type);
                    break;
                    
                case NestedStructDeclaration nestedStruct:
                    result[nestedStruct.Name] = ConvertStruct(nestedStruct);
                    break;
            }
        }
        
        return result;
    }
    
    /// <summary>
    /// Convert a field type to the format expected by the binary decoder
    /// </summary>
    private static object ConvertFieldType(FieldType fieldType)
    {
        // Handle arrays
        if (fieldType.ArraySpec != null)
        {
            return new Dictionary<string, object>
            {
                ["$array"] = fieldType.ArraySpec.Size,
                ["$refs"] = GetTypeString(fieldType),
                ["$offset"] = 0  // Default offset, can be customized later
            };
        }

        // Handle simple types
        return GetTypeString(fieldType);
    }
    
    /// <summary>
    /// Get the type string for the binary decoder, including modifiers
    /// </summary>
    private static string GetTypeString(FieldType fieldType)
    {
        var baseType = fieldType.BaseType;
        
        // Apply modifiers
        foreach (var modifier in fieldType.Modifiers)
        {
            switch (modifier)
            {
                case EndiannessModifier endian:
                    // Replace the endianness prefix if it exists
                    if (baseType.StartsWith("s7."))
                    {
                        baseType = endian.Endianness + "." + baseType.Substring(3);
                    }
                    else if (baseType.StartsWith("be.") || baseType.StartsWith("le."))
                    {
                        baseType = endian.Endianness + "." + baseType.Substring(3);
                    }
                    else
                    {
                        baseType = endian.Endianness + "." + baseType;
                    }
                    break;
                    
                case AlignmentModifier align:
                    // For now, we'll add alignment as a comment or ignore it
                    // The existing binary decoder doesn't seem to handle alignment explicitly
                    // This could be extended in the future
                    break;
            }
        }
        
        return baseType;
    }
    
    /// <summary>
    /// Parse schema text and convert to the format expected by the binary decoder
    /// </summary>
    public static Dictionary<string, object> ParseAndConvert(string schemaText)
    {
        var schemaFile = BinarySchemaParser.Parse(schemaText);
        return ConvertToSchemas(schemaFile);
    }
    
    /// <summary>
    /// Try to parse schema text and convert to the format expected by the binary decoder
    /// </summary>
    public static bool TryParseAndConvert(string schemaText, out Dictionary<string, object>? schemas, out string? error)
    {
        schemas = null;
        error = null;
        
        try
        {
            if (BinarySchemaParser.TryParse(schemaText, out var schemaFile, out error))
            {
                schemas = ConvertToSchemas(schemaFile!);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            error = ex.Message;
            return false;
        }
    }
}

/// <summary>
/// Extension methods for integrating the schema parser with the existing binary decoder
/// </summary>
public static class BinaryDecoderExtensions
{
    /// <summary>
    /// Initialize schemas from struct-style schema text
    /// </summary>
    public static void InitializeFromSchemaText(string schemaText)
    {
        var schemas = SchemaConverter.ParseAndConvert(schemaText);
        Schemas.Initialize(schemas);
    }
    
    /// <summary>
    /// Add schemas from struct-style schema text to existing schemas
    /// </summary>
    public static void AddSchemasFromText(string schemaText)
    {
        var newSchemas = SchemaConverter.ParseAndConvert(schemaText);
        
        // Merge with existing schemas
        var existingSchemas = new Dictionary<string, object>();
        foreach (var kvp in Schemas.DecodeCatalog)
        {
            existingSchemas[kvp.Key] = new Dictionary<string, object>(); // Placeholder
        }
        
        foreach (var kvp in newSchemas)
        {
            existingSchemas[kvp.Key] = kvp.Value;
        }
        
        Schemas.Initialize(existingSchemas);
    }
}
