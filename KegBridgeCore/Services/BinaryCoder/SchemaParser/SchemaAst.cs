using System.Collections.Generic;

namespace KegBridgeCore.Services.BinaryCoder.SchemaParser;

/// <summary>
/// Base class for all AST nodes in the binary schema language
/// </summary>
public abstract class AstNode
{
    public int Line { get; set; }
    public int Column { get; set; }
}

/// <summary>
/// Represents a complete schema file containing multiple struct definitions
/// </summary>
public class SchemaFile : AstNode
{
    public List<StructDeclaration> Structs { get; set; } = new();
}

/// <summary>
/// Represents a struct declaration
/// </summary>
public class StructDeclaration : AstNode
{
    public string Name { get; set; } = string.Empty;
    public List<IMember> Members { get; set; } = new();
}

/// <summary>
/// Interface for struct members (fields or nested structs)
/// </summary>
public interface IMember
{
    string Name { get; }
}

/// <summary>
/// Represents a field declaration within a struct
/// </summary>
public class FieldDeclaration : AstNode, IMember
{
    public string Name { get; set; } = string.Empty;
    public FieldType Type { get; set; } = new();
    public string? Comment { get; set; }
}

/// <summary>
/// Represents a nested struct declaration
/// </summary>
public class NestedStructDeclaration : StructDeclaration, IMember
{
}

/// <summary>
/// Represents a field type with optional modifiers and array specification
/// </summary>
public class FieldType : AstNode
{
    public string BaseType { get; set; } = string.Empty;
    public List<TypeModifier> Modifiers { get; set; } = new();
    public ArraySpecification? ArraySpec { get; set; }
    
    /// <summary>
    /// Gets the full type string including modifiers
    /// </summary>
    public string GetFullType()
    {
        var parts = new List<string>();
        
        foreach (var modifier in Modifiers)
        {
            parts.Add(modifier.ToString());
        }
        
        parts.Add(BaseType);
        
        if (ArraySpec != null)
        {
            parts.Add($"[{ArraySpec.Size}]");
        }
        
        return string.Join(" ", parts);
    }
}

/// <summary>
/// Base class for type modifiers
/// </summary>
public abstract class TypeModifier : AstNode
{
}

/// <summary>
/// Represents endianness modifier (be/le)
/// </summary>
public class EndiannessModifier : TypeModifier
{
    public string Endianness { get; set; } = string.Empty; // "be" or "le"
    
    public override string ToString() => Endianness;
}

/// <summary>
/// Represents alignment modifier
/// </summary>
public class AlignmentModifier : TypeModifier
{
    public int Alignment { get; set; }
    
    public override string ToString() => $"align({Alignment})";
}

/// <summary>
/// Represents array specification [size]
/// </summary>
public class ArraySpecification : AstNode
{
    public int Size { get; set; }
}

/// <summary>
/// Represents a comment in the schema
/// </summary>
public class Comment : AstNode
{
    public string Text { get; set; } = string.Empty;
    public CommentType Type { get; set; }
}

/// <summary>
/// Type of comment
/// </summary>
public enum CommentType
{
    Line,   // // comment
    Block   // /* comment */
}
