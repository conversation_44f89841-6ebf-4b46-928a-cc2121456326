using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace KegBridgeCore.Services.BinaryCoder;

public class BinaryStreamReader : BinaryReader
{
    #region Constructors

    public BinaryStreamReader(Stream stream) : base(stream)
    {
    }

    public BinaryStreamReader(string file) : base(File.Open(file, FileMode.Open, FileAccess.Read, FileShare.Read))
    {
    }

    public BinaryStreamReader(byte[] bytes) : base(new MemoryStream(bytes))
    {
    }

    private ulong Bits = 0;
    private int BitsLeft = 0;

    static readonly bool IsLittleEndian = BitConverter.IsLittleEndian;

    #endregion

    #region Stream positioning

    /// <summary>
    /// Check if the stream position is at the end of the stream
    /// </summary>
    public bool IsEof
    {
        get { return BaseStream.Position >= BaseStream.Length && BitsLeft == 0; }
    }

    /// <summary>
    /// Seek to a specific position from the beginning of the stream
    /// </summary>
    /// <param name="position">The position to seek to</param>
    public void Seek(long position)
    {
        BaseStream.Seek(position, SeekOrigin.Begin);
    }

    /// <summary>
    /// Get the current position in the stream
    /// </summary>
    public long Pos
    {
        get { return BaseStream.Position; }
    }

    /// <summary>
    /// Get the total length of the stream (ie. file size)
    /// </summary>
    public long Size
    {
        get { return BaseStream.Length; }
    }

    #endregion

    #region Integer types

    #region Signed

    /// <summary>
    /// Read a signed byte from the stream
    /// </summary>
    /// <returns></returns>
    public sbyte ReadS1()
    {
        return ReadSByte();
    }

    #region Big-endian

    /// <summary>
    /// Read a signed short from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public short ReadS2be()
    {
        return BitConverter.ToInt16(ReadBytesNormalisedBigEndian(2), 0);
    }

    /// <summary>
    /// Read a signed int from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public int ReadS4be()
    {
        return BitConverter.ToInt32(ReadBytesNormalisedBigEndian(4), 0);
    }

    /// <summary>
    /// Read a signed long from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public long ReadS8be()
    {
        return BitConverter.ToInt64(ReadBytesNormalisedBigEndian(8), 0);
    }

    #endregion

    #region Little-endian

    /// <summary>
    /// Read a signed short from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public short ReadS2le()
    {
        return BitConverter.ToInt16(ReadBytesNormalisedLittleEndian(2), 0);
    }

    /// <summary>
    /// Read a signed int from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public int ReadS4le()
    {
        return BitConverter.ToInt32(ReadBytesNormalisedLittleEndian(4), 0);
    }

    /// <summary>
    /// Read a signed long from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public long ReadS8le()
    {
        return BitConverter.ToInt64(ReadBytesNormalisedLittleEndian(8), 0);
    }

    #endregion

    #endregion

    #region Big-endian

    /// <summary>
    /// Read an unsigned short from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public ushort ReadU2be()
    {
        return BitConverter.ToUInt16(ReadBytesNormalisedBigEndian(2), 0);
    }

    /// <summary>
    /// Read an unsigned int from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public uint ReadU4be()
    {
        return BitConverter.ToUInt32(ReadBytesNormalisedBigEndian(4), 0);
    }

    /// <summary>
    /// Read an unsigned long from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public ulong ReadU8be()
    {
        return BitConverter.ToUInt64(ReadBytesNormalisedBigEndian(8), 0);
    }

    #endregion

    #region Little-endian

    /// <summary>
    /// Read an unsigned short from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public ushort ReadU2le()
    {
        return BitConverter.ToUInt16(ReadBytesNormalisedLittleEndian(2), 0);
    }

    /// <summary>
    /// Read an unsigned int from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public uint ReadU4le()
    {
        return BitConverter.ToUInt32(ReadBytesNormalisedLittleEndian(4), 0);
    }

    /// <summary>
    /// Read an unsigned long from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public ulong ReadU8le()
    {
        return BitConverter.ToUInt64(ReadBytesNormalisedLittleEndian(8), 0);
    }

    #endregion

    #endregion

    #region Floating point types

    #region Big-endian

    /// <summary>
    /// Read a single-precision floating point value from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public float ReadF4be()
    {
        return BitConverter.ToSingle(ReadBytesNormalisedBigEndian(4), 0);
    }

    /// <summary>
    /// Read a double-precision floating point value from the stream (big endian)
    /// </summary>
    /// <returns></returns>
    public double ReadF8be()
    {
        return BitConverter.ToDouble(ReadBytesNormalisedBigEndian(8), 0);
    }

    #endregion

    #region Little-endian

    /// <summary>
    /// Read a single-precision floating point value from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public float ReadF4le()
    {
        return BitConverter.ToSingle(ReadBytesNormalisedLittleEndian(4), 0);
    }

    /// <summary>
    /// Read a double-precision floating point value from the stream (little endian)
    /// </summary>
    /// <returns></returns>
    public double ReadF8le()
    {
        return BitConverter.ToDouble(ReadBytesNormalisedLittleEndian(8), 0);
    }

    #endregion

    #endregion

    #region Unaligned bit values

    public void AlignToByte()
    {
        Bits = 0;
        BitsLeft = 0;
    }

    public void AlignToWord()
    {
        AlignToByte();

        if( Pos % 2 != 0)
        {
            ReadBytes(1);
        }
    }

    public ulong ReadBitsIntBe(int n)
    {
        int bitsNeeded = n - BitsLeft;
        if (bitsNeeded > 0)
        {
            // 1 bit  => 1 byte
            // 8 bits => 1 byte
            // 9 bits => 2 bytes
            int bytesNeeded = ((bitsNeeded - 1) / 8) + 1;
            byte[] buf = ReadBytes(bytesNeeded);
            for (int i = 0; i < buf.Length; i++)
            {
                Bits <<= 8;
                Bits |= buf[i];
                BitsLeft += 8;
            }
        }

        // raw mask with required number of 1s, starting from lowest bit
        ulong mask = GetMaskOnes(n);
        // shift "bits" to align the highest bits with the mask & derive reading result
        int shiftBits = BitsLeft - n;
        ulong res = (Bits >> shiftBits) & mask;
        // clear top bits that we've just read => AND with 1s
        BitsLeft -= n;
        mask = GetMaskOnes(BitsLeft);
        Bits &= mask;

        return res;
    }

    public bool ReadBitS7()
    {
        if (BitsLeft == 0)
        {
            byte[] buf = ReadBytes(1);
            Bits = buf[0];
            BitsLeft = 8;
        }

        ulong res = Bits & 0x01;

        BitsLeft -= 1;
        Bits >>= 1;

        return res == 1;
    }


    //Method ported from algorithm specified @ issue#155
    public ulong ReadBitsIntLe(int n)
    {
        int bitsNeeded = n - BitsLeft;

        if (bitsNeeded > 0)
        {
            // 1 bit  => 1 byte
            // 8 bits => 1 byte
            // 9 bits => 2 bytes
            int bytesNeeded = ((bitsNeeded - 1) / 8) + 1;
            byte[] buf = ReadBytes(bytesNeeded);
            for (int i = 0; i < buf.Length; i++)
            {
                ulong v = (ulong)((ulong)buf[i] << BitsLeft);
                Bits |= v;
                BitsLeft += 8;
            }
        }

        // raw mask with required number of 1s, starting from lowest bit
        ulong mask = GetMaskOnes(n);

        // derive reading result
        ulong res = (Bits & mask);

        // remove bottom bits that we've just read by shifting
        Bits >>= n;
        BitsLeft -= n;

        return res;
    }

    private static ulong GetMaskOnes(int n)
    {
        return n == 64 ? 0xffffffffffffffffUL : (1UL << n) - 1;
    }

    #endregion

    #region Byte arrays

    /// <summary>
    /// Read a fixed number of bytes from the stream
    /// </summary>
    /// <param name="count">The number of bytes to read</param>
    /// <returns></returns>
    public byte[] ReadBytes(long count)
    {
        if (count < 0 || count > Int32.MaxValue)
            throw new ArgumentOutOfRangeException("requested " + count + " bytes, while only non-negative int32 amount of bytes possible");
        byte[] bytes = base.ReadBytes((int)count);
        if (bytes.Length < count)
            throw new EndOfStreamException("requested " + count + " bytes, but got only " + bytes.Length + " bytes");
        return bytes;
    }

    /// <summary>
    /// Read a fixed number of bytes from the stream
    /// </summary>
    /// <param name="count">The number of bytes to read</param>
    /// <returns></returns>
    public byte[] ReadBytes(ulong count)
    {
        if (count > Int32.MaxValue)
            throw new ArgumentOutOfRangeException("requested " + count + " bytes, while only non-negative int32 amount of bytes possible");
        byte[] bytes = base.ReadBytes((int)count);
        if (bytes.Length < (int)count)
            throw new EndOfStreamException("requested " + count + " bytes, but got only " + bytes.Length + " bytes");
        return bytes;
    }

    /// <summary>
    /// Read bytes from the stream in little endian format and convert them to the endianness of the current platform
    /// </summary>
    /// <param name="count">The number of bytes to read</param>
    /// <returns>An array of bytes that matches the endianness of the current platform</returns>
    protected byte[] ReadBytesNormalisedLittleEndian(int count)
    {
        byte[] bytes = ReadBytes(count);
        if (!IsLittleEndian) Array.Reverse(bytes);
        return bytes;
    }

    /// <summary>
    /// Read bytes from the stream in big endian format and convert them to the endianness of the current platform
    /// </summary>
    /// <param name="count">The number of bytes to read</param>
    /// <returns>An array of bytes that matches the endianness of the current platform</returns>
    protected byte[] ReadBytesNormalisedBigEndian(int count)
    {
        byte[] bytes = ReadBytes(count);
        if (IsLittleEndian) Array.Reverse(bytes);
        return bytes;
    }

    /// <summary>
    /// Read all the remaining bytes from the stream until the end is reached
    /// </summary>
    /// <returns></returns>
    public byte[] ReadBytesFull()
    {
        return ReadBytes(BaseStream.Length - BaseStream.Position);
    }

    /// <summary>
    /// Read a terminated string from the stream
    /// </summary>
    /// <param name="terminator">The string terminator value</param>
    /// <param name="includeTerminator">True to include the terminator in the returned string</param>
    /// <param name="consumeTerminator">True to consume the terminator byte before returning</param>
    /// <param name="eosError">True to throw an error when the EOS was reached before the terminator</param>
    /// <returns></returns>
    public byte[] ReadBytesTerm(byte terminator, bool includeTerminator, bool consumeTerminator, bool eosError)
    {
        List<byte> bytes = new List<byte>();
        while (true)
        {
            if (IsEof)
            {
                if (eosError) throw new EndOfStreamException(string.Format("End of stream reached, but no terminator `{0}` found", terminator));
                break;
            }

            byte b = ReadByte();
            if (b == terminator)
            {
                if (includeTerminator) bytes.Add(b);
                if (!consumeTerminator) Seek(Pos - 1);
                break;
            }
            bytes.Add(b);
        }
        return bytes.ToArray();
    }

    /// <summary>
    /// Read a specific set of bytes and assert that they are the same as an expected result
    /// </summary>
    /// <param name="expected">The expected result</param>
    /// <returns></returns>
    public byte[] EnsureFixedContents(byte[] expected)
    {
        byte[] bytes = ReadBytes(expected.Length);

        if (bytes.Length != expected.Length)
        {
            throw new Exception(string.Format("Expected bytes: {0} ({1} bytes), Instead got: {2} ({3} bytes)", Convert.ToBase64String(expected), expected.Length, Convert.ToBase64String(bytes), bytes.Length));
        }
        for (int i = 0; i < bytes.Length; i++)
        {
            if (bytes[i] != expected[i])
            {
                throw new Exception(string.Format("Expected bytes: {0} ({1} bytes), Instead got: {2} ({3} bytes)", Convert.ToBase64String(expected), expected.Length, Convert.ToBase64String(bytes), bytes.Length));
            }
        }

        return bytes;
    }

    #endregion

    #region Strings
    public string ReadStringS7()
    {
        short maxLen = ReadByte();
        short actualLen = ReadByte();

        byte[] bytes =  ReadBytes(maxLen);

        // read terminating zero
        //ReadByte();

        return Encoding.UTF8.GetString(bytes, 0, actualLen); ;
    }
    #endregion

    #region Misc utility methods

    /// <summary>
    /// Performs modulo operation between two integers.
    /// </summary>
    /// <remarks>
    /// This method is required because C# lacks a "true" modulo
    /// operator, the % operator rather being the "remainder"
    /// operator. We want mod operations to always be positive.
    /// </remarks>
    /// <param name="a">The value to be divided</param>
    /// <param name="b">The value to divide by. Must be greater than zero.</param>
    /// <returns>The result of the modulo opertion. Will always be positive.</returns>
    public static int Mod(int a, int b)
    {
        if (b <= 0) throw new ArgumentException("Divisor of mod operation must be greater than zero.", "b");
        int r = a % b;
        if (r < 0) r += b;
        return r;
    }

    /// <summary>
    /// Performs modulo operation between two integers.
    /// </summary>
    /// <remarks>
    /// This method is required because C# lacks a "true" modulo
    /// operator, the % operator rather being the "remainder"
    /// operator. We want mod operations to always be positive.
    /// </remarks>
    /// <param name="a">The value to be divided</param>
    /// <param name="b">The value to divide by. Must be greater than zero.</param>
    /// <returns>The result of the modulo opertion. Will always be positive.</returns>
    public static long Mod(long a, long b)
    {
        if (b <= 0) throw new ArgumentException("Divisor of mod operation must be greater than zero.", "b");
        long r = a % b;
        if (r < 0) r += b;
        return r;
    }

    /// <summary>
    /// Compares two byte arrays in lexicographical order.
    /// </summary>
    /// <returns>negative number if a is less than b, <c>0</c> if a is equal to b, positive number if a is greater than b.</returns>
    /// <param name="a">First byte array to compare</param>
    /// <param name="b">Second byte array to compare.</param>
    public static int ByteArrayCompare(byte[] a, byte[] b)
    {
        if (a == b)
            return 0;
        int al = a.Length;
        int bl = b.Length;
        int minLen = al < bl ? al : bl;
        for (int i = 0; i < minLen; i++)
        {
            int cmp = a[i] - b[i];
            if (cmp != 0)
                return cmp;
        }

        // Reached the end of at least one of the arrays
        if (al == bl)
        {
            return 0;
        }
        else
        {
            return al - bl;
        }
    }

    /// <summary>
    /// Reverses the string, Unicode-aware.
    /// </summary>
    /// <a href="https://stackoverflow.com/a/15029493">taken from here</a>
    public static string StringReverse(string s)
    {
        TextElementEnumerator enumerator = StringInfo.GetTextElementEnumerator(s);

        List<string> elements = new List<string>();
        while (enumerator.MoveNext())
            elements.Add(enumerator.GetTextElement());

        elements.Reverse();
        return string.Concat(elements);
    }

    public static byte[] HexStringToByteArray(string hexString)
    {
        if (hexString.Length % 2 != 0)
        {
            throw new ArgumentException(String.Format(CultureInfo.InvariantCulture, "The hexstring cannot have an odd number of digits: {0}", hexString));
        }

        byte[] data = new byte[hexString.Length / 2];
        for (int index = 0; index < data.Length; index++)
        {
            string byteValue = hexString.Substring(index * 2, 2);
            data[index] = byte.Parse(byteValue, NumberStyles.HexNumber, CultureInfo.InvariantCulture);
        }

        return data;
    }

    #endregion
}