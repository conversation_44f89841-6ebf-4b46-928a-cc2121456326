#nullable enable
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using NCalc;
using Serilog.Core;

namespace KegBridgeCore.Services;

public static class EventNodeRuleExpression
{
    private static readonly ExpressionContext DefaultContext = CreateContext();

    public static Expression? Create(string? text)
    {
        if (text == null)
        {
            return null;
        }

        var expr = new Expression(text, DefaultContext);
        return expr;
    }

    public static object? Evaluate(Expression expr, Data.NodeEvent nodeEvent)
    {
        var parameters = expr.GetParameterNames();

        foreach (var n in parameters)
        {
            if (n.StartsWith("data."))
            {
                nodeEvent.TryGetPath(n.Substring(5), out var value);
                expr.Parameters[n] = value;
            }
        }

        expr.Parameters["node"] = nodeEvent.Node;
        expr.Parameters["topic"] = nodeEvent.Topic;
        expr.Parameters["timestamp"] = nodeEvent.Timestamp;
        expr.Parameters["id"] = nodeEvent.Id;
        expr.Parameters["data"] = nodeEvent.Data;

        return expr.Evaluate();
    }

    private static ExpressionContext CreateContext()
    {
        var ctx = new ExpressionContext();

        // ---- Null & coalescing helpers ----
        ctx.Functions["isnull"] = args => IsNullish(args[0].Evaluate());
        ctx.Functions["nullif"] = args =>
        {
            var x = args[0].Evaluate();
            var y = args[1].Evaluate();
            return Equals(x, y) ? null : x;
        };
        ctx.Functions["coalesce"] = args =>
        {
            foreach (var e in args)
            {
                var v = e.Evaluate();
                if (!IsNullish(v)) return v;
            }

            return null;
        };
        ctx.Functions["nvl"] = args =>
        {
            var v = args[0].Evaluate();
            return IsNullish(v) ? args[1].Evaluate() : v;
        };
        ctx.Functions["nz"] = ctx.Functions["nvl"]; // alias
        // Typed defaults
        ctx.Functions["nzs"] = args =>
            IsNullish(args[0].Evaluate()) ? (args.Count() > 1 ? args[1].Evaluate() : "") : args[0].Evaluate();
        ctx.Functions["nzb"] = args =>
            IsNullish(args[0].Evaluate())
                ? (args.Count() > 1 ? ToBool(args[1].Evaluate()) : false)
                : ToBool(args[0].Evaluate());
        ctx.Functions["nzi"] = args =>
            IsNullish(args[0].Evaluate())
                ? (args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0)
                : ToInt(args[0].Evaluate());
        ctx.Functions["nzd"] = args =>
            IsNullish(args[0].Evaluate())
                ? (args.Count() > 1 ? ToDecimal(args[1].Evaluate()) : 0m)
                : ToDecimal(args[0].Evaluate());
        ctx.Functions["nzdbl"] = args =>
            IsNullish(args[0].Evaluate())
                ? (args.Count() > 1 ? ToDouble(args[1].Evaluate()) : 0.0)
                : ToDouble(args[0].Evaluate());
        // Converters
        ctx.Functions["toDecimal"] = args => ToDecimal(args[0].Evaluate());
        ctx.Functions["toDouble"] = args => ToDouble(args[0].Evaluate());
        ctx.Functions["toInt"] = args => ToInt(args[0].Evaluate());
        ctx.Functions["toBool"] = args => ToBool(args[0].Evaluate());
        ctx.Functions["toString"] = args => args[0].Evaluate()?.ToString() ?? "";

        // ---- NodeEvent and NodeEventData specific functions ----
        ctx.Functions["getPath"] = args =>
        {
            var obj = args[0].Evaluate();
            var path = args[1].Evaluate()?.ToString();

            if (string.IsNullOrEmpty(path)) return null;

            return obj switch
            {
                Data.NodeEvent nodeEvent => nodeEvent.GetPath(path),
                Data.NodeEventData nodeEventData => nodeEventData.GetPath(path),
                _ => null
            };
        };

        ctx.Functions["hasPath"] = args =>
        {
            var obj = args[0].Evaluate();
            var path = args[1].Evaluate()?.ToString();

            if (string.IsNullOrEmpty(path)) return false;

            return obj switch
            {
                Data.NodeEvent nodeEvent => nodeEvent.TryGetPath(path, out _),
                Data.NodeEventData nodeEventData => nodeEventData.TryGetPath(path, out _),
                _ => false
            };
        };

        // ---- String manipulation functions ----
        ctx.Functions["concat"] = args => string.Concat(args.Select(a => a.Evaluate()?.ToString() ?? ""));
        ctx.Functions["substring"] = args =>
        {
            var str = args[0].Evaluate()?.ToString() ?? "";
            var start = ToInt(args[1].Evaluate());
            if (args.Count() > 2)
            {
                var length = ToInt(args[2].Evaluate());
                return start >= 0 && start < str.Length && length > 0
                    ? str.Substring(start, Math.Min(length, str.Length - start))
                    : "";
            }

            return start >= 0 && start < str.Length ? str.Substring(start) : "";
        };
        ctx.Functions["indexOf"] = args =>
        {
            var str = args[0].Evaluate()?.ToString() ?? "";
            var search = args[1].Evaluate()?.ToString() ?? "";
            return str.IndexOf(search, StringComparison.Ordinal);
        };
        ctx.Functions["replace"] = args =>
        {
            var str = args[0].Evaluate()?.ToString() ?? "";
            var oldValue = args[1].Evaluate()?.ToString() ?? "";
            var newValue = args[2].Evaluate()?.ToString() ?? "";
            return str.Replace(oldValue, newValue);
        };
        ctx.Functions["trim"] = args => args[0].Evaluate()?.ToString()?.Trim() ?? "";
        ctx.Functions["upper"] = args => args[0].Evaluate()?.ToString()?.ToUpperInvariant() ?? "";
        ctx.Functions["lower"] = args => args[0].Evaluate()?.ToString()?.ToLowerInvariant() ?? "";
        ctx.Functions["length"] = args => args[0].Evaluate()?.ToString()?.Length ?? 0;

        // ---- Math functions ----
        ctx.Functions["abs"] = args => Math.Abs(ToDouble(args[0].Evaluate()));
        ctx.Functions["round"] = args =>
        {
            var value = ToDouble(args[0].Evaluate());
            var digits = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            return Math.Round(value, digits);
        };
        ctx.Functions["floor"] = args => Math.Floor(ToDouble(args[0].Evaluate()));
        ctx.Functions["ceiling"] = args => Math.Ceiling(ToDouble(args[0].Evaluate()));
        ctx.Functions["min"] = args => args.Select(a => ToDouble(a.Evaluate())).Min();
        ctx.Functions["max"] = args => args.Select(a => ToDouble(a.Evaluate())).Max();

        // ---- Date/Time functions ----
        ctx.Functions["now"] = args => DateTime.UtcNow;
        ctx.Functions["today"] = args => DateTime.Today;
        ctx.Functions["addDays"] = args =>
        {
            var date = args[0].Evaluate();
            var days = ToDouble(args[1].Evaluate());
            return date is DateTime dt ? dt.AddDays(days) : DateTime.UtcNow.AddDays(days);
        };
        ctx.Functions["addHours"] = args =>
        {
            var date = args[0].Evaluate();
            var hours = ToDouble(args[1].Evaluate());
            return date is DateTime dt ? dt.AddHours(hours) : DateTime.UtcNow.AddHours(hours);
        };
        ctx.Functions["formatDate"] = args =>
        {
            var date = args[0].Evaluate();
            var format = args[1].Evaluate()?.ToString() ?? "yyyy-MM-dd";
            return date is DateTime dt ? dt.ToString(format) : "";
        };

        // ---- Byte array extraction functions ----
        ctx.Functions["getUInt16LE"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            return ExtractUInt16(bytes, offset, littleEndian: true);
        };

        ctx.Functions["getUInt16BE"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            return ExtractUInt16(bytes, offset, littleEndian: false);
        };

        ctx.Functions["getUInt32LE"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            return ExtractUInt32(bytes, offset, littleEndian: true);
        };

        ctx.Functions["getUInt32BE"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            return ExtractUInt32(bytes, offset, littleEndian: false);
        };

        // Convenience functions with explicit endianness parameter
        ctx.Functions["getUInt16"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            var littleEndian = args.Count() > 2 ? ToBool(args[2].Evaluate()) : true; // Default to little endian
            return ExtractUInt16(bytes, offset, littleEndian);
        };

        ctx.Functions["getUInt32"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            var littleEndian = args.Count() > 2 ? ToBool(args[2].Evaluate()) : true; // Default to little endian
            return ExtractUInt32(bytes, offset, littleEndian);
        };

        // ---- DateTime extraction functions ----
        ctx.Functions["getDateTimeLE"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            return ExtractDateTime(bytes, offset, littleEndian: true);
        };

        ctx.Functions["getDateTimeBE"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            return ExtractDateTime(bytes, offset, littleEndian: false);
        };

        ctx.Functions["getDateTime"] = args =>
        {
            var bytes = GetByteArray(args[0].Evaluate());
            var offset = args.Count() > 1 ? ToInt(args[1].Evaluate()) : 0;
            var littleEndian = args.Count() > 2 ? ToBool(args[2].Evaluate()) : true; // Default to little endian
            return ExtractDateTime(bytes, offset, littleEndian);
        };

        return ctx;
    }

    private static bool IsNullish(object? v) => v is null || v == DBNull.Value;

    private static decimal ToDecimal(object? v)
    {
        if (IsNullish(v)) return 0m;
        return v switch
        {
            decimal d => d,
            string s => decimal.TryParse(s, NumberStyles.Any, CultureInfo.InvariantCulture, out var r)
                ? r
                : Convert.ToDecimal(s, CultureInfo.InvariantCulture),
            _ => Convert.ToDecimal(v, CultureInfo.InvariantCulture)
        };
    }

    private static double ToDouble(object? v)
    {
        if (IsNullish(v)) return 0.0;
        return v switch
        {
            double d => d,
            float f => f,
            string s => double.TryParse(s, NumberStyles.Any, CultureInfo.InvariantCulture, out var r)
                ? r
                : Convert.ToDouble(s, CultureInfo.InvariantCulture),
            _ => Convert.ToDouble(v, CultureInfo.InvariantCulture)
        };
    }

    private static int ToInt(object? v)
    {
        if (IsNullish(v)) return 0;
        return v switch
        {
            int i => i,
            long l => checked((int)l),
            string s => int.TryParse(s, NumberStyles.Any, CultureInfo.InvariantCulture, out var r)
                ? r
                : Convert.ToInt32(s, CultureInfo.InvariantCulture),
            _ => Convert.ToInt32(v, CultureInfo.InvariantCulture)
        };
    }

    private static bool ToBool(object? v)
    {
        if (IsNullish(v)) return false;
        return v switch
        {
            bool b => b,
            string s => bool.TryParse(s, out var r) ? r : Convert.ToBoolean(s, CultureInfo.InvariantCulture),
            _ => Convert.ToBoolean(v, CultureInfo.InvariantCulture)
        };
    }

    private static byte[] GetByteArray(object? v)
    {
        return v switch
        {
            byte[] bytes => bytes,
            System.Collections.Immutable.ImmutableArray<object> immutableArray =>
                immutableArray.Select(obj => Convert.ToByte(obj)).ToArray(),
            null => throw new ArgumentException("Byte array cannot be null"),
            _ => throw new ArgumentException($"Expected byte array, got {v.GetType().Name}")
        };
    }

    private static uint ExtractUInt16(byte[] bytes, int offset, bool littleEndian)
    {
        // Handle negative offset (count from end)
        if (offset < 0)
        {
            offset = bytes.Length + offset;
        }

        if (offset < 0 || bytes.Length < offset + 2)
            throw new ArgumentException($"Byte array too short or invalid offset. Need at least {offset + 2} bytes, got {bytes.Length}. Offset: {offset}");

        if (littleEndian)
        {
            return (uint)(bytes[offset] | (bytes[offset + 1] << 8));
        }
        else
        {
            return (uint)((bytes[offset] << 8) | bytes[offset + 1]);
        }
    }

    private static uint ExtractUInt32(byte[] bytes, int offset, bool littleEndian)
    {
        // Handle negative offset (count from end)
        if (offset < 0)
        {
            offset = bytes.Length + offset;
        }

        if (offset < 0 || bytes.Length < offset + 4)
            throw new ArgumentException($"Byte array too short or invalid offset. Need at least {offset + 4} bytes, got {bytes.Length}. Offset: {offset}");

        if (littleEndian)
        {
            return (uint)(bytes[offset] |
                         (bytes[offset + 1] << 8) |
                         (bytes[offset + 2] << 16) |
                         (bytes[offset + 3] << 24));
        }
        else
        {
            return (uint)((bytes[offset] << 24) |
                         (bytes[offset + 1] << 16) |
                         (bytes[offset + 2] << 8) |
                         bytes[offset + 3]);
        }
    }

    private static DateTime ExtractDateTime(byte[] bytes, int offset, bool littleEndian)
    {
        // Handle negative offset (count from end)
        if (offset < 0)
        {
            offset = bytes.Length + offset;
        }

        if (offset < 0 || bytes.Length < offset + 8)
            throw new ArgumentException($"Byte array too short or invalid offset. Need at least {offset + 8} bytes, got {bytes.Length}. Offset: {offset}");

        ulong epochValue;

        if (littleEndian)
        {
            epochValue = (ulong)bytes[offset] |
                        ((ulong)bytes[offset + 1] << 8) |
                        ((ulong)bytes[offset + 2] << 16) |
                        ((ulong)bytes[offset + 3] << 24) |
                        ((ulong)bytes[offset + 4] << 32) |
                        ((ulong)bytes[offset + 5] << 40) |
                        ((ulong)bytes[offset + 6] << 48) |
                        ((ulong)bytes[offset + 7] << 56);
        }
        else
        {
            epochValue = ((ulong)bytes[offset] << 56) |
                        ((ulong)bytes[offset + 1] << 48) |
                        ((ulong)bytes[offset + 2] << 40) |
                        ((ulong)bytes[offset + 3] << 32) |
                        ((ulong)bytes[offset + 4] << 24) |
                        ((ulong)bytes[offset + 5] << 16) |
                        ((ulong)bytes[offset + 6] << 8) |
                        (ulong)bytes[offset + 7];
        }

        // Convert epoch value to DateTime
        // The example value 1755872665095018066 appears to be in 100-nanosecond ticks since Unix epoch
        // Let's detect the format based on the magnitude of the value
        return ConvertEpochToDateTime(epochValue);
    }

    private static DateTime ConvertEpochToDateTime(ulong epochValue)
    {
        try
        {
            // Let's analyze the example value: 1755872665095018066
            // This is approximately 1.755 * 10^18
            // If this were nanoseconds since Unix epoch, it would be:
            // 1755872665095018066 / 1000000000 = 1755872665 seconds since Unix epoch
            // Which is approximately year 2025 (current time)

            // Check if this looks like nanoseconds since Unix epoch (most likely for the example)
            if (epochValue > 1000000000000000000UL) // > 10^18 (roughly year 2001 in nanoseconds)
            {
                // Convert nanoseconds to DateTime with full precision
                // 1 tick = 100 nanoseconds
                var ticks = epochValue / 100UL; // Convert nanoseconds to ticks
                var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                return unixEpoch.AddTicks((long)ticks);
            }

            // Check if this looks like .NET ticks (100-nanosecond intervals since 0001-01-01)
            // .NET ticks for Unix epoch (1970-01-01) = 621355968000000000
            if (epochValue > 621355968000000000UL && epochValue < 3155378975999999999UL) // Between 1970 and 9999
            {
                return new DateTime((long)epochValue, DateTimeKind.Utc);
            }

            // Check if this looks like 100-nanosecond ticks since Unix epoch
            if (epochValue > 10000000000000000UL && epochValue <= 1000000000000000000UL) // 10^16 to 10^18
            {
                var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                return unixEpoch.AddTicks((long)epochValue);
            }

            // Check if this looks like milliseconds since Unix epoch
            // Current time in milliseconds is around 1.7e12 (13 digits)
            if (epochValue > 1000000000000UL && epochValue <= 9999999999999UL) // 10^12 to 10^13-1 (13 digits)
            {
                // Convert milliseconds to DateTime with full precision
                // 1 millisecond = 10,000 ticks (1 tick = 100 nanoseconds)
                var ticks = epochValue * 10000UL; // Convert milliseconds to ticks
                var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                return unixEpoch.AddTicks((long)ticks);
            }

            // Check if this looks like microseconds since Unix epoch
            if (epochValue > 10000000000000UL && epochValue <= 9999999999999999UL) // 10^13 to 10^16-1 (14-16 digits)
            {
                // Convert microseconds to DateTime with full precision
                // 1 microsecond = 10 ticks (1 tick = 100 nanoseconds)
                var ticks = epochValue * 10UL; // Convert microseconds to ticks
                var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                return unixEpoch.AddTicks((long)ticks);
            }

            // Check if this looks like milliseconds since Unix epoch (legacy range)
            if (epochValue > 1000000000UL && epochValue <= 1000000000000UL) // 10^9 to 10^12 (10-12 digits)
            {
                // Convert milliseconds to DateTime with full precision
                // 1 millisecond = 10,000 ticks (1 tick = 100 nanoseconds)
                var ticks = epochValue * 10000UL; // Convert milliseconds to ticks
                var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                return unixEpoch.AddTicks((long)ticks);
            }

            // Assume seconds since Unix epoch
            return DateTimeOffset.FromUnixTimeSeconds((long)epochValue).DateTime;
        }
        catch
        {
            // If all else fails, try to interpret as Unix timestamp in seconds
            try
            {
                return DateTimeOffset.FromUnixTimeSeconds((long)epochValue).DateTime;
            }
            catch
            {
                // Return Unix epoch if we can't convert
                return new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            }
        }
    }
}

