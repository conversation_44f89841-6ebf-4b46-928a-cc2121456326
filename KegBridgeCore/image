#!/usr/bin/env bash
set -e

IMAGE_ENV_FILE=.image.env

if [ ! -f $IMAGE_ENV_FILE ]
then
    cat << EOF
$IMAGE_ENV_FILE does not exist.

Create this file with following env:

REGISTRY_HOST=registry.gitlab.com
REGISTRY_PATH=/mep_insight/insight
REGISTRY_USERNAME=<your username>
REGISTRY_PASSWORD=<your access token>

EOF

    exit
fi

source .image.env

REGISTRY="${REGISTRY_HOST}${REGISTRY_PATH}"

CURRENT_BRANCH=`git branch --show-current`
REMOTE_SHA=`git ls-remote --head --exit-code origin ${CURRENT_BRANCH} | cut -f 1`
LOCAL_SHA=`git rev-parse ${CURRENT_BRANCH}`
LOCAL_SHORT_SHA=`git rev-parse --short=8 ${CURRENT_BRANCH}`

PROD_IMG="${REGISTRY}/${CURRENT_BRANCH}"
VERSION=${LOCAL_SHORT_SHA}

function info {
    git remote show origin

    cat << EOF
* local folder

Branch             : $CURRENT_BRANCH
Branch remote head : $REMOTE_SHA
Branch local  SHA  : $LOCAL_SHA

EOF

git status

}

function build {
    docker build \
        -t ${PROD_IMG}:${VERSION} .
}

function ls {
    docker images ${PROD_IMG} | sort -u
}

function rm {
    IMAGES=$( docker images ${PROD_IMG} -q | sort -u )
    docker rmi -f ${IMAGES}
}

function push {
    
    if ! git diff-index --quiet HEAD
    then
        printf "There are uncommited local changes !\n\n"
        git status
        # exit
    fi

    if [ "${LOCAL_SHA}" = "${REMOTE_SHA}" ]
    then
        printf "This is the latest image for this branch\n"
        docker tag  ${PROD_IMG}:${VERSION} ${PROD_IMG}:latest
        docker push ${PROD_IMG}:${VERSION}
        docker push ${PROD_IMG}:latest
    else
        echo "Local SHA is not origin's HEAD of this branch. Not tagging as latest."
        docker tag  ${PROD_IMG}:${VERSION}
        docker push ${PROD_IMG}:${VERSION}
    fi
}

function login {
    docker login ${REGISTRY_HOST} -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD
}

function logout {
    docker logout ${REGISTRY_HOST}
}

function help {
  printf "%s <task> [args]\n\nTasks:\n" "${0}"

  compgen -A function | grep -v "^_"  | cat -n

  printf "\nExtended help:\n  Each task has comments for general usage\n"
}

# MAIN 


# This idea is heavily inspired by: https://github.com/adriancooney/Taskfile
TIMEFORMAT=$'\nTask completed in %3lR'
CMD=shift
time "${1:-help}" $2 $3 $4 $5 $6 $7 
