{
  "nodes": [
    {
      "id": 1,
      "name": "plc_keg",
      "type": "plc",
      "enabled": true,
      "config": {
        //"ip": "*************",
        "ip": "*************",
        "rack": 0,
        "slot": 1,
        "poll_interval": 100,
        //"poll_interval": 1000,
        "dbs": [
          {
            "topic": "plc.general",
            "db": 20312,
            "length": 134,
            "poll": true
          }, // general
          {
            "topic": "plc.fillers",
            "db": 20320,
            "length": 1408,
            "poll": true
          }, // fillers
          {
            "topic": "plc.tanks",
            "db": 20321,
            "length": 220,
            "poll": true
          }, // tanks
          {
            "topic": "plc.transport",
            "db": 20311,
            "length": 1584,
            "poll": true
          } // transport
        ]
      }
    }, // PLC KEG
    {
      "id": 3,
      "name": "decoder",
      "type": "decoder",
      "enabled": true,
      "config": {
        "schemas": {
          "udt_kb_ce": {
            "counter": "s7.dint",
            "seq": {
              "msg_id": "s7.int",
              "msg_step": "s7.int",
              "msg_stat": "s7.int",
              "active_step": "s7.int",
              "val": "s7.real"
            },
            "ce_io": {
              "di_do": "s7.dword",
              "values": {
                "$array": 5,
                "$refs": "s7.real"
              }
            },
            "ce_status": {
              "Action": "s7.int",
              "Status": "s7.int"
            }
          },
          "db_general": {
            "mode": "s7.int",
            "kegtype": {
              "depal": "s7.int",
              "infeed": "s7.int",
              "fillers": "s7.int",
              "outfeed": "s7.int",
              "pal": "s7.int"
            },
            "media": {
              "p_o2": "s7.real",
              "p_co2": "s7.real",
              "t_st_steam": "s7.real",
              "p_steam": "s7.real",
              "t_steam": "s7.real",
              "beertype": "s7.int"
            },
            "di_do": "s7.dword",
            "ints": {
              "$array": 16,
              "$refs": "s7.int"
            },
            "reals": {
              "$array": 16,
              "$refs": "s7.real"
            }
          },
          "db_fillers": {
            "ce": {
              "$array": 32,
              "$refs": "udt_kb_ce"
            }
          },
          "db_tanks": {
            "ce": {
              "$array": 5,
              "$refs": "udt_kb_ce"
            }
          },
          "db_transport": {
            "ce": {
              "$array": 36,
              "$refs": "udt_kb_ce"
            }
          }
        },
        "decode": [
          {
            "in_topic": "plc.general",
            "schema": "db_general",
            "out_topic": "db.general"
          },
          {
            "in_topic": "plc.fillers",
            "schema": "db_fillers",
            "out_topic": "db.fillers"
          },
          {
            "in_topic": "plc.tanks",
            "schema": "db_tanks",
            "out_topic": "db.tanks"
          },
          {
            "in_topic": "plc.transport",
            "schema": "db_transport",
            "out_topic": "db.transport"
          }
        ]
      }
    }, // DECODER
    {
      "id": 4,
      "type": "script",
      "name": "keg_processor",
      "config": {
        "remoteDebug": true,
        "topics": [
          "db.general",
          "db.fillers",
          "db.tanks",
          "db.transport"
        ],
        "script": "Pietra/KegProcessor"
      }
    }, // SCRIPT KEG
    {
      "id": 5,
      "type": "influx",
      "name": "influx_keg",
      "config": {
        "url": "http://localhost:8086",
        "database": "pietra_vl",
        "retention": "autogen",
        "topics": [
          "influx.keg"
        ],
        "batch_size": 1000,
        "flush_interval": 1000
      }
    } // INFLUX KEG
  ]
}